{"formatVersion": "1.1", "component": {"group": "com.lyokone.location", "module": "location_release", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.11.1"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-871f65ac1bf129edb222c3293a636ff4b67534a6"}}, {"group": "com.google.android.gms", "module": "play-services-location", "version": {"requires": "21.3.0"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.9.21"}}], "files": [{"name": "location_release-1.0.aar", "url": "location_release-1.0.aar", "size": 30268, "sha512": "f68b7ee6ca3a81ff66c079a874d8106952700afabdd8ac542038cb70fe02940c9d9d86d7fd24e1b24627682ab060291c64db0da6208339e243b723a4beb706d5", "sha256": "6210f9525f7460ca7991d39d6abf071dcfee24c1efe6bfac714a9edd9d83024a", "sha1": "4ea03f1f421499803c9b9298f96f61cf7113953b", "md5": "5000a6c9cf4fd981401cab8a553495bd"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_release", "version": {"requires": "1.0.0-871f65ac1bf129edb222c3293a636ff4b67534a6"}}, {"group": "androidx.core", "module": "core-ktx", "version": {"requires": "1.13.1"}}, {"group": "com.google.android.gms", "module": "play-services-location", "version": {"requires": "21.3.0"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.9.21"}}], "files": [{"name": "location_release-1.0.aar", "url": "location_release-1.0.aar", "size": 30268, "sha512": "f68b7ee6ca3a81ff66c079a874d8106952700afabdd8ac542038cb70fe02940c9d9d86d7fd24e1b24627682ab060291c64db0da6208339e243b723a4beb706d5", "sha256": "6210f9525f7460ca7991d39d6abf071dcfee24c1efe6bfac714a9edd9d83024a", "sha1": "4ea03f1f421499803c9b9298f96f61cf7113953b", "md5": "5000a6c9cf4fd981401cab8a553495bd"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "location_release-1.0-sources.jar", "url": "location_release-1.0-sources.jar", "size": 11423, "sha512": "36978a86a531e3803a1dd2aa59fab8049e14b0f746b1f05244cf0f1247694bb47f5e8fb43f88c10f43a0032590d532b9fb5fcef092b00f2ee62ae641724fb439", "sha256": "70cc2cb6b18df6dc919519e89945994a037b54753c927d9e87bbcf4cbf5924f0", "sha1": "3b36a1a00b06759cace85df7b9fb1c6f43c1e5b8", "md5": "96b8981bff025e0c5bccb3226914a079"}]}, {"name": "releaseVariantReleaseJavaDocPublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "location_release-1.0-javadoc.jar", "url": "location_release-1.0-javadoc.jar", "size": 319979, "sha512": "b343bfd1329072a33d309e6a99c1870be046748c4a7fd0c1f35560f0e921733814f6ed8408ae052f7f8e103e8cbe66f97859ea8a0a7b3d9228815a96cb851d6d", "sha256": "c06a486c0951ba67ae50ecfb1754b03f4de4746e998730b3a8b3088f5a85d051", "sha1": "7972eced79bb689d5f596a893f418ee0b0815561", "md5": "08116f737cabe7445a7bad095f21360f"}]}]}