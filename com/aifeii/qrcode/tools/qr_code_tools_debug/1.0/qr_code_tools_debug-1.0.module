{"formatVersion": "1.1", "component": {"group": "com.aifeii.qrcode.tools", "module": "qr_code_tools_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.11.1"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-871f65ac1bf129edb222c3293a636ff4b67534a6"}}], "files": [{"name": "qr_code_tools_debug-1.0.aar", "url": "qr_code_tools_debug-1.0.aar", "size": 4024, "sha512": "6e95ee21db0cee31d0cc6ced90ce80afc68a82edaa0db8b994c3adf6f8a7bb487812679c80d4b20eefdbe52f4196c5d6002af07c2b2269c192ec81db55026b8b", "sha256": "8253e5115401b2a5f76ba7ef67394cb4d8f039fcbaf1239fb600b7f70e6d52e6", "sha1": "7b7e149392ddc4c10e1f5678050d28a20aedf7a8", "md5": "4b43e80edec9541884d102b5441ad7bd"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-871f65ac1bf129edb222c3293a636ff4b67534a6"}}, {"group": "com.google.zxing", "module": "core", "version": {"requires": "3.3.0"}}, {"group": "com.google.zxing", "module": "javase", "version": {"requires": "3.3.0"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.7.10"}}], "files": [{"name": "qr_code_tools_debug-1.0.aar", "url": "qr_code_tools_debug-1.0.aar", "size": 4024, "sha512": "6e95ee21db0cee31d0cc6ced90ce80afc68a82edaa0db8b994c3adf6f8a7bb487812679c80d4b20eefdbe52f4196c5d6002af07c2b2269c192ec81db55026b8b", "sha256": "8253e5115401b2a5f76ba7ef67394cb4d8f039fcbaf1239fb600b7f70e6d52e6", "sha1": "7b7e149392ddc4c10e1f5678050d28a20aedf7a8", "md5": "4b43e80edec9541884d102b5441ad7bd"}]}, {"name": "debugVariantDebugSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "qr_code_tools_debug-1.0-sources.jar", "url": "qr_code_tools_debug-1.0-sources.jar", "size": 1874, "sha512": "e280362c067c28a5db20d7fa99ad2ecd6762370b5a386df9f6f0d2dcf6289d519a974a41a061b9f6fb489d4313d25a85648b9dd2e89a715fae4695429612d9bb", "sha256": "6bd098a4930c13e095a769dec6bd903c030f466d93eea561d9884432d45c9188", "sha1": "be0fc595c4a913b3cdf87ac7b046bc40ce843405", "md5": "2ccdb5ce10ce8723c82cf9a6260aa53a"}]}, {"name": "debugVariantDebugJavaDocPublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "qr_code_tools_debug-1.0-javadoc.jar", "url": "qr_code_tools_debug-1.0-javadoc.jar", "size": 262377, "sha512": "f1c8d4bfaddb222c8457d2b10bd03be35cce07033b7a8cc983a10c8d544ad47533e6d9616c771ae3e0cd23655e0a4652f5cb53f1dac0f57e08489036eb5c37a9", "sha256": "4a4742eb5db37a17b244df920290eb842b642a3e2dc941c03e041beeded410fe", "sha1": "6c650628936f95ca5dd278ed31dcebc07ea31538", "md5": "717d9c2bbc9c026095161dfbb6bbc0ef"}]}]}