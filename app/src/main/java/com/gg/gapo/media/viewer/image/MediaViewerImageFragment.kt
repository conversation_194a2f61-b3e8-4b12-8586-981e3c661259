package com.gg.gapo.media.viewer.image

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.databinding.MediaViewerImageFragmentBinding
import com.gg.gapo.media.viewer.MediaViewerActivity
import com.github.penfeizhou.animation.glide.AnimationDecoderOption

/**
 * <AUTHOR>
 * @since 23/12/2022
 * Move từ Common
 */
internal class MediaViewerImageFragment : Fragment() {

    private var binding: MediaViewerImageFragmentBinding? = null

    private var src: String? = null
    private var thumb: String? = null

    private lateinit var glideRequests: GlideRequests

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            src = it.getString(ARG_VIEWER_MEDIA_SRC)
            thumb = it.getString(ARG_VIEWER_MEDIA_THUMB)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = MediaViewerImageFragmentBinding.inflate(inflater, container, false)
        binding?.imagePhoto?.updateLayoutParams<FrameLayout.LayoutParams> {
            height = requireActivity().findViewById<View>(android.R.id.content).height
        }
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.imagePhoto?.apply {
            scaleType = ImageView.ScaleType.FIT_CENTER
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        }
        glideRequests = GapoGlide.with(this)
        init()
    }

    override fun onPause() {
        binding?.imagePhoto?.scale = 1F

        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun init() {
        val mediaSrc = src ?: return
        val isShowProgress =
            mediaSrc == thumb // nếu thumb là origin thì show progress vì sẽ phải đợi lâu
        binding?.apply {
            progressBar.isVisible = isShowProgress
            val thumbRequest = glideRequests.load(thumb)
                .fitCenter()
                .priority(Priority.IMMEDIATE)
                .diskCacheStrategy(DiskCacheStrategy.ALL)

            glideRequests
                .load(mediaSrc)
                .thumbnail(thumbRequest)
                .fitCenter()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .priority(Priority.IMMEDIATE)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        binding?.progressBar?.hide()
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        binding?.progressBar?.hide()
                        return false
                    }
                })
                .set(AnimationDecoderOption.DISABLE_ANIMATION_GIF_DECODER, true)
                .into(imagePhoto)

            imagePhoto.setOnClickListener {
                activity?.let {
                    val isToolbarVisible = (it as MediaViewerActivity).isToolbarVisible()
                    it.setToolbarVisible(if (isToolbarVisible) View.GONE else View.VISIBLE)
                }
            }

            imagePhoto.setOnMatrixChangeListener {
                activity?.let {
                    val isEnabled = binding?.imagePhoto?.scale == 1F
                    (it as MediaViewerActivity).setDragEnabled(isEnabled)
                }
            }
        }
    }

    companion object {
        @JvmStatic
        fun newInstance(src: String, thumb: String) =
            MediaViewerImageFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_VIEWER_MEDIA_SRC, src)
                    putString(ARG_VIEWER_MEDIA_THUMB, thumb)
                }
            }

        private const val ARG_VIEWER_MEDIA_SRC = "src"
        private const val ARG_VIEWER_MEDIA_THUMB = "thumb"
    }
}
