[versions]
versionCode = "367"
versionName = "5.7.1"
compileSdk = "35"
minSdk = "21"
targetSdk = "35"
buildTools = "35.0.0"
agp = "8.7.3"
kotlin = "1.9.24"
coroutines = "1.7.3"
realm = "10.18.0"
room = "2.5.1"
androidx-navigation = "2.8.5"
androidx-lifecycle = "2.8.7"
androidx-work = "2.10.0"
androidx-emoji = "1.2.0-alpha03"
androidx-emoji2 = "1.4.0"
androidx-datastore = "1.0.0"
koin = "3.3.3"
retrofit = "2.11.0"
okhttp = "4.11.0"
deeplinkdispatch = "6.2.2"
glide = "4.16.0"
exoplayer = "2.17.1"
epoxy = "5.1.1"
scarlet = "0.2.4"
moshi = "1.14.0"
markwon = "4.6.2"
kohii = "1.4.0.2017001"
fps = "2.1.1"
kotlinCompiler = "1.5.14"

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
android-test = { id = "com.android.test", version.ref = "agp" }
androidx-navigation = { id = "androidx.navigation.safeargs.kotlin", version.ref = "androidx-navigation" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
ktlint = "org.jlleitschuh.gradle.ktlint:11.4.0"
ksp = "com.google.devtools.ksp:1.9.24-1.0.20"
gms-google-services = "com.google.gms.google-services:4.4.2"
firebase-perf = "com.google.firebase.firebase-perf:1.4.2"
firebase-crashlytics = "com.google.firebase.crashlytics:3.0.2"
sentry = "io.sentry.android.gradle:5.3.0"
maven-version = "com.github.ben-manes.versions:0.44.0"

[libraries]
desugarJdk = "com.android.tools:desugar_jdk_libs:2.1.5" # v1.2.2 require agp = 7.3.1

kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }

kotlin-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
kotlin-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }
kotlin-coroutines-rx2 = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-rx2", version.ref = "kotlin" }

androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
androidx-room-paging = { module = "androidx.room:room-paging", version.ref = "room" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-rxjava2 = { module = "androidx.room:room-rxjava2", version.ref = "room" }

androidx-lifecycle-viewmodel = { module = "androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-common-java8 = { module = "androidx.lifecycle:lifecycle-common-java8", version.ref = "androidx-lifecycle" }
androidx-lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "androidx-lifecycle" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidx-lifecycle" }

androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "androidx-navigation" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "androidx-navigation" }

androidx-work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "androidx-work" }
androidx-work-rxjava2 = { module = "androidx.work:work-rxjava2", version.ref = "androidx-work" }

androidx-emoji = { module = "androidx.emoji:emoji", version.ref = "androidx-emoji" }
androidx-emoji-bundled = { module = "androidx.emoji:emoji-bundled", version.ref = "androidx-emoji" }

androidx-emoji2 = { module = "androidx.emoji2:emoji2", version.ref = "androidx-emoji2" }
androidx-emoji2-bundled = { module = "androidx.emoji2:emoji2-bundled", version.ref = "androidx-emoji2" }
androidx-emoji2-views = { module = "androidx.emoji2:emoji2-views", version.ref = "androidx-emoji2" }
androidx-emoji2-views-helper = { module = "androidx.emoji2:emoji2-views-helper", version.ref = "androidx-emoji2" }

androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "androidx-datastore" }
androidx-datastore-preferences-core = { module = "androidx.datastore:datastore-preferences-core", version.ref = "androidx-datastore" }

androidx-compose-ui = { module = "androidx.compose.ui:ui" }
androidx-compose-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-compose-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
androidx-compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-material3 = { module = "androidx.compose.material3:material3" }
androidx-compose-activity = "androidx.activity:activity-compose:1.9.1"
androidx-compose-bom = "androidx.compose:compose-bom:2024.12.01"

androidx-window = "androidx.window:window:1.3.0"
androidx-window-java = "androidx.window:window-java:1.3.0"

androidx-paging3-runtime = "androidx.paging:paging-runtime:3.3.5"
androidx-multidex = "androidx.multidex:multidex:2.0.1"
androidx-appcompat = "androidx.appcompat:appcompat:1.7.0"
androidx-activity-ktx = "androidx.activity:activity-ktx:1.9.3"
androidx-fragment-ktx = "androidx.fragment:fragment-ktx:1.8.5"
androidx-core = "androidx.core:core:1.15.0"
androidx-core-ktx = "androidx.core:core-ktx:1.15.0"
androidx-splashscreen = "androidx.core:core-splashscreen:1.0.1"
androidx-constraintlayout = "androidx.constraintlayout:constraintlayout:2.1.4"
androidx-recyclerview = "androidx.recyclerview:recyclerview:1.3.2"
androidx-viewpager = "androidx.viewpager:viewpager:1.1.0-alpha01"
androidx-viewpager2 = "androidx.viewpager2:viewpager2:1.1.0-beta02"
androidx-cardview = "androidx.cardview:cardview:1.0.0"
androidx-swiperefreshlayout = "androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-alpha01"
androidx-dynamicanimation-ktx = "androidx.dynamicanimation:dynamicanimation-ktx:1.0.0-alpha03"
androidx-localbroadcastmanager = "androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
androidx-browser = "androidx.browser:browser:1.8.0"
androidx-exifinterface = "androidx.exifinterface:exifinterface:1.3.7"
androidx-startup-runtime = "androidx.startup:startup-runtime:1.2.0-alpha02"
androidx-vectordrawable = "androidx.vectordrawable:vectordrawable:1.1.0"
androidx-annotation = "androidx.annotation:annotation:1.7.1"
androidx-documentfile = "androidx.documentfile:documentfile:1.1.0-alpha01"
androidx-security-crypto = "androidx.security:security-crypto:1.1.0-alpha06"
androidx-palette = "androidx.palette:palette:1.0.0"
androidx-profileinstaller = "androidx.profileinstaller:profileinstaller:1.3.1"

androidx-test-core = "androidx.test:core-ktx:1.5.0"
androidx-test-junit = "androidx.test.ext:junit-ktx:1.1.5"
androidx-test-rules = "androidx.test:rules:1.5.0"
androidx-test-runner = "androidx.test:runner:1.5.2"
androidx-benchmark-macro = "androidx.benchmark:benchmark-macro-junit4:1.2.2"
androidx-uiautomator = "androidx.test.uiautomator:uiautomator:2.2.0"

junit = "junit:junit:4.13.2"
mockito = "org.mockito:mockito-core:5.12.0"
robolectric = "org.robolectric:robolectric:4.13"

google-android-material = "com.google.android.material:material:1.12.0"

gapo-flutter-debug = "vn.gapowork.android:flutter_debug:1.0"
gapo-flutter-release = "vn.gapowork.android:flutter_release:1.0"
gapo-treeview = "vn.gapowork.android:tree-view:1.0.0-alpha01"

realm-gp = { module = "io.realm:realm-gradle-plugin", version.ref = "realm" }
realm-kotlin-extensions = { module = "io.realm:realm-android-kotlin-extensions", version.ref = "realm" }

koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
koin-androidx-workmanager = { module = "io.insert-koin:koin-androidx-workmanager", version.ref = "koin" }
koin-androidx-navigation = { module = "io.insert-koin:koin-androidx-navigation", version.ref = "koin" }

retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-adapter-rxjava2 = { module = "com.squareup.retrofit2:adapter-rxjava2", version.ref = "retrofit" }
retrofit-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
retrofit-converter-moshi = { module = "com.squareup.retrofit2:converter-moshi", version.ref = "retrofit" }

okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-tls = { module = "com.squareup.okhttp3:okhttp-tls", version.ref = "okhttp" }
okhttp-logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
okhttp-mockwebserver = { module = "com.squareup.okhttp:mockwebserver", version.ref = "okhttp" }

deeplinkdispatch = { module = "com.airbnb:deeplinkdispatch", version.ref = "deeplinkdispatch" }
deeplinkdispatch-processor = { module = "com.airbnb:deeplinkdispatch-processor", version.ref = "deeplinkdispatch" }

glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-annotations = { module = "com.github.bumptech.glide:annotations", version.ref = "glide" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
glide-okhttp3 = { module = "com.github.bumptech.glide:okhttp3-integration", version.ref = "glide" }
glide-transformations = "jp.wasabeef:glide-transformations:4.3.0"
glide-animation = "com.github.penfeizhou.android.animation:glide-plugin:2.24.0"
glide-webp = "com.github.penfeizhou.android.animation:glide-plugin:2.24.0"
glide-compose = "com.github.bumptech.glide:compose:1.0.0-beta01"

firebase-bom = "com.google.firebase:firebase-bom:33.1.2"
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics-ktx" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics-ktx" }
firebase-config = { module = "com.google.firebase:firebase-config-ktx" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging-ktx" }
firebase-perf = { module = "com.google.firebase:firebase-perf-ktx" }
firebase-inappmessaging-display = { module = "com.google.firebase:firebase-inappmessaging-display-ktx" }

gms-playServices-base = "com.google.android.gms:play-services-base:18.5.0"
gms-playServices-location = "com.google.android.gms:play-services-location:21.3.0"
gms-playServices-auth = "com.google.android.gms:play-services-auth:21.2.0"
gms-playServices-auth-apiPhone = "com.google.android.gms:play-services-auth-api-phone:18.1.0"

reactivex-rx2Java = "io.reactivex.rxjava2:rxjava:2.2.21"
reactivex-rx2Android = "io.reactivex.rxjava2:rxandroid:2.1.1"
reactivex-rx2Kotlin = "io.reactivex.rxjava2:rxkotlin:2.4.0"
reactivex-rx3Binding = "com.jakewharton.rxbinding3:rxbinding:3.1.0"
reactivex-rx3Java = "io.reactivex.rxjava3:rxjava:3.1.5"

exoplayer = { module = "com.google.android.exoplayer:exoplayer", version.ref = "exoplayer" }
exoplayer-core = { module = "com.google.android.exoplayer:exoplayer-core", version.ref = "exoplayer" }
exoplayer-ui = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayer" }
exoplayer-hls = { module = "com.google.android.exoplayer:exoplayer-hls", version.ref = "exoplayer" }
exoplayer-okhttp = { module = "com.google.android.exoplayer:extension-okhttp", version.ref = "exoplayer" }

epoxy = { module = "com.airbnb.android:epoxy", version.ref = "epoxy" }
epoxy-processor = { module = "com.airbnb.android:epoxy-processor", version.ref = "epoxy" }
epoxy-databinding = { module = "com.airbnb.android:epoxy-databinding", version.ref = "epoxy" }
epoxy-paging3 = { module = "com.airbnb.android:epoxy-paging3", version.ref = "epoxy" }

tinder-scarlet = { module = "com.github.Tinder.Scarlet:scarlet", version.ref = "scarlet" }
tinder-scarlet-protocol-webSocketOkHttp = { module = "com.github.Tinder.Scarlet:scarlet-protocol-websocket-okhttp", version.ref = "scarlet" }
tinder-scarlet-adapter-rx2 = { module = "com.github.Tinder.Scarlet:scarlet-stream-adapter-rxjava2", version.ref = "scarlet" }
tinder-scarlet-message-gson = { module = "com.github.Tinder.Scarlet:scarlet-message-adapter-gson", version.ref = "scarlet" }
tinder-statemachine = "com.tinder.statemachine:statemachine:0.2.0"

moshi-kotlin = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshi" }
moshi-adapters = { module = "com.squareup.moshi:moshi-adapters", version.ref = "moshi" }
moshi-kotlin-codegen = { module = "com.squareup.moshi:moshi-kotlin-codegen", version.ref = "moshi" }

markwon-core = { module = "io.noties.markwon:core", version.ref = "markwon" }
markwon-linkify = { module = "io.noties.markwon:linkify", version.ref = "markwon" }
markwon-ext-strikeThrough = { module = "io.noties.markwon:ext-strikethrough", version.ref = "markwon" }
markwon-ext-tables = { module = "io.noties.markwon:ext-tables", version.ref = "markwon" }
markwon-ext-taskList = { module = "io.noties.markwon:ext-tasklist", version.ref = "markwon" }
markwon-inline-parser = { module = "io.noties.markwon:inline-parser", version.ref = "markwon" }

kohii-core = { module = "im.ene.kohii:kohii-core", version.ref = "kohii" }
kohii-exoplayer = { module = "im.ene.kohii:kohii-exoplayer", version.ref = "kohii" }

fps-debug = { module = "jp.wasabeef:takt", version.ref = "fps" }
fps-release = { module = "jp.wasabeef:takt-no-op", version.ref = "fps" }

microsoft-msal = "com.microsoft.identity.client:msal:4.2.0"

autoDimens = "com.github.hantrungkien:AutoDimension:1.0.9"

eventbus = "org.greenrobot:eventbus:3.3.1"

mqtt = "com.github.hannesa2:paho.mqtt.android:4.3.beta1"

sentry = "io.sentry:sentry-android:7.22.1"

emoji-google = "com.vanniktech:emoji-google:0.15.0"
emoji-google-compat = "com.vanniktech:emoji-google-compat:0.15.0"

leakcanary = "com.squareup.leakcanary:leakcanary-android:2.10"

gson = "com.google.code.gson:gson:2.11.0"

circleimageview = "de.hdodenhof:circleimageview:3.1.0"

sheetmenu = "com.github.chihung93:sheetmenu:2.0.4"

ucrop = "com.github.chihung93:uCrop:2.2.5"

swipelayout = "com.daimajia.swipelayout:library:1.2.0"

spinKit = "com.github.ybq:Android-SpinKit:1.4.0"

timber = "com.jakewharton.timber:timber:5.0.1"

guava = "com.google.guava:guava:28.0-android"

flexbox = "com.google.android.flexbox:flexbox:3.0.0"

materialprogressbar = "me.zhanghai.android.materialprogressbar:library:1.6.1"

joda-time = "joda-time:joda-time:2.12.2"

imagecropview = "com.naver.android.helloyako:imagecropview:1.2.4"

compressor = "id.zelory:compressor:3.0.1"

customtabshelper = "me.zhanghai.android.customtabshelper:library:1.0.6"

elasticviews = "com.github.skydoves:elasticviews:2.1.0"

materialPopupMenu = "com.github.zawadz88:MaterialPopupMenu:4.1.0"

photoView = "com.github.chrisbanes:PhotoView:2.3.0"

hauler = "app.futured.hauler:hauler:4.0.0"

webrtc = "io.github.webrtc-sdk:android:125.6422.04"

balloon = "com.github.skydoves:balloon:1.5.2"

gpuimage = "jp.co.cyberagent.android:gpuimage:2.1.0"

roundedimageview = "com.makeramen:roundedimageview:2.3.0"

dragToClose = "com.github.davidmigloz:drag-to-close:2.0.0"

progressbutton = "com.github.razir.progressbutton:progressbutton:2.1.0"

swipeActionView = "com.github.chihung93:SwipeActionView:1.3.2"

keyboardvisibilityevent = "net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3"

zxing-android-embedded = "com.journeyapps:zxing-android-embedded:4.3.0"

zxing-core = "com.google.zxing:core:3.5.1"

google-barcode-scanning = "com.google.mlkit:barcode-scanning:17.3.0"

gestureViews = "com.alexvasilkov:gesture-views:2.8.3"

imagezoom = "it.sephiroth.android.library.imagezoom:library:1.0.4"

androidEasing = "it.sephiroth.android.library.easing:android-easing:1.0.3"

utilcodex = "com.blankj:utilcodex:1.31.1"

xfetch2 = "com.github.Akaizz.Fetch:fetch2:v3.2"

axrLottie = "com.github.hantrungkien:AXrLottie:1.4.0"
lottie = "com.airbnb.android:lottie:4.0.0"

autocomplete = "com.github.natario1:Autocomplete:v1.1.0"

libphonenumber = "com.googlecode.libphonenumber:libphonenumber:8.13.7"

spyglass = "com.linkedin.android.spyglass:spyglass:3.0.1"

spotlight = "com.github.takusemba:spotlight:2.0.5"

stfalconimageviewer = "com.github.stfalcon-studio:stfalconimageviewer:v1.0.1"

toasty = "com.github.GrenderG:Toasty:1.5.2"

audioRecordView = "com.github.Armen101:AudioRecordView:1.0.5"

simpleCommons = "com.github.SimpleMobileTools:Simple-Commons:5422908"

jwtdecode = "com.auth0.android:jwtdecode:2.0.2"

rtmpRtspStreamClient = "com.github.pedroSG94.rtmp-rtsp-stream-client-java:rtplibrary:2.2.2"

tink = "com.google.crypto.tink:tink-android:1.8.0"

slidableActivity = "com.r0adkll:slidableactivity:2.1.0"

newCalendarView = "com.kizitonwose.calendar:view:2.1.1"

evalExpression = "com.ezylang:EvalEx:3.1.0"

signaturePad = "com.github.gcacace:signature-pad:1.3.1"

dotsIndicator = "com.tbuonomo:dotsindicator:4.3"

livekit = "io.livekit:livekit-android:2.16.0"

livekitKrisp = "io.livekit:krisp-noise-filter:0.0.5"

groupie = "com.github.lisawray.groupie:groupie:2.9.0"

groupie-viewbinding = "com.github.lisawray.groupie:groupie-viewbinding:2.9.0"

rtlDuolingoViewpager = "com.github.duolingo:rtl-viewpager:v2.0.0"
patternLockview = "com.github.aritraroy.PatternLockView:patternlockview:b69afae9c1"

recaptcha = "com.google.android.recaptcha:recaptcha:18.6.0-beta02"

[bundles]
android-coroutines = ["kotlin-coroutines-core", "kotlin-coroutines-android"]
