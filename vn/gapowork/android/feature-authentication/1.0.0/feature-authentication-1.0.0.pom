<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>vn.gapowork.android</groupId>
  <artifactId>feature-authentication</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>
  <dependencies>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.24</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.7.3</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-android</artifactId>
      <version>1.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.appcompat</groupId>
      <artifactId>appcompat</artifactId>
      <version>1.7.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.fragment</groupId>
      <artifactId>fragment-ktx</artifactId>
      <version>1.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.core</groupId>
      <artifactId>core-ktx</artifactId>
      <version>1.15.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.constraintlayout</groupId>
      <artifactId>constraintlayout</artifactId>
      <version>2.1.4</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.material</groupId>
      <artifactId>material</artifactId>
      <version>1.12.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.work</groupId>
      <artifactId>work-runtime-ktx</artifactId>
      <version>2.10.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.navigation</groupId>
      <artifactId>navigation-ui-ktx</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.navigation</groupId>
      <artifactId>navigation-fragment-ktx</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-viewmodel-ktx</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-livedata-ktx</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-common-java8</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.gms</groupId>
      <artifactId>play-services-auth</artifactId>
      <version>21.2.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>retrofit</artifactId>
      <version>2.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.microsoft.identity.client</groupId>
      <artifactId>msal</artifactId>
      <version>4.2.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>glide</artifactId>
      <version>4.16.0</version>
    </dependency>
    <dependency>
      <groupId>io.insert-koin</groupId>
      <artifactId>koin-android</artifactId>
      <version>3.3.3</version>
    </dependency>
    <dependency>
      <groupId>com.github.hantrungkien</groupId>
      <artifactId>AutoDimension</artifactId>
      <version>1.0.9</version>
    </dependency>
    <dependency>
      <groupId>com.airbnb</groupId>
      <artifactId>deeplinkdispatch</artifactId>
      <version>6.2.2</version>
    </dependency>
    <dependency>
      <groupId>com.google.firebase</groupId>
      <artifactId>firebase-messaging-ktx</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>org.greenrobot</groupId>
      <artifactId>eventbus</artifactId>
      <version>3.3.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.jakewharton.timber</groupId>
      <artifactId>timber</artifactId>
      <version>5.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.auth0.android</groupId>
      <artifactId>jwtdecode</artifactId>
      <version>2.0.2</version>
    </dependency>
    <dependency>
      <groupId>com.googlecode.libphonenumber</groupId>
      <artifactId>libphonenumber</artifactId>
      <version>8.13.7</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.gms</groupId>
      <artifactId>play-services-auth-api-phone</artifactId>
      <version>18.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.recaptcha</groupId>
      <artifactId>recaptcha</artifactId>
      <version>18.6.0-beta02</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>viewbinding</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>viewbinding</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.firebase</groupId>
        <artifactId>firebase-bom</artifactId>
        <version>33.1.2</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
