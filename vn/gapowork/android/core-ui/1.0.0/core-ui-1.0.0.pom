<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>vn.gapowork.android</groupId>
  <artifactId>core-ui</artifactId>
  <version>1.0.0</version>
  <packaging>aar</packaging>
  <dependencies>
    <dependency>
      <groupId>androidx.core</groupId>
      <artifactId>core-ktx</artifactId>
      <version>1.15.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.material</groupId>
      <artifactId>material</artifactId>
      <version>1.12.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.activity</groupId>
      <artifactId>activity-ktx</artifactId>
      <version>1.9.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.fragment</groupId>
      <artifactId>fragment-ktx</artifactId>
      <version>1.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.swiperefreshlayout</groupId>
      <artifactId>swiperefreshlayout</artifactId>
      <version>1.2.0-alpha01</version>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-graphics</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-tooling-preview</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material3</groupId>
      <artifactId>material3</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.emoji2</groupId>
      <artifactId>emoji2-views</artifactId>
      <version>1.4.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-common-java8</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>com.github.hantrungkien</groupId>
      <artifactId>AutoDimension</artifactId>
      <version>1.0.9</version>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>glide</artifactId>
      <version>4.16.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.11.0</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>core</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-strikethrough</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-tables</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-tasklist</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>exoplayer-ui</artifactId>
      <version>2.17.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.razir.progressbutton</groupId>
      <artifactId>progressbutton</artifactId>
      <version>2.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.jakewharton.timber</groupId>
      <artifactId>timber</artifactId>
      <version>5.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.GrenderG</groupId>
      <artifactId>Toasty</artifactId>
      <version>1.5.2</version>
    </dependency>
    <dependency>
      <groupId>io.insert-koin</groupId>
      <artifactId>koin-android</artifactId>
      <version>3.3.3</version>
    </dependency>
    <dependency>
      <groupId>com.github.skydoves</groupId>
      <artifactId>balloon</artifactId>
      <version>1.5.2</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.9.24</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.9.24</version>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.compose</groupId>
        <artifactId>compose-bom</artifactId>
        <version>2024.12.01</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
