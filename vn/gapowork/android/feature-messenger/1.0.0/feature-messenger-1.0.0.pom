<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>vn.gapowork.android</groupId>
  <artifactId>feature-messenger</artifactId>
  <version>1.0.0</version>
  <packaging>pom</packaging>
  <dependencies>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.24</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.7.3</version>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-android</artifactId>
      <version>1.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.appcompat</groupId>
      <artifactId>appcompat</artifactId>
      <version>1.7.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.core</groupId>
      <artifactId>core-ktx</artifactId>
      <version>1.15.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.material</groupId>
      <artifactId>material</artifactId>
      <version>1.12.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.activity</groupId>
      <artifactId>activity-ktx</artifactId>
      <version>1.9.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.fragment</groupId>
      <artifactId>fragment-ktx</artifactId>
      <version>1.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.swiperefreshlayout</groupId>
      <artifactId>swiperefreshlayout</artifactId>
      <version>1.2.0-alpha01</version>
    </dependency>
    <dependency>
      <groupId>androidx.recyclerview</groupId>
      <artifactId>recyclerview</artifactId>
      <version>1.3.2</version>
    </dependency>
    <dependency>
      <groupId>androidx.constraintlayout</groupId>
      <artifactId>constraintlayout</artifactId>
      <version>2.1.4</version>
    </dependency>
    <dependency>
      <groupId>androidx.browser</groupId>
      <artifactId>browser</artifactId>
      <version>1.8.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.exifinterface</groupId>
      <artifactId>exifinterface</artifactId>
      <version>1.3.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.localbroadcastmanager</groupId>
      <artifactId>localbroadcastmanager</artifactId>
      <version>1.1.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.paging</groupId>
      <artifactId>paging-runtime</artifactId>
      <version>3.3.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.emoji2</groupId>
      <artifactId>emoji2-views</artifactId>
      <version>1.4.0</version>
    </dependency>
    <dependency>
      <groupId>com.vanniktech</groupId>
      <artifactId>emoji-google</artifactId>
      <version>0.15.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-viewmodel-ktx</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-livedata-ktx</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-common-java8</artifactId>
      <version>2.8.7</version>
    </dependency>
    <dependency>
      <groupId>androidx.navigation</groupId>
      <artifactId>navigation-ui-ktx</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.navigation</groupId>
      <artifactId>navigation-fragment-ktx</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>androidx.work</groupId>
      <artifactId>work-runtime-ktx</artifactId>
      <version>2.10.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.work</groupId>
      <artifactId>work-rxjava2</artifactId>
      <version>2.10.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.room</groupId>
      <artifactId>room-runtime</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>androidx.room</groupId>
      <artifactId>room-ktx</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>androidx.room</groupId>
      <artifactId>room-rxjava2</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-android-kotlin-extensions</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.firebase</groupId>
      <artifactId>firebase-crashlytics-ktx</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxjava</artifactId>
      <version>2.2.21</version>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxkotlin</artifactId>
      <version>2.4.0</version>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxandroid</artifactId>
      <version>2.1.1</version>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava3</groupId>
      <artifactId>rxjava</artifactId>
      <version>3.1.5</version>
    </dependency>
    <dependency>
      <groupId>io.insert-koin</groupId>
      <artifactId>koin-android</artifactId>
      <version>3.3.3</version>
    </dependency>
    <dependency>
      <groupId>io.insert-koin</groupId>
      <artifactId>koin-androidx-workmanager</artifactId>
      <version>3.3.3</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>retrofit</artifactId>
      <version>2.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.airbnb.android</groupId>
      <artifactId>epoxy</artifactId>
      <version>5.1.1</version>
    </dependency>
    <dependency>
      <groupId>com.airbnb.android</groupId>
      <artifactId>epoxy-databinding</artifactId>
      <version>5.1.1</version>
    </dependency>
    <dependency>
      <groupId>org.greenrobot</groupId>
      <artifactId>eventbus</artifactId>
      <version>3.3.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>glide</artifactId>
      <version>4.16.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.penfeizhou.android.animation</groupId>
      <artifactId>glide-plugin</artifactId>
      <version>2.24.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.bumptech.glide</groupId>
      <artifactId>compose</artifactId>
      <version>1.0.0-beta01</version>
    </dependency>
    <dependency>
      <groupId>com.airbnb</groupId>
      <artifactId>deeplinkdispatch</artifactId>
      <version>6.2.2</version>
    </dependency>
    <dependency>
      <groupId>io.sentry</groupId>
      <artifactId>sentry-android</artifactId>
      <version>7.22.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.hantrungkien</groupId>
      <artifactId>AutoDimension</artifactId>
      <version>1.0.9</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>exoplayer-core</artifactId>
      <version>2.17.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>exoplayer-ui</artifactId>
      <version>2.17.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>exoplayer-hls</artifactId>
      <version>2.17.1</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>core</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-strikethrough</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-tables</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>ext-tasklist</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>linkify</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>io.noties.markwon</groupId>
      <artifactId>inline-parser</artifactId>
      <version>4.6.2</version>
    </dependency>
    <dependency>
      <groupId>de.hdodenhof</groupId>
      <artifactId>circleimageview</artifactId>
      <version>3.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.chihung93</groupId>
      <artifactId>sheetmenu</artifactId>
      <version>2.0.4</version>
    </dependency>
    <dependency>
      <groupId>com.jakewharton.timber</groupId>
      <artifactId>timber</artifactId>
      <version>5.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.ybq</groupId>
      <artifactId>Android-SpinKit</artifactId>
      <version>1.4.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.android.flexbox</groupId>
      <artifactId>flexbox</artifactId>
      <version>3.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.skydoves</groupId>
      <artifactId>elasticviews</artifactId>
      <version>2.1.0</version>
    </dependency>
    <dependency>
      <groupId>me.zhanghai.android.materialprogressbar</groupId>
      <artifactId>library</artifactId>
      <version>1.6.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.chihung93</groupId>
      <artifactId>SwipeActionView</artifactId>
      <version>1.3.2</version>
    </dependency>
    <dependency>
      <groupId>app.futured.hauler</groupId>
      <artifactId>hauler</artifactId>
      <version>4.0.0</version>
    </dependency>
    <dependency>
      <groupId>net.yslibrary.keyboardvisibilityevent</groupId>
      <artifactId>keyboardvisibilityevent</artifactId>
      <version>3.0.0-RC3</version>
    </dependency>
    <dependency>
      <groupId>com.github.chihung93</groupId>
      <artifactId>uCrop</artifactId>
      <version>2.2.5</version>
    </dependency>
    <dependency>
      <groupId>com.daimajia.swipelayout</groupId>
      <artifactId>library</artifactId>
      <version>1.2.0</version>
    </dependency>
    <dependency>
      <groupId>com.blankj</groupId>
      <artifactId>utilcodex</artifactId>
      <version>1.31.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.zawadz88</groupId>
      <artifactId>MaterialPopupMenu</artifactId>
      <version>4.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.hantrungkien</groupId>
      <artifactId>AXrLottie</artifactId>
      <version>1.4.0</version>
    </dependency>
    <dependency>
      <groupId>me.zhanghai.android.customtabshelper</groupId>
      <artifactId>library</artifactId>
      <version>1.0.6</version>
    </dependency>
    <dependency>
      <groupId>com.github.natario1</groupId>
      <artifactId>Autocomplete</artifactId>
      <version>v1.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.googlecode.libphonenumber</groupId>
      <artifactId>libphonenumber</artifactId>
      <version>8.13.7</version>
    </dependency>
    <dependency>
      <groupId>com.linkedin.android.spyglass</groupId>
      <artifactId>spyglass</artifactId>
      <version>3.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.takusemba</groupId>
      <artifactId>spotlight</artifactId>
      <version>2.0.5</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.12.2</version>
    </dependency>
    <dependency>
      <groupId>id.zelory</groupId>
      <artifactId>compressor</artifactId>
      <version>3.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.Armen101</groupId>
      <artifactId>AudioRecordView</artifactId>
      <version>1.0.5</version>
    </dependency>
    <dependency>
      <groupId>com.github.SimpleMobileTools</groupId>
      <artifactId>Simple-Commons</artifactId>
      <version>5422908</version>
    </dependency>
    <dependency>
      <groupId>com.github.aritraroy.PatternLockView</groupId>
      <artifactId>patternlockview</artifactId>
      <version>b69afae9c1</version>
    </dependency>
    <dependency>
      <groupId>com.github.duolingo</groupId>
      <artifactId>rtl-viewpager</artifactId>
      <version>v2.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.skydoves</groupId>
      <artifactId>balloon</artifactId>
      <version>1.5.2</version>
    </dependency>
    <dependency>
      <groupId>com.r0adkll</groupId>
      <artifactId>slidableactivity</artifactId>
      <version>2.1.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.activity</groupId>
      <artifactId>activity-compose</artifactId>
      <version>1.9.1</version>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-graphics</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-tooling-preview</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material3</groupId>
      <artifactId>material3</artifactId>
      <version/>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-annotations</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>viewbinding</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-android-library</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-android-kotlin-extensions</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-annotations</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>viewbinding</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-runtime</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-adapters</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-ktx</artifactId>
      <version>8.7.3</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-android-library</artifactId>
      <version>10.18.0</version>
    </dependency>
    <dependency>
      <groupId>io.realm</groupId>
      <artifactId>realm-android-kotlin-extensions</artifactId>
      <version>10.18.0</version>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.firebase</groupId>
        <artifactId>firebase-bom</artifactId>
        <version>33.1.2</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>androidx.compose</groupId>
        <artifactId>compose-bom</artifactId>
        <version>2024.12.01</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
