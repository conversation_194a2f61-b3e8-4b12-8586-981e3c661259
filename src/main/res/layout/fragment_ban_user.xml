<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/_10dp"
            android:fontFamily="@font/font_roboto_bold"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/workspace_option_disable_account"
            android:textColor="@color/negativePrimary"
            android:textSize="@dimen/_16sp" />

        <View
            android:id="@+id/lineTitle"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/_16dp"
            android:background="#E6E6E6" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ic_ban"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_24dp"
            android:layout_marginTop="@dimen/_28dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:tint="@color/negativePrimary"
            app:srcCompat="@drawable/ic64_line15_person_banmark" />

        <TextView
            android:id="@+id/tv_ask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:fontFamily="@font/font_roboto_regular"
            android:textStyle="bold"
            android:text="@string/workspace_dialog_ban_account_title"
            android:textColor="@color/contentPrimary"
            android:textSize="@dimen/_16sp" />

        <com.gg.gapo.feature.workspace.presentation.member.common.view.RedDotTextView
            android:id="@+id/tv_content_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            app:textContent="@string/workspace_dialog_ban_description_1" />

        <com.gg.gapo.feature.workspace.presentation.member.common.view.RedDotTextView
            android:id="@+id/tv_content_ws_same_domain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            app:textContent="@string/workspace_dialog_ban_description_2" />

        <com.gg.gapo.feature.workspace.presentation.member.common.view.RedDotTextView
            android:id="@+id/tv_content_other_domain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            app:textContent="@string/workspace_dialog_ban_description_not_same_domain" />

        <com.gg.gapo.feature.workspace.presentation.member.common.view.RedDotTextView
            android:id="@+id/tv_content_ws_other_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:visibility="gone"
            app:textContent="@string/workspace_dialog_ban_description_3" />

        <com.gg.gapo.feature.workspace.presentation.member.common.view.RedDotTextView
            android:id="@+id/tv_content_ws_other_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:visibility="gone"
            app:textContent="@string/workspace_dialog_ban_description_4" />

        <TextView
            android:id="@+id/tvUnderstood"
            style="@style/GapoButton.Large.AccentWorkPrimary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/shared_understood"
            android:textColor="@color/white"
            android:visibility="gone"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_32dp"/>

        <LinearLayout
            android:id="@+id/lnConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_32dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/workspace_bg_radius_8"
                android:backgroundTint="#F1F2F4"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="@font/font_roboto_bold"
                android:gravity="center"
                android:padding="@dimen/_12dp"
                android:text="@string/shared_cancel"
                android:textColor="@color/colorTextGray10"
                android:textSize="@dimen/_16sp" />

            <TextView
                android:id="@+id/tvConfirm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:layout_weight="1"
                android:background="@drawable/workspace_bg_radius_8"
                android:backgroundTint="@color/negativePrimary"
                android:clickable="true"
                android:focusable="true"
                android:fontFamily="@font/font_roboto_bold"
                android:gravity="center"
                android:padding="@dimen/_12dp"
                android:text="@string/common_confirm"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_16sp" />

        </LinearLayout>

    </LinearLayout>
</layout>