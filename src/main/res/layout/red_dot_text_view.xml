<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="@dimen/_6dp"
        android:layout_height="@dimen/_6dp"
        android:layout_marginTop="@dimen/_9dp"
        android:src="@drawable/workspace_red_dot" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvContent"
        style="@style/GapoTextStyle.BodyLarge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10dp"
        android:textColor="@color/contentSecondary" />
</LinearLayout>