<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgPrimary"
            android:paddingVertical="@dimen/_12dp"
            android:paddingStart="@dimen/_12dp">

            <com.gg.gapo.core.ui.image.GapoAvatarImageView
                android:id="@+id/ivAvatar"
                android:layout_width="@dimen/_64dp"
                android:layout_height="@dimen/_64dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_admin"
                android:layout_width="@dimen/_24dp"
                android:layout_height="@dimen/_24dp"
                android:src="@drawable/workspace_ic_role_admin"
                app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
                app:layout_constraintEnd_toEndOf="@id/ivAvatar" />

            <LinearLayout
                android:id="@+id/ll_contact_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/ivOption"
                app:layout_constraintStart_toEndOf="@id/ivAvatar"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_member_name"
                    style="@style/GapoTextStyle.HeadingMedium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/contentPrimary"
                    tools:text="MinhTA" />


                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/linear_department"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:tint="@color/contentSecondary"
                        android:layout_marginTop="@dimen/_4dp"
                        app:srcCompat="@drawable/ic12_fill_organizational_chart" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_department"
                        style="@style/GapoTextStyle.BodyMedium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:textColor="@color/contentSecondary"
                        tools:text="Android Dev" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/linear_role"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:tint="@color/contentSecondary"
                        android:layout_marginTop="@dimen/_4dp"
                        app:srcCompat="@drawable/ic12_fill_briefcase" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_role"
                        style="@style/GapoTextStyle.BodyMedium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:textColor="@color/contentSecondary"
                        tools:text="Android Dev" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_more_position"
                    style="@style/GapoTextStyle.BodyMedium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_18dp"
                    android:textColor="?accentWorkSecondary" />


            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/ivOption"
                android:layout_width="@dimen/_48dp"
                android:layout_height="@dimen/_48dp"
                android:background="@android:color/transparent"
                android:src="@drawable/workspace_ic_three_dots"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1dp"
            android:layout_marginStart="@dimen/_88dp"
            android:background="@drawable/workspace_list_member_divider" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>