<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="viewModel"
            type="com.gg.gapo.feature.post.presentation.post.media.horizontal.viewmodel.PostMediaDetailsHorizontalViewModel" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:fillViewport="true">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.github.chrisbanes.photoview.PhotoView
                android:id="@+id/image_photo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:onClick="@{() -> viewModel.onClickMediaImage()}" />

        </FrameLayout>

    </androidx.core.widget.NestedScrollView>
</layout>
