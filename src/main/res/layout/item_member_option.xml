<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.appcompat.widget.AppCompatTextView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/text_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:drawablePadding="@dimen/_12dp"
        android:fontFamily="@font/font_roboto_regular"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_16dp"
        android:text="@string/common_pin_post_title"
        android:textColor="@color/colorTextGray10"
        android:textSize="@dimen/_16dp"
        tools:drawableStartCompat="@drawable/ic_gray_pin" />
</layout>


