<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/bgPrimary"
            tools:context=".presentation.features.forward.ChatForwardMessagesActivity">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imvBack"
                style="@style/MessengerButtonBackStyle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                style="@style/GapoTextStyle.BodyLarge.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/messenger_forward_title"
                android:textColor="@color/contentPrimary"
                app:layout_constraintBottom_toBottomOf="@id/imvBack"
                app:layout_constraintEnd_toStartOf="@id/text_done"
                app:layout_constraintStart_toEndOf="@id/imvBack"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_done"
                style="@style/GapoTextStyle.BodyLarge.Bold"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:gravity="center"
                android:paddingEnd="@dimen/_16dp"
                android:text="@string/shared_done"
                android:textColor="?accentWorkSecondary"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/imvBack"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/_12dp"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_toEndOf="@+id/imvBack"
            android:background="@drawable/bg_search_view_border"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:theme="@style/SearchView"
            android:visibility="visible"
            app:closeIcon="@drawable/ic_messenger_close"
            app:iconifiedByDefault="false"
            app:layout_constraintBottom_toBottomOf="@id/imvBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/imvBack"
            app:layout_constraintTop_toTopOf="parent"
            app:queryBackground="@android:color/transparent"
            app:queryHint="@string/shared_search"
            app:searchIcon="@drawable/ic_search"
            tools:visibility="visible" />


        <androidx.core.widget.ContentLoadingProgressBar
            android:id="@+id/loadMoreProgress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/imvBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imvBack"
            tools:visibility="visible" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            style="@style/Messenger.TabLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48dp"
            app:tabGravity="fill"
            app:tabIndicatorAnimationMode="elastic"
            app:tabIndicatorColor="?accentWorkSecondary"
            app:tabIndicatorGravity="bottom"
            app:tabIndicatorHeight="@dimen/_2dp"
            app:tabMode="fixed"
            app:tabPaddingEnd="@dimen/_15dp"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="?accentWorkSecondary"
            app:tabTextColor="@color/contentSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imvBack"
            tools:ignore="RtlSymmetry" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab_layout" />

    </LinearLayout>

</layout>