<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgPrimary"
        android:orientation="vertical"
        tools:context=".presentation.InvitationWorkspaceActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="@dimen/_1dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/btnBack"
                    android:layout_width="@dimen/_48dp"
                    android:layout_height="@dimen/_48dp"
                    android:background="@android:color/transparent"
                    app:layout_constraintEnd_toStartOf="@+id/title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/shared_ic_arrow_left_24dp_primary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title"
                    style="@style/GapoTextStyle.HeadingLarge"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:maxLines="1"
                    android:layout_marginEnd="@dimen/_48dp"
                    android:singleLine="true"
                    android:text="@string/periodic_survey_survey_participant"
                    app:layout_constraintBottom_toBottomOf="@+id/btnBack"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btnBack"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textAllCaps="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btnBack"
                    app:tabIndicatorColor="?accentWorkSecondary"
                    app:tabIndicatorHeight="@dimen/_2dp"
                    app:tabMaxWidth="0dp"
                    app:tabMode="scrollable"
                    app:tabSelectedTextColor="?accentWorkSecondary"
                    app:tabTextAppearance="@style/SurveyTabTextAppearance2"
                    app:tabTextColor="@color/contentSecondary" />

                <View
                    android:id="@+id/line_header"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/linePrimary"
                    app:layout_constraintBottom_toBottomOf="@id/tabLayout" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/lnIncognito"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingTop="@dimen/_36dp"
            android:paddingBottom="@dimen/_36dp"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/frame_incognito_answered" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCount"
                style="@style/GapoTextStyle.HeadingMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:gravity="center"
                android:layout_marginHorizontal="@dimen/_44dp"
                tools:text="Chưa có kết quả" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/GapoTextStyle.BodyMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_6dp"
                android:gravity="center"
                android:textColor="@color/contentSecondary"
                android:layout_marginHorizontal="@dimen/_44dp"
                android:text="@string/survey_v2_popup_survey_incognito_mode_unanswer_subtitle" />

        </LinearLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>