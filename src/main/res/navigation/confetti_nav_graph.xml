<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:id="@+id/my_gift_nav_graph"
	app:startDestination="@id/managingConfetti">

	<fragment
		android:id="@+id/managingConfetti"
		android:name="com.gg.gapo.feature.livestream.presentation.confetti.manage.ManagingConfettiFragment"
		tools:layout="@layout/managing_confetti_fragment">
		<action
			android:id="@+id/action_go_to_creating_cofnetti"
			app:destination="@id/creatingConfetti"
			app:enterAnim="@anim/nav_default_pop_enter_anim"
			app:exitAnim="@anim/nav_default_pop_exit_anim" />
	</fragment>

	<fragment
		android:id="@+id/creatingConfetti"
		android:name="com.gg.gapo.feature.livestream.presentation.confetti.create.CreatingConfettiFragment"
		tools:layout="@layout/creating_confetti_fragment"></fragment>
</navigation>