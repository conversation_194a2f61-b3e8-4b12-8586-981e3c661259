package com.gg.gapo.feature.v2.presentation.order.adapter.workflow.holder

import android.content.Context
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.databinding.setLayoutMarginStart
import com.gg.gapo.feature.approval.databinding.OrderFormWorkflowCommentItemBinding
import com.gg.gapo.feature.v2.presentation.model.enum.WorkflowHierarchyEnum
import com.gg.gapo.feature.v2.presentation.model.form.workflow.WorkflowViewData

/**
 * <AUTHOR>
 * @since 10/4/23
 */
internal class WorkflowCommentViewHolder(
    val context: Context,
    val binding: OrderFormWorkflowCommentItemBinding
) : RecyclerView.ViewHolder(binding.root) {
    fun onBind(data: WorkflowViewData.Comment) = with(binding) {
        viewLine.isVisible = data.isDoneState != null
        if (data.isDoneState == true) {
            viewLine.setBackgroundColor(
                ContextCompat.getColor(
                    context,
                    GapoColors.accentWorkSecondary
                )
            )
        } else {
            viewLine.setBackgroundColor(
                ContextCompat.getColor(
                    context,
                    GapoColors.linePrimary
                )
            )
        }

        textContent.text = data.comment
        textContent.setLayoutMarginStart(
            context.resources.getDimension(WorkflowHierarchyEnum.mapToDimenId(data.hierarchyType))
        )
    }

    fun onBindPayloads(data: WorkflowViewData.Comment, payloads: List<Any>) = with(binding) {
    }
}
