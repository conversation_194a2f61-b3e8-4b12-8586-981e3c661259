package com.gg.gapo.feature.v2.presentation.order.bottomsheet.calendar

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.approval.R
import com.gg.gapo.feature.approval.databinding.OrderCalendarDayViewBinding
import com.gg.gapo.feature.approval.databinding.OrderCalendarViewBottomSheetFragmentBinding
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils.getOrDefault
import com.gg.gapo.feature.v2.presentation.utils.ApprovalUtils.setTextColorRes
import com.gg.gapo.feature.v2.presentation.utils.ConstUtils.ZERO_VALUE
import com.gg.gapo.feature.v2.presentation.utils.DateUtils
import com.gg.gapo.feature.v2.presentation.utils.DateUtils.convertToListDate
import com.kizitonwose.calendar.core.CalendarDay
import com.kizitonwose.calendar.core.DayPosition
import com.kizitonwose.calendar.core.nextMonth
import com.kizitonwose.calendar.core.previousMonth
import com.kizitonwose.calendar.core.yearMonth
import com.kizitonwose.calendar.view.MonthDayBinder
import com.kizitonwose.calendar.view.ViewContainer
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * <AUTHOR>
 * @since 1/13/23
 */
class CalendarBottomSheetFragment : GapoBottomSheetFragment() {

    private var binding by autoCleared<OrderCalendarViewBottomSheetFragmentBinding>()

    private val args by navArgs<CalendarBottomSheetFragmentArgs>()

    private var selectedDate: LocalDate? = null

    private val today = LocalDate.now()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = OrderCalendarViewBottomSheetFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initLayout()
    }

    private fun initLayout() {
        initCalendar()

        binding.imageBack.setDebouncedClickListener {
            dismiss()
        }

        binding.imagePreviousDate.setDebouncedClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.previousMonth)
            }
        }

        binding.imageNextDate.setDebouncedClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.nextMonth)
            }
        }

        binding.textDone.setDebouncedClickListener {
            selectedDate?.run {
                val bundle = Bundle()
                bundle.putString(REGISTER_ID_BUNDLE, args.idRegister)
                bundle.putIntegerArrayList(
                    CALENDAR_DATA_BUNDLE,
                    ArrayList<Int>().apply {
                        add(dayOfMonth)
                        add(monthValue)
                        add(year)
                    }
                )

                setFragmentResult(CALENDAR_REQUEST_KEY, bundle)

                dismiss()
            }
        }
    }

    private fun initCalendar() {
        binding.textTitle.text = args.title.orEmpty()

        //val daysOfWeek = daysOfWeek()
        val daysOfWeek = listOf(
            DayOfWeek.MONDAY,
            DayOfWeek.TUESDAY,
            DayOfWeek.WEDNESDAY,
            DayOfWeek.THURSDAY,
            DayOfWeek.FRIDAY,
            DayOfWeek.SATURDAY,
            DayOfWeek.SUNDAY,
        )
        val currentMonth = YearMonth.now()
        val startMonth = currentMonth.minusMonths(100)
        val endMonth = currentMonth.plusMonths(100)
        setupMonthCalendar(startMonth, endMonth, daysOfWeek)

        val listDate =
            (if (args.timestampSelected > 0) args.timestampSelected else System.currentTimeMillis()).convertToListDate()
        if (listDate.size >= 3) {
            selectDate(LocalDate.of(listDate[2], listDate[1], listDate[0]))
        } else {
            selectDate(LocalDate.now())
        }

        binding.calendarView.scrollToMonth(selectedDate?.yearMonth.getOrDefault(YearMonth.now()))
    }

    private fun setupMonthCalendar(
        startMonth: YearMonth,
        endMonth: YearMonth,
        daysOfWeek: List<DayOfWeek>
    ) {
        class DayViewContainer(view: View) : ViewContainer(view) {
            lateinit var day: CalendarDay
            val textView = OrderCalendarDayViewBinding.bind(view).textDay

            init {
                view.setDebouncedClickListener {
                    if (day.position == DayPosition.MonthDate) {
                        selectDate(day.date)
                    }
                }
            }
        }

        binding.calendarView.dayBinder = object : MonthDayBinder<DayViewContainer> {
            override fun bind(container: DayViewContainer, data: CalendarDay) {
                container.day = data
                val millisStart = args.startBlockTime
                val millisEnd = args.endBlockTime

                val isBlockSelectable = let {
                    val zoneId = ZoneId.systemDefault()
                    val currentDate = data.date
                    val instantStart = Instant.ofEpochMilli(millisStart)
                    val instantEnd = Instant.ofEpochMilli(millisEnd)

                    val blockStartDate = instantStart.atZone(zoneId).toLocalDate()
                    val blockEndDate = instantEnd.atZone(zoneId).toLocalDate()

                    when {
                        millisStart != ZERO_VALUE.toLong() && millisEnd != ZERO_VALUE.toLong() -> {
                            currentDate in blockStartDate..blockEndDate
                        }

                        millisStart != ZERO_VALUE.toLong() -> {
                            currentDate >= blockStartDate
                        }

                        millisEnd != ZERO_VALUE.toLong() -> {
                            currentDate <= blockEndDate
                        }

                        else -> true
                    }
                }

                bindDate(
                    data.date,
                    container.textView,
                    (data.position == DayPosition.MonthDate) && isBlockSelectable
                )
            }

            override fun create(view: View): DayViewContainer {
                return DayViewContainer(view)
            }
        }

        binding.calendarView.monthScrollListener = {
            updateTitle()
        }

        binding.calendarView.setup(startMonth, endMonth, DayOfWeek.MONDAY)
    }

    private fun bindDate(date: LocalDate, textView: TextView, isSelectable: Boolean) {
        textView.text = date.dayOfMonth.toString()
        textView.isEnabled = isSelectable

        if (isSelectable) {
            textView.setTextColorRes(GapoColors.contentPrimary)

            if (date == selectedDate && date == today) {
                textView.setBackgroundResource(R.drawable.approval_bg_calendar_select_today)
            } else if (date == selectedDate) {
                textView.setBackgroundResource(R.drawable.approval_bg_action_approve)
            } else if (date == today) {
                textView.setBackgroundResource(R.drawable.approval_bg_edittext_reason_selected)
            } else {
                textView.background = null
            }
        } else {
            textView.setTextColorRes(GapoColors.contentQuaternary)
        }
    }

    private fun selectDate(date: LocalDate) {
        val oldDate = selectedDate
        selectedDate = date
        binding.calendarView.notifyDateChanged(date)
        oldDate?.let { binding.calendarView.notifyDateChanged(it) }
    }

    private fun updateTitle() {
        val month = binding.calendarView.findFirstVisibleMonth()?.yearMonth ?: return

        val monthTitleFormatter: DateTimeFormatter =
            DateTimeFormatter.ofPattern(DateUtils.MONTH_YEAR_NUMBER_PATTERN)

        if (GapoGlobalResources.getLanguage(requireContext()) == GapoGlobalResources.Language.VI) {
            binding.textDate.text =
                String.format(
                    getString(GapoStrings.timekeeping_month_and_year),
                    monthTitleFormatter.format(month)
                )
        } else {
            binding.textDate.text = String.format(
                getString(GapoStrings.timekeeping_month_and_year),
                monthTitleFormatter.format(month)
            )
        }
    }

    companion object {
        const val CALENDAR_REQUEST_KEY = "CALENDAR_REQUEST_KEY"
        const val REGISTER_ID_BUNDLE = "REGISTER_ID_BUNDLE"
        const val CALENDAR_DATA_BUNDLE = "CALENDAR_DATA_BUNDLE"
    }
}
