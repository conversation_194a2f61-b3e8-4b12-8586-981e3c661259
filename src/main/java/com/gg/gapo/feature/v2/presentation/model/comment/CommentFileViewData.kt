package com.gg.gapo.feature.v2.presentation.model.comment

import androidx.recyclerview.widget.DiffUtil
import com.gg.gapo.core.utilities.collections.toArrayList
import com.gg.gapo.feature.v2.presentation.model.form.value.ValueFileFieldViewData

/**
 * <AUTHOR>
 * @since 6/1/23
 */
internal data class CommentFileViewData(
    var id: String,
    var list: MutableList<ValueFileFieldViewData>,
    var isExpand: Boolean
) {
    fun shallowCopy() = this.copy(list = list.map { it.copy() }.toArrayList())

    object CommentFileDiffUtil : DiffUtil.ItemCallback<CommentFileViewData>() {
        override fun areItemsTheSame(
            oldItem: CommentFileViewData,
            newItem: CommentFileViewData
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: CommentFileViewData,
            newItem: CommentFileViewData
        ): Boolean {
            return oldItem == newItem
        }

        override fun getChangePayload(
            oldItem: CommentFileViewData,
            newItem: CommentFileViewData
        ): Any? {
            val payloads = ArrayList<Any>()

            if (oldItem.isExpand != newItem.isExpand) {
                payloads.add(COMMENT_FILE_EXPAND_PAYLOAD)
                payloads.add(COMMENT_FILE_LIST_PAYLOAD)
            }

            if (oldItem.list != newItem.list) {
                payloads.add(COMMENT_FILE_LIST_PAYLOAD)
            }

            return if (payloads.size > 0) {
                payloads
            } else {
                super.getChangePayload(oldItem, newItem)
            }
        }
    }

    companion object {
        const val COMMENT_FILE_EXPAND_PAYLOAD = "COMMENT_FILE_EXPAND_PAYLOAD"
        const val COMMENT_FILE_LIST_PAYLOAD = "COMMENT_FILE_LIST_PAYLOAD"
    }
}
