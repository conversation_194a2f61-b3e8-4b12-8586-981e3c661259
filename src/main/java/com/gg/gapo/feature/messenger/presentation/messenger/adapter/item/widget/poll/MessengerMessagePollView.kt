package com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.poll

import android.content.Context
import android.text.Spanned
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.text.toSpanned
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.pollvote.PollVoteItemView
import com.gg.gapo.core.ui.pollvote.PollVoteItemViewData
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.MessengerMessageView
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.subthread.MessengerMessageSubthreadView
import com.gg.gapo.feature.messenger.presentation.messenger.model.MessengerUserViewData
import com.gg.gapo.feature.messenger.utils.MessengerImageLoader
import com.gg.gapo.feature.messenger.utils.parseRemainingTimePollVote
import com.gg.gapo.feature.messenger.utils.timeStampToTimeInMillis
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.MessengerMessagePollViewBinding
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 18/07/2022
 */
internal class MessengerMessagePollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr),
    PollVoteItemView.OnItemListener {

    private val binding: MessengerMessagePollViewBinding =
        MessengerMessagePollViewBinding.inflate(LayoutInflater.from(context), this, true)

    private lateinit var voteAdapter: MessengerMessagePollVoteAdapter

    private var messageId: Int? = null
    private var interactor: Interactor? = null

    private val viewSubthread: MessengerMessageSubthreadView?
        get() = findViewById(R.id.view_subthread_poll)

    init {
        layoutParams =
            LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        orientation = VERTICAL

        binding.listVote.apply {
            itemAnimator = null
            layoutManager = LinearLayoutManager(context)
            addItemDecoration(MessengerMessagePollVoteDecoration(context))
        }
    }

    override fun onShowVotedUsers(pollOptionId: String) {
        val messageId = messageId ?: return
        val voteCount =
            voteAdapter.currentList.find { it.vote.voteId == pollOptionId }?.vote?.count ?: return
        interactor?.onMessengerMessageOnShowVotedUser(
            messageId,
            pollOptionId,
            voteCount
        )
    }

    override fun onUnVoted(pollOptionId: String, userId: String) {
        val messageId = messageId ?: return
        interactor?.onMessengerMessageOnUnVote(messageId, pollOptionId)
    }

    override fun onVoted(pollOptionId: String, userId: String) {
        val messageId = messageId ?: return
        interactor?.onMessengerMessageOnVote(messageId, pollOptionId)
    }

    fun setData(
        data: Data,
        imageLoader: MessengerImageLoader,
        config: MessengerMessageView.Data.Config
    ) {
        Timber.e("vit setData poll $data")
        interactor = data.interactor
        messageId = config.messageId
        val voteSize = data.votes.size
        with(binding) {
            textMess.text = data.message
            textRemaining.isVisible = data.remainingTime.isNotEmpty()
            textRemaining.text = data.remainingTime
            textQuestion.text = data.question
            textAnonymous.isVisible = data.isAnonymous
            textSeeMore.isVisible = voteSize > VOTE_SIZE_SHOW && !data.expandPollBehavior
            textAdd.isVisible =
                !textSeeMore.isVisible && voteSize < VOTE_SIZE_MAX && data.allowAddChoice
            textSeeMore.text = context.getString(
                GapoStrings.messenger_poll_see_more,
                voteSize - VOTE_SIZE_SHOW
            )
            textSeeMore.setDebouncedClickListener {
                data.interactor.onMessengerMessageOnToggleExpandPoll(config.messageId)
            }
            textAdd.setDebouncedClickListener {
                data.interactor.onMessengerMessageAddVote(config.messageId)
            }
        }
        binding.listVote.apply {
            if (adapter == null) {
                adapter =
                    MessengerMessagePollVoteAdapter(
                        context,
                        imageLoader,
                        this@MessengerMessagePollView
                    ).also {
                        voteAdapter = it
                    }
            }
        }
        val showVotes = if (data.expandPollBehavior) data.votes else data.votes.take(VOTE_SIZE_SHOW)
        voteAdapter.submitList(showVotes)

        if (config.subthread != null) {
            binding.viewSubthreadStub.viewStub?.inflate()
            viewSubthread?.setData(config.subthread, config, imageLoader)
            viewSubthread?.isVisible = true
        } else {
            viewSubthread?.isVisible = false
        }
    }

    fun onViewRecycled() {
        with(binding) {
            listVote.adapter = null
        }
        viewSubthread?.onViewRecycled()
    }

    data class Data(
        val id: String,
        val message: Spanned,
        val question: Spanned,
        val remainingTime: Spanned,
        val isAnonymous: Boolean,
        val allowAddChoice: Boolean,
        val votes: List<MessengerMessagePollVoteViewData>,
        val expandPollBehavior: Boolean,
        val interactor: Interactor
    ) {

        override fun hashCode(): Int {
            return id.hashCode() + votes.hashCode()
        }

        override fun equals(other: Any?): Boolean {
            return if (other !is Data) false
            else other.id == id && other.votes == votes && other.isAnonymous == isAnonymous &&
                other.expandPollBehavior == expandPollBehavior
        }
    }

    interface Interactor {
        fun onMessengerMessageOnVote(messageId: Int, voteId: String)

        fun onMessengerMessageOnUnVote(messageId: Int, voteId: String)

        fun onMessengerMessageOnShowVotedUser(messageId: Int, voteId: String, voteCount: Int)

        fun onMessengerMessageAddVote(messageId: Int)

        fun onMessengerMessageOnToggleExpandPoll(messageId: Int)
    }

    companion object {
        private const val VOTE_SIZE_MAX = 50
        private const val VOTE_SIZE_SHOW = 4
    }
}

internal data class MessengerMessagePollVoteViewData(
    val vote: PollVoteItemViewData
) {

    override fun hashCode(): Int {
        return vote.voteId.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return if (other !is MessengerMessagePollVoteViewData) false
        else other.vote.voteId == vote.voteId && other.vote.title == vote.title &&
            other.vote.type == vote.type && other.vote.count == vote.count &&
            other.vote.pollVoteCount == vote.pollVoteCount &&
            other.vote.votedUsers == vote.votedUsers
    }

    object DiffCallback : DiffUtil.ItemCallback<MessengerMessagePollVoteViewData>() {
        override fun areItemsTheSame(
            oldItem: MessengerMessagePollVoteViewData,
            newItem: MessengerMessagePollVoteViewData
        ): Boolean {
            return oldItem.vote.voteId == newItem.vote.voteId
        }

        override fun areContentsTheSame(
            oldItem: MessengerMessagePollVoteViewData,
            newItem: MessengerMessagePollVoteViewData
        ): Boolean {
            return oldItem == newItem
        }
    }
}

internal fun MessageModel.mapToMessengerMessagePollViewData(
    context: Context,
    myUser: MessengerUserViewData,
    expandPollBehavior: Boolean,
    interactor: MessengerMessagePollView.Interactor
): MessengerMessagePollView.Data? {
    val pollInfo = body.metadata?.pollInformation ?: return null
    val message = if (pollInfo.votes.firstOrNull { it.count > 0 } == null) {
        context.getString(
            GapoStrings.messenger_poll_user_create_poll,
            sender.name
        )
    } else {
        context.getString(
            GapoStrings.messenger_poll_user_updated_vote,
            sender.name
        )
    }
    val question = pollInfo.title.toSpanned()
    val remainingTime = context.parseRemainingTimePollVote(pollInfo.endAt).toSpanned()
    val inAnonymous = pollInfo.incognito

    val type = when {
        pollInfo.endAt != 0L && pollInfo.endAt.timeStampToTimeInMillis - System.currentTimeMillis() <= 0 -> PollVoteItemViewData.Type.NO_SELECTION
        pollInfo.allowMultipleChoice -> PollVoteItemViewData.Type.MULTI_SELECTION
        else -> PollVoteItemViewData.Type.ONE_SELECTION
    }

    val currentUser = PollVoteItemViewData.User.create(
        context,
        myUser.id,
        myUser.displayName,
        myUser.avatar,
        myUser.avatarThumbPattern
    )

    val votes = pollInfo.votes.map { vote ->
        val userVoters = vote.userVotes
            .take(3) // 3 vì để PollVoteItemView khi unvote có thể show đc 2 người khác luôn
            .filter { it.userId != currentUser.id }
            .map { user ->
                PollVoteItemViewData.User.create(
                    context,
                    user.id,
                    user.displayName,
                    user.avatar,
                    user.avatarThumbPattern
                )
            }.toMutableList().apply {
                if (pollInfo.myselfVotes.contains(vote.id)) {
                    add(0, currentUser)
                }
            }

        MessengerMessagePollVoteViewData(
            PollVoteItemViewData(
                voteId = vote.id,
                title = vote.title,
                count = vote.count,
                pollVoteCount = pollInfo.totalVote,
                isAnonymousPoll = pollInfo.incognito,
                type = type,
                votedUsers = userVoters,
                currentUser = currentUser
            )
        )
    }
    return MessengerMessagePollView.Data(
        pollInfo.id,
        message.toSpanned(),
        question,
        remainingTime,
        inAnonymous,
        pollInfo.allowAddChoice,
        votes,
        expandPollBehavior,
        interactor
    )
}
