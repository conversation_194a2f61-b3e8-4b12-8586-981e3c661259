package com.gg.gapo.feature.messenger.presentation.test

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.feature.messenger.presentation.test.folder.FolderTestAdapter
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.FragmentTestConversationBinding
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 08/06/2022
 */
class ConversationTestFragment : Fragment() {

    private var binding by autoCleared<FragmentTestConversationBinding>()
    private val viewModel by viewModel<ConversationTestViewModel>()

    private lateinit var folderTestAdapter: FolderTestAdapter
    private lateinit var conversationTestAdapter: ConversationTestAdapter
    private lateinit var linearLayoutManager: LinearLayoutManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentTestConversationBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("UnsafeRepeatOnLifecycleDetector")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.listItemFolder.run {
            layoutManager =
                LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            adapter = FolderTestAdapter(viewModel).also { folderTestAdapter = it }
        }

        binding.listItem.run {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context)
                .also { linearLayoutManager = it }
            addItemDecoration(object :
                    DividerItemDecoration(context, RecyclerView.VERTICAL) {
                    override fun getItemOffsets(
                        outRect: Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        if (parent.getChildAdapterPosition(view) != RecyclerView.NO_POSITION) {
                            outRect.set(24, 24, 24, this.drawable?.intrinsicHeight ?: 24)
                        }
                    }
                })
            setItemViewCacheSize(5)
            setRecycledViewPool(
                RecyclerView.RecycledViewPool().apply {
                    setMaxRecycledViews(R.layout.conversation_test_item, Int.MAX_VALUE)
                }
            )
            adapter = ConversationTestAdapter(viewModel)
                .also { conversationTestAdapter = it }
        }

        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            viewModel.foldersFlow.distinctUntilChanged()
//                .onEach {
//                    Timber.e("foldersFlow onEach -> ${it}" )
//                    it.firstOrNull()?.let { folder -> viewModel.folderSelected(folder.alias) }
//                }
                .collectLatest {
                    folderTestAdapter.submitList(it)
                }
        }

        viewModel.fetchListFolder()

        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            viewModel.conversationsFlow
                .distinctUntilChanged()
                .collectLatest {
                    conversationTestAdapter.submitData(it)
                }
        }

//        lifecycleScope.launchWhenStarted {
//            delay(20_000)
//            Timber.e("refresh adapter")
//            conversationTestAdapter.refresh()
//            while (isActive) {
//                Log.e("TAG", "itemCount = ${messageAdapter.itemCount}")
//                delay(1_000)
//            }
//        }
    }

    companion object {
        fun newInstance(): ConversationTestFragment {
            return ConversationTestFragment()
        }
    }
}
