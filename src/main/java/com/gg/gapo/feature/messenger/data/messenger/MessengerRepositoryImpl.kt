package com.gg.gapo.feature.messenger.data.messenger

import androidx.paging.PagingData
import com.gg.gapo.core.auth.manager.AuthManager
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.feature.messenger.data.messenger.conversation.ConversationRepository
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.mapToEntity
import com.gg.gapo.feature.messenger.data.messenger.conversation.paging.mapToCallClientId
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.dto.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.dto.mapToSubThreadDomain
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.request.CreateThreadRequestBody
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.request.ThreadOrgcRequestBody
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.response.ConversationCreatedResponse
import com.gg.gapo.feature.messenger.data.messenger.folder.FolderRepository
import com.gg.gapo.feature.messenger.data.messenger.folder.cache.model.mapToEntity
import com.gg.gapo.feature.messenger.data.messenger.folder.remote.model.dto.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.message.MessageRepository
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.SenderEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.mapToEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.mapToMessageUserEntity
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Maker
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.MessageCreatedMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Receiver
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Settings
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.StatusMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.mapToConversationDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.mapToFolderDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.mapToMessageDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.BlockUserRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.SaveMessageRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.JoinLinkCollabResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.MessageCreateResponse
import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerScreenScopeEnum
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ChatPendingModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ChatPendingStatus
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationRole
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.UserType
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderModel
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageBotCommandModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageDraftModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserParticipantsModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.QuickMessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.ReactedUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageMultipleTargetRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.feature.messenger.domain.messenger.model.mqtt.MqttEventType
import com.gg.gapo.feature.messenger.logger.MessengerLogger
import com.gg.gapo.feature.messenger.presentation.helper.Constant.MENTION_ALL
import com.gg.gapo.feature.messenger.presentation.messenger.adapter.item.widget.reaction.MessengerReactType
import com.gg.gapo.messenger.data.sources.models.bases.Links
import com.gg.gapo.messenger.data.sources.models.collab.CollabModel
import com.gg.gapo.messenger.domain.models.ChatBotModel
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

/**
 * Created by bacnd on 10/06/2022.
 */
internal class MessengerRepositoryImpl(
    private val conversationRepository: ConversationRepository,
    private val folderRepository: FolderRepository,
    private val messageRepository: MessageRepository,
    private val userManager: UserManager,
    private val coroutineDispatchers: CoroutineDispatchers,
    private val authManager: AuthManager,
    private val messengerLogger: MessengerLogger
) : MessengerRepository {
    override fun conversationsFlow(): Flow<PagingData<ConversationModel>> =
        conversationRepository.conversationsFlow()

    override suspend fun pinConversationRemote(conversationId: Long) =
        conversationRepository.pinConversationRemote(conversationId)

    override fun foldersFlow(): Flow<List<FolderModel>> = folderRepository.foldersFlow()

    override fun folderUnreadFlow(): Flow<FolderModel?> = folderRepository.folderUnreadFlow()

    // override suspend fun fetchListFolders() = folderRepository.fetchFolders()

    override suspend fun selectFolder(folderType: FolderType) =
        conversationRepository.selectFolder(folderType)

    override suspend fun sortFolders(aliasFolders: List<String>) =
        folderRepository.sortFolders(aliasFolders)

    override suspend fun getFolderById(alias: String): FolderModel? =
        folderRepository.getFolderById(alias)

    override suspend fun deleteFolder(alias: String) = folderRepository.deleteFolder(alias)

    override suspend fun moveFolders(threadIds: List<Long>, newFolder: String) =
        folderRepository.moveFolders(threadIds, newFolder)

    override fun messagesFlow(conversationId: Long): Flow<PagingData<MessageModel>> =
        messageRepository.messagesFlow(conversationId)

    override fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>> =
        messageRepository.messagesPinedFlow(conversationId)

    override suspend fun jumpTo(messageId: Int) = messageRepository.jumpTo(messageId)

    override suspend fun pinMessage(messageId: Int, query: HashMap<String, String>) =
        messageRepository.pinMessage(messageId, query)

    override suspend fun unpinMessage(query: HashMap<String, String>) =
        messageRepository.unpinMessage(query)

    override suspend fun reactMessage(conversationId: Long, messageId: Int, reactType: Int) =
        messageRepository.reactMessage(conversationId, messageId, reactType)

    override suspend fun createMessage(messageRequestModel: MessageRequestModel): MessageCreateResponse? =
        messageRepository.createMessage(messageRequestModel)

    override suspend fun createMessageForMultipleTarget(messageMultipleTargetRequestModel: MessageMultipleTargetRequestModel): MessageCreateResponse? =
        messageRepository.createMessageForMultipleTarget(messageMultipleTargetRequestModel)

    override suspend fun saveMessageCreatorRequest(messageRequestModel: MessageRequestModel) {
        if (messageRequestModel.id == Int.MAX_VALUE) {
            // getTotal message in conversation
            val numMessageInConversation =
                conversationRepository.getNumMessageInConversation(messageRequestModel.threadId)

            messageRequestModel.id = numMessageInConversation + 1

            // if clientId < createdAt lastmsg thi clientId == createdAt lastmsg + 1
            val createAtLastMessage =
                messageRepository.getCreateAtLastMessageInConversationLocal(messageRequestModel.threadId)
            if (messageRequestModel.clientId < createAtLastMessage) messageRequestModel.clientId =
                createAtLastMessage + 1
        }

        messageRequestModel.createdAt = messageRequestModel.clientId
        val userProfile = userManager.userProfile
        messageRequestModel.sender = MessageModel.SenderModel(
            id = userManager.userId,
            avatar = userProfile?.avatar.orEmpty(),
            name = userProfile?.displayName.orEmpty(),
            seenAt = 0L,
            statusVerify = 0,
            type = UserType.USER
        )
        var replyToMsgObjectBody: MessageEntity? = null
        if (messageRequestModel.body?.replyToMsg != null && messageRequestModel.body.replyToMsg != 0) {
            replyToMsgObjectBody = messageRepository.getMessageCachedById(
                messageRequestModel.threadId,
                messageRequestModel.body.replyToMsg
            )
                ?.mapToEntity(messageRequestModel.threadId)
        }

        val messageEntityToSave = messageRequestModel.mapToEntity(messageRequestModel.threadId)
        messageEntityToSave.apply {
            if (this.replyToMsg != null && this.replyToMsg != 0) {
                this.replyToMsgObjectBody = replyToMsgObjectBody
            }
        }

        // save message
        messageRepository.saveMessageCreatorRequest(messageEntityToSave)
    }

    override suspend fun deleteMessageError(conversationId: Long, createAt: Long) =
        messageRepository.deleteMessageError(conversationId, createAt)

    override suspend fun getMessageRequestModelError(
        conversationId: Long,
        createAt: Long
    ): MessageRequestModel? =
        messageRepository.getMessageRequestModelError(conversationId, createAt)

    override suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel) =
        messageRepository.updateMessageCreatorRequest(messageRequestModel)

    override suspend fun findByClientId(
        clientId: Long,
        conversationId: Long
    ): MessageRequestModel? =
        messageRepository.findByClientId(clientId, conversationId)

    override suspend fun read(conversationId: Long, messageId: Int) {
        // update conversation
        conversationRepository.readAtConversationMqtt(conversationId, messageId, userManager.userId)

        messageRepository.read(conversationId, messageId)
    }

    override suspend fun markUnread(conversationId: Long, messageId: Int) {
        messageRepository.markUnread(conversationId, messageId)
    }

    override suspend fun updateReadMessageFromTypingEvent(conversationId: Long, userId: String) {
        readAtConversationMqtt(conversationId, 0, userId)
    }

    override suspend fun fetchMessageFirstPageInConversations(folderType: FolderType) {
        // query tat ca conversation dang cached 1 message
        val conversationsCached =
            conversationRepository.getAllConversationCachingOnlyMessage(folderType)

        // fetch tung firstPage cua moi conversationEntity
        if (conversationsCached.isNotEmpty()) {
            messageRepository.fetchMessageFirstPageInConversations(conversationsCached)
        }
    }

    override suspend fun searchUsers(
        queries: Map<String, String>,
        groupId: String
    ): Pair<List<MessageUserModel>, Map<String, String>> =
        messageRepository.searchUsers(queries, groupId)

    override fun lastMessageFlow(conversationId: Long): Flow<MessageModel> =
        messageRepository.lastMessageFlow(conversationId)

    override suspend fun lastMessage(conversationId: Long): MessageModel? =
        messageRepository.lastMessage(conversationId)

    override suspend fun getMessageById(conversationId: Long, messageId: Int): MessageModel? =
        messageRepository.getMessageCachedById(conversationId, messageId)

    override suspend fun fetchMessengerReactedUsers(
        messageId: Int,
        queries: Map<String, String>
    ): Pair<List<ReactedUserModel>, Map<String, String>> =
        messageRepository.fetchMessengerReactedUsers(messageId, queries)

    override suspend fun getReactedUsersInDirect(
        conversationId: Long,
        messageId: Int
    ): List<ReactedUserModel> {
        val message = messageRepository.getMessageCachedById(conversationId, messageId)

        return buildList {
            if (message?.react?.reactCountTotal == 1) {
                // xem la minh hay la partner
                if (message.react.reactYourself > 0) { // la minh
                    add(getReactedUserMySelfInDirect(message.react.reactYourself))
                } else { // la partner
                    val partner =
                        conversationRepository.getConversationById(conversationId)?.partner
                    val reactCount = message.react.reactCount
                    val reactType = if (reactCount.reactType1 > 0) MessengerReactType.FIRST.type
                    else if (reactCount.reactType2 > 0) MessengerReactType.SECOND.type
                    else if (reactCount.reactType3 > 0) MessengerReactType.THIRD.type
                    else if (reactCount.reactType4 > 0) MessengerReactType.FOURTH.type
                    else if (reactCount.reactType5 > 0) MessengerReactType.FIFTH.type
                    else MessengerReactType.SIXTH.type
                    add(getReactedUserPartnerInDirect(reactType, partner))
                }
            } else if (message?.react?.reactCountTotal == 2) {
                add(getReactedUserMySelfInDirect(message.react.reactYourself))
                val reactYourself = MessengerReactType.getByType(message.react.reactYourself)
                val partner = conversationRepository.getConversationById(conversationId)?.partner
                val reactCount = message.react.reactCount
                val reactType =
                    if ((reactCount.reactType1 == 1 && reactYourself != MessengerReactType.FIRST) || reactCount.reactType1 == 2) MessengerReactType.FIRST.type
                    else if ((reactCount.reactType2 == 1 && reactYourself != MessengerReactType.SECOND) || reactCount.reactType2 == 2) MessengerReactType.SECOND.type
                    else if ((reactCount.reactType3 == 1 && reactYourself != MessengerReactType.THIRD) || reactCount.reactType3 == 2) MessengerReactType.THIRD.type
                    else if ((reactCount.reactType4 == 1 && reactYourself != MessengerReactType.FOURTH) || reactCount.reactType4 == 2) MessengerReactType.FOURTH.type
                    else if ((reactCount.reactType5 == 1 && reactYourself != MessengerReactType.FIFTH) || reactCount.reactType5 == 2) MessengerReactType.FIFTH.type
                    else MessengerReactType.SIXTH.type
                add(getReactedUserPartnerInDirect(reactType, partner))
            }
        }
    }

    override suspend fun getSenderById(userId: String): MessageModel.SenderModel? =
        messageRepository.getSenderById(userId)

    override suspend fun sendMessagesToSave(body: SaveMessageRequestBody) =
        messageRepository.sendMessagesToSave(body)

    override suspend fun deleteMessages(
        messageIds: List<Int>,
        conversationId: Long,
        level: MessageLevelDeleteType
    ) =
        messageRepository.deleteMessages(messageIds, conversationId, level)

    override suspend fun createConversation(body: CreateThreadRequestBody): ConversationCreatedResponse? =
        conversationRepository.createConversation(body)

    override suspend fun createOrganisation(body: ThreadOrgcRequestBody): ConversationCreatedResponse? =
        conversationRepository.createOrganisation(body)

    override suspend fun addOrganisation(body: ThreadOrgcRequestBody) =
        conversationRepository.addOrganisation(body)

    override suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel) =
        messageRepository.editMessageCacheAction(messageRequestModel)

    override suspend fun editMessageAction(conversationId: Long, messageId: Int) =
        messageRepository.editMessageAction(conversationId, messageId)

    override suspend fun createVote(
        conversationId: Long,
        messageId: Int,
        voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel
    ) {
        messageRepository.createVote(conversationId, messageId, voteAdded)
    }

    override suspend fun chooseVote(
        conversationId: Long,
        messageId: Int,
        voteId: String,
        isChoose: Boolean
    ) {
        messageRepository.chooseVote(conversationId, messageId, voteId, isChoose)
    }

    override suspend fun getUserVoted(
        conversationId: Long,
        messageId: Int,
        voteId: String
    ): List<MessageUserModel> =
        messageRepository.getUserVoted(conversationId, messageId, voteId)

    override suspend fun fetchUsersVoted(queryMap: Map<String, String>): List<MessageUserModel> =
        messageRepository.fetchUsersVoted(queryMap)

    override suspend fun getUsersWithIds(userIds: List<String>): List<MessageUserModel> =
        messageRepository.getUsersWithIds(userIds)

    override suspend fun fetchUserParticipants(threadId: Long) =
        messageRepository.fetchUserParticipants(threadId)

    override suspend fun getUserParticipantsById(
        conversationId: Long,
        userId: String
    ): MessageUserParticipantsModel? =
        conversationRepository.getUserParticipantsById(conversationId, userId)

    override suspend fun updateMessageTextDraft(
        conversationId: Long,
        messageDraft: MessageDraftModel?
    ) {
        messageRepository.updateMessageTextDraft(conversationId, messageDraft)
    }

    private fun getReactedUserMySelfInDirect(reactYourself: Int): ReactedUserModel {
        val userProfile = userManager.userProfile

        return ReactedUserModel(
            ReactedUserModel.User(
                id = userProfile?.id.orEmpty(),
                displayName = userProfile?.displayName.orEmpty(),
                avatar = userProfile?.avatar.orEmpty(),
                avatarThumbPattern = userProfile?.avatarThumbPattern.orEmpty(),
                info = userProfile?.info
            ),
            reactYourself,
            null
        )
    }

    private fun getReactedUserPartnerInDirect(
        reactType: Int,
        partner: ConversationModel.PartnerModel?
    ): ReactedUserModel {
        return ReactedUserModel(
            ReactedUserModel.User(
                id = partner?.id.orEmpty(),
                displayName = partner?.name.orEmpty(),
                avatar = partner?.avatar.orEmpty(),
                avatarThumbPattern = partner?.avatar.orEmpty(),
                info = null
            ),
            reactType,
            null
        )
    }

    override suspend fun createMessageMqtt(
        messageCreatedMqttDto: MessageCreatedMqttDto,
        scope: MessengerScreenScopeEnum
    ) {
        when (scope) {
            MessengerScreenScopeEnum.CONVERSATION -> {
                // if dang o man conversation thi update folder unread_count
                // folderRepository.fetchAndUpdateUnReadCountFolder()
            }

            MessengerScreenScopeEnum.MESSAGE -> {
            }

            MessengerScreenScopeEnum.APP -> {
                val folderMqttEntity =
                    messageCreatedMqttDto.mapToFolderDto().mapToDomain().mapToEntity()
                val conversationMqttEntity =
                    messageCreatedMqttDto.mapToConversationDto()?.mapToDomain()?.mapToEntity()
                val sender = messageCreatedMqttDto.sender?.mapToDomain()?.mapToMessageUserEntity()?.mapToDomain()
                val messageMqttEntity =
                    conversationMqttEntity?.let { conversationMqttEntityUnmanaged ->
                        val messageDto = messageCreatedMqttDto.mapToMessageDto()

                        messageDto.mapToDomain()?.let { messageModel ->
                            messageRepository.fillMessage(
                                messageModel,
                                conversationMqttEntityUnmanaged.id
                            )
                                .mapToEntity(conversationMqttEntityUnmanaged.id).apply {
                                    val userIdForward = messageDto.getBodyParse()?.forward?.userId
                                    val userNameForward =
                                        messageDto.getBodyParse()?.forward?.userName
                                    if (!userIdForward.isNullOrEmpty()) {
                                        val senderEntity =
                                            messageRepository.getSenderById(userIdForward)
                                                ?.mapToEntity()
                                        if (senderEntity == null) {
                                            userForward = SenderEntity(
                                                id = userIdForward,
                                                name = userNameForward
                                            )
                                        } else {
                                            userForward = senderEntity
                                        }
                                    }
                                }
                        }
                    }

                // update them thong tin cho conversation nhan dc tu mqtt
                conversationMqttEntity?.let {
                    it.messageCount = messageMqttEntity?.id ?: 0
                    messageMqttEntity?.mentionsMetadataBody?.let { mentions ->
                        if (mentions.find { mention -> mention.target == MENTION_ALL || mention.target == userManager.userId } != null) {
                            it.tags = listOf(messageMqttEntity.id.toString()).joinToString()
                        }
                    }
                    messageMqttEntity?.replyToMsgObjectBody?.sender?.id?.let { senderIdMessageReply ->
                        if (senderIdMessageReply == userManager.userId) {
                            it.tags = listOf(messageMqttEntity.id.toString()).joinToString()
                        }
                    }
                }

                // createFolderMqtt
                // TH mqtt create message sender là mình thì k có thông tin folder của thread, còn ng khác thì có
                val folderCached = folderRepository.createFolderMqtt(folderMqttEntity)

                conversationMqttEntity?.let {
                    // createConversation
                    conversationMqttEntity.folder = folderCached

                    val conversationId = conversationMqttEntity.id
                    val currentUnReadCount =
                        conversationRepository.getConversationById(conversationId)?.unReadCount

                    val conversationCached = conversationRepository.createConversationMqtt(
                        conversationMqttEntity,
                        messageMqttEntity
                    )

                    messageMqttEntity?.let {
                        // createMessage
                        // linking cho message vs conversation, replyToMsgObject
                        // messageRepository.fillLinkMessage(messageMqttEntity, conversationCached)

                        messageMqttEntity.conversation = conversationCached
                        if (messageMqttEntity.replyToMsg != null && messageMqttEntity.replyToMsg != 0) {
                            messageMqttEntity.replyToMsgObjectBody =
                                messageRepository.getMessageById(
                                    conversationMqttEntity.id,
                                    messageMqttEntity.replyToMsg ?: 0
                                )
                        }

                        messageRepository.createMessageMqtt(
                            messageMqttEntity,
                            conversationMqttEntity.id
                        )
                    }

                    // update subthread info of message
                    messageRepository.updateMessageSubthreadMqtt(
                        messageCreatedMqttDto.mapToConversationDto()?.parentId,
                        messageCreatedMqttDto.mapToConversationDto()?.rootMessageId,
                        sender,
                        messageCreatedMqttDto.mapToConversationDto()?.mapToSubThreadDomain()
                    )
                }
            }
        }
    }

    override suspend fun deleteConversationMqtt(conversationId: Long) {
        conversationRepository.deleteConversationMqtt(conversationId)
    }

    override suspend fun pinConversationCache(
        conversationId: Long,
        pinnedAt: Long,
        folder: FolderType?
    ) {
        conversationRepository.pinConversationCache(conversationId, pinnedAt, folder)
    }

    override suspend fun unPinConversation(conversationId: Long) {
        conversationRepository.unPinConversation(conversationId)
    }

    override suspend fun readAtConversationMqtt(
        conversationId: Long,
        messageId: Int,
        userId: String
    ) {
        // update conversation
        conversationRepository.readAtConversationMqtt(conversationId, messageId, userId)

        // update message
        messageRepository.readAtMessageMqtt(conversationId, messageId, userId)
    }

    /**
     * Đang bị gọi 2 lần khi App start và ConversationScreen onResume
     * Trace lại các chỗ khác xem có bị tương tự hay ko
     * Cần xử lý
     */
    override suspend fun connectedMessengerMqtt() {
        // TODO co thoi gian can toi uu lai cho nay

        // rest update folder
        folderRepository.fetchFolders()

        // rest update 40 dau tien conversation default
        conversationRepository.refreshConversationMqtt()
    }

    override suspend fun connectedMessageMqtt(conversationId: Long) {
        val conversationModel = fetchConversation(conversationId)
        conversationModel?.let {
            if (messageRepository.getMessageById(conversationId, it.messageCount) == null) {
                jumpTo(it.messageCount)
            }
        }
    }

    override suspend fun fetchConversation(conversationId: Long): ConversationModel? {
        val conversationModel =
            conversationRepository.fetchConversation(conversationId)?.mapToDomain()

        // update conversation
        conversationModel?.let {
            val conversationResult = if (it.type == ConversationType.GROUP) {
                try {
                    val roomInfoId = messageRepository.getRoomInfo(
                        authManager.uniqueDeviceId.mapToCallClientId(),
                        arrayListOf(conversationId.toString())
                    ).lastOrNull()?.id
                    it.copy(roomId = roomInfoId)
                } catch (e: Exception) {
                    Timber.e(e)
                    it
                }
            } else {
                it
            }
            conversationRepository.updateConversationCache(conversationResult.mapToEntity())
//            it.referencedMessage?.mapToEntity(conversationModel.id)?.let { referencedMessage ->
//                messageRepository.updateMessageCache(referencedMessage, conversationId)
//            }
            return conversationResult
        }

        // update last message
//        conversationModel?.lastMessage?.let {
//            val lastMessage = messageRepository.fillMessage(it, conversationModel.id).mapToEntity(conversationModel.id)
//            messageRepository.fillAndSaveMessageEntity(lastMessage, conversationModel.id)
//        }

        return conversationModel
    }

    override suspend fun deleteMessageMqtt(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    ) {
        messageRepository.deleteMessageMqtt(conversationId, messageId, level)
    }

    override suspend fun editMessageMqtt(statusMqttDto: StatusMqttDto) {
        messageRepository.editMessageMqtt(statusMqttDto)
    }

    override suspend fun updateMessageSubthreadMqtt(statusMqttDto: StatusMqttDto) {
        val subthreadId = statusMqttDto.body?.subthread?.id ?: 0L
        val conversationModel = fetchConversation(subthreadId)
        messageRepository.updateMessageSubthreadMqtt(conversationModel, statusMqttDto)
    }

    override suspend fun reactMessageMqtt(statusMqttDto: StatusMqttDto) {
        messageRepository.reactMessageMqtt(statusMqttDto)
    }

    override suspend fun blockConversationMqtt(
        maker: Maker,
        receiver: Receiver,
        block: MqttEventType,
        isMeMaker: Boolean
    ) {
        conversationRepository.blockConversationMqtt(maker, receiver, block, isMeMaker)
    }

    override suspend fun updateSettingsMqtt(conversationId: Long, settings: Settings) {
        conversationRepository.updateSettingsMqtt(conversationId, settings)
    }

    override suspend fun updateAuthorizeMqtt(
        conversationId: Long,
        userId: String,
        role: ConversationRole
    ) {
        conversationRepository.updateAuthorizeMqtt(conversationId, userId, role)
    }

    override suspend fun updateMarkUnReadMqtt(conversationId: Long, markUnread: Boolean) {
        conversationRepository.updateMarkUnReadMqtt(conversationId, markUnread)
    }

    override suspend fun fetchPreviewLink(url: String): MessageModel.MessagePreviewLinkModel? =
        messageRepository.fetchPreviewLink(url)

    override suspend fun toggleNotifyConversationCache(conversationId: Long) {
        conversationRepository.toggleNotifyConversationCache(conversationId)
    }

    override suspend fun toggleNotifyConversation(conversationId: Long) {
        conversationRepository.toggleNotifyConversation(conversationId)
    }

    override suspend fun clearHistory(conversationId: Long) {
        conversationRepository.clearHistory(conversationId)
    }

    override suspend fun fetchTimeStampUserOnline(folderType: FolderType) {
        conversationRepository.fetchTimeStampUserOnline(folderType)
    }

    override fun conversationFlow(conversationId: Long): Flow<ConversationModel?> =
        conversationRepository.conversationFlow(conversationId)

    override suspend fun getConversationById(conversationId: Long): ConversationModel? =
        conversationRepository.getConversationById(conversationId)

    override suspend fun getForwardConversation(): List<ConversationModel> =
        conversationRepository.getForwardConversation()

    override suspend fun sendTypingEvent(conversationId: Long) {
        messageRepository.sendTypingEvent(conversationId)
    }

    override suspend fun moveConversationToFolder(conversationId: Long, folderAlias: String) {
        conversationRepository.moveConversationToFolder(conversationId, folderAlias)
    }

    override suspend fun fetchThreadByCollabId(collabId: String): ConversationCreatedResponse? {
        return conversationRepository.fetchThreadByCollabId(collabId)
    }

    override suspend fun fetchCollabById(collabId: String): CollabModel? {
        return conversationRepository.fetchCollabById(collabId)
    }

    override suspend fun joinCollab(collabId: String) {
        return conversationRepository.joinCollab(collabId)
    }

    override suspend fun joinLinkCollab(inviteToken: String): JoinLinkCollabResponse? {
        return conversationRepository.joinLinkCollab(inviteToken)
    }

    override suspend fun updateConversationLastVisit(conversationId: Long, lastVisitAt: Long) {
        conversationRepository.updateConversationLastVisit(conversationId, lastVisitAt)
    }

    override suspend fun getConversationsByVisit(): List<ConversationModel> {
        return conversationRepository.getConversationsByVisit()
    }

    override suspend fun setUnreadFlag(conversationId: Long, unread: Boolean) {
        conversationRepository.setUnreadFlag(conversationId, unread)

        if (!unread) {
            getConversationById(conversationId)?.let { conversation ->
                if (conversation.unReadCount > 0) {
                    read(conversationId, conversation.lastMessage?.id ?: 0)
                }
            }
        }
    }

    override suspend fun fetchChatPending(
        collabId: String,
        queries: Map<String, String>
    ): Pair<List<ChatPendingModel>, Map<String, String>> =
        conversationRepository.fetchChatPending(collabId, queries)

    override suspend fun reviewsChatPending(
        collabId: String,
        pendingId: String,
        status: ChatPendingStatus
    ) =
        conversationRepository.reviewsChatPending(collabId, pendingId, status)

    override suspend fun createSubThread(conversationId: Long, messageId: Int): ConversationModel? {
        return conversationRepository.createSubThread(conversationId, messageId)
    }

    override suspend fun statusForMessengerFlow(data: StatusMqttDto) {
        when (data.eventType) {
            MqttEventType.THREAD_DELETED -> deleteConversationMqtt(data.body?.threadId ?: 0L)
            MqttEventType.PIN -> {
                val folder = FolderType.getByType(data.body?.folder ?: FolderType.Default.alias)
                val conversationId = data.body?.threadId ?: 0L
                val pinnedAt = data.body?.pinnedAt ?: 0L
                pinConversationCache(
                    conversationId,
                    pinnedAt,
                    folder
                )
            }

            MqttEventType.READ_AT -> readAtConversationMqtt(
                data.body?.threadId ?: 0L,
                data.body?.messageId ?: 0,
                data.body?.userId.orEmpty()
            )

            MqttEventType.MESSAGE_DELETED -> deleteMessageMqtt(
                data.body?.threadId ?: 0L,
                data.body?.messageId ?: 0,
                MessageLevelDeleteType.getByLevel(data.body?.level)
                    ?: MessageLevelDeleteType.FOR_ALL
            )

            MqttEventType.MESSAGE_EDITED -> {
                editMessageMqtt(data)
            }

            MqttEventType.MESSAGE_REACT -> reactMessageMqtt(data)
            MqttEventType.BLOCK -> {
                val maker = data.body?.maker
                val receiver = data.body?.receiver
                if (maker != null && receiver != null) {
                    blockConversationMqtt(
                        maker,
                        receiver,
                        MqttEventType.BLOCK,
                        userManager.userId == maker.id
                    )
                }
            }

            MqttEventType.UNBLOCK -> {
                val maker = data.body?.maker
                val receiver = data.body?.receiver
                if (maker != null && receiver != null) {
                    blockConversationMqtt(
                        maker,
                        receiver,
                        MqttEventType.UNBLOCK,
                        userManager.userId == maker.id
                    )
                }
            }

            MqttEventType.UPDATE_SETTINGS -> {
                val conversationId = data.body?.threadId ?: 0L
                val settings = data.body?.settings
                settings?.let {
                    updateSettingsMqtt(conversationId, settings)
                }
            }

            MqttEventType.AUTHORIZE -> {
                val conversationId = data.body?.threadId ?: 0L
                val userId = data.body?.userId.orEmpty()
                val role = ConversationRole.getByRole(data.body?.role)
                role?.let {
                    updateAuthorizeMqtt(conversationId, userId, role)
                }
            }

            MqttEventType.MARK_UNREAD_THREAD -> {
                val conversationId = data.body?.threadId ?: 0L
                val markUnread = data.body?.markUnread
                markUnread?.let {
                    updateMarkUnReadMqtt(conversationId, markUnread)
                }
            }

            MqttEventType.NON_MEM_SUBTHREAD_MESSAGE -> {
                updateMessageSubthreadMqtt(data)
            }

            else -> {}
        }
    }

    override suspend fun messageForMessengerFlow(
        data: MessageCreatedMqttDto,
        scope: MessengerScreenScopeEnum
    ) {
        createMessageMqtt(data, scope)
    }

    override suspend fun blockUser(body: BlockUserRequestBody) {
        messageRepository.blockUser(body)
    }

    override suspend fun fetchBotCommands(botId: String): List<MessageBotCommandModel> =
        messageRepository.fetchBotCommands(botId)

    override suspend fun fetchBotById(botId: String) = messageRepository.fetchBotById(botId)
    override suspend fun getBotList(conversationId: Long) =
        conversationRepository.getBotList(conversationId)

    override suspend fun autoDeleteMessages(threadId: Long) =
        messageRepository.autoDeleteMessages(threadId)

    override suspend fun existAutoDeleteMessages(threadId: Long) =
        messageRepository.existAutoDeleteMessages(threadId)

    override suspend fun searchBot(params: Map<String, Any>): Pair<Links, List<ChatBotModel>> {
        val result = messageRepository.searchBot(params)
        return Pair(result.links ?: Links("", 0, 0, 0), result.data.map { it.mapFromEntity() })
    }

    override suspend fun getActionNotes(
        threadId: Long,
        lastCreatedAt: Long,
        prevCreatedAt: Long,
        middleCreatedAt: Long,
        pageSize: Int
    ): List<MessageModel> {
        return messageRepository.getActionNotes(
            threadId,
            lastCreatedAt,
            prevCreatedAt,
            middleCreatedAt,
            pageSize
        )
    }

    override suspend fun joinGroupCall(client: String, roomId: String): String {
        return messageRepository.joinGroupCall(client = client, roomId = roomId)
    }

    override suspend fun getQuickMessages(): List<QuickMessageModel> {
        return messageRepository.getQuickMessages()
    }

    override suspend fun fetchUsersById(id: String): MessageUserModel? {
        return messageRepository.fetchUsersById(id)
    }
}
