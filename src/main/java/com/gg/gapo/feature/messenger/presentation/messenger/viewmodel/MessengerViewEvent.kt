package com.gg.gapo.feature.messenger.presentation.messenger.viewmodel

import com.gg.gapo.core.navigation.deeplink.photo.viewer.MediaViewerDeepLink
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageBotCommandModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.QuickMessageModel
import com.gg.gapo.feature.messenger.presentation.messenger.model.MessengerUserViewData

/**
 * <AUTHOR>
 * @since 07/07/2022
 */
internal sealed class MessengerViewEvent {

    object OnFinish : MessengerViewEvent()

    object OnShowKeyboard : MessengerViewEvent()

    object OnOpenGallery : MessengerViewEvent()

    object OnOpenSelectedMedia : MessengerViewEvent()

    object OnCreatePoll : MessengerViewEvent()

    object OnAttachFile : MessengerViewEvent()

    object OnOpenCamera : MessengerViewEvent()

    object OnRecordVoice : MessengerViewEvent()

    data class OnOpenQuickMessagesPopUp(val query: String, val count: Int) : MessengerViewEvent()

    data class OnInputQuickMessages(val data: QuickMessageModel) : MessengerViewEvent()

    data class OnCallAudio(val userId: String = "") : MessengerViewEvent()

    data class OnCallVideo(val userId: String = "") : MessengerViewEvent()

    data class OnCallGroup(val roomId: String = "") : MessengerViewEvent()

    object OnOpenMessagesPinned : MessengerViewEvent()

    data class OnOpenBotCommand(val threadId: Long, val botId: String, val botName: String = "") : MessengerViewEvent()

    data class OnOpenBotList(val threadId: Long, val botId: String = "") : MessengerViewEvent()

    data class OnShowNegativeSnackBar(val text: String) : MessengerViewEvent()

    data class OnShowPositiveSnackBar(val text: String) : MessengerViewEvent()

    data class OnViewImages(val data: List<String>, val index: Int = 0) : MessengerViewEvent()

    data class OnOpenConversation(val conversationId: String? = null, val userId: String? = null) :
        MessengerViewEvent()

    data class OnOpenSubthread(val subthreadId: Long, val messageId: Int? = null) :
        MessengerViewEvent()

    data class OnGoToOriginalMessage(val threadId: String, val messageId: Int, val isSubThread: Boolean = false) :
        MessengerViewEvent()

    object OnCreateMeeting : MessengerViewEvent()

    data class OnOpenSetting(val conversationId: String, val isGroup: Boolean) :
        MessengerViewEvent()

    data class OnOpenUserProfile(val userId: String) : MessengerViewEvent()

    data class OnWarning(val message: String) : MessengerViewEvent()

    data class OnOpenMessageAction(val messageId: Int) : MessengerViewEvent()

    data class OnOpenAvatarUserAction(val userId: String) : MessengerViewEvent()

    data class OnOpenMentionUserAction(val userId: String) : MessengerViewEvent()

    data class OnOpenLink(val link: String) : MessengerViewEvent()

    data class OnOpenPhone(val phoneNumber: String) : MessengerViewEvent()

    data class OnOpenEmail(val email: String) : MessengerViewEvent()

    data class OnOpenMedia(val id: String, val thumb: String?) : MessengerViewEvent()

    data class OnOpenLocalMedia(
        val viewerMedia: List<MediaViewerDeepLink.Media>,
        val position: Int
    ) : MessengerViewEvent()

    data class OnWatchMeetingFile(val url: String) : MessengerViewEvent()

    data class OnOpenUserSeen(
        val totalMember: Int,
        val totalSeen: List<MessengerUserViewData>
    ) : MessengerViewEvent()

    data class OnOpenUserReaction(val messageId: Int) : MessengerViewEvent()

    data class OnDownloadFile(val url: String, val name: String) : MessengerViewEvent()

    data class OnPlayVoice(val url: String) : MessengerViewEvent()

    object OnPauseVoice : MessengerViewEvent()

    data class OnMessageForwardAction(val messageIds: List<Int>) : MessengerViewEvent()

    data class OnMessageDeleteAction(val message: List<MessageModel>) : MessengerViewEvent()

    data class OnAddVote(val messageId: Int) : MessengerViewEvent()

    data class OnShowVotedUser(val messageId: Int, val voteId: String, val voteCount: Int) :
        MessengerViewEvent()
    object OnScrollToBottomDelaySecond : MessengerViewEvent()

    data class OnScrollToMessageId(val messageId: Int) : MessengerViewEvent()

    data class OnScrollToBottom(val lastMessageId: Int) : MessengerViewEvent()

    data class OnUnPinMessage(val messageId: Int) : MessengerViewEvent()

    data class OnClickErrorMessage(val message: MessageModel) : MessengerViewEvent()

    data class OnMessageCreateTask(val conversation: ConversationModel, val message: MessageModel) : MessengerViewEvent()

    data class OnPartnerProfile(val user: MessageUserModel) : MessengerViewEvent()

    object OnClearedInputMessageLayout : MessengerViewEvent()

    object OnSendMessage : MessengerViewEvent()

    data class OnOpenCombineActionNote(val messsage: MessageModel) : MessengerViewEvent()

    data class OnOpenActionNote(val messsage: MessageModel) : MessengerViewEvent()

    data class OnAddDynamicMessageBotCommand(val botCommand: MessageBotCommandModel, val botId: String, val botName: String) : MessengerViewEvent()
}
