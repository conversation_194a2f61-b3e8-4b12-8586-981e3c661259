package com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerExceptionHandler

/**
 * Created by bacnd on 21/06/2022.
 */
internal class EnableNotifyConversationUseCase(
    private val messengerRepository: MessengerRepository,
    private val exceptionHandler: MessengerExceptionHandler
) {
    suspend operator fun invoke(conversationId: Long): Result<Unit> {
        return try {
            messengerRepository.toggleNotifyConversation(conversationId)
            Result.Success(Unit)
        } catch (e: Exception) {
            messengerRepository.toggleNotifyConversationCache(conversationId)
            Result.Error(exceptionHandler(e))
        }
    }
}
