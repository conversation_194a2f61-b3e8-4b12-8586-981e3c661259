package com.gg.gapo.feature.messenger.presentation.conversation

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoAlertBottomSheetFragment
import com.gg.gapo.core.ui.bottomsheet.lifecycleOwner
import com.gg.gapo.core.ui.image.thumbpattern.GapoThumbPattern
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbar
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.livedata.debounce
import com.gg.gapo.core.utilities.view.span.Spanny
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderType
import com.gg.gapo.feature.messenger.presentation.conversation.action.AddToFolderBottomFragment
import com.gg.gapo.feature.messenger.presentation.conversation.action.ConversationBottomSheetFragment
import com.gg.gapo.feature.messenger.presentation.conversation.action.FolderLongPressBottomFragment
import com.gg.gapo.feature.messenger.presentation.conversation.adapter.ConversationAdapter
import com.gg.gapo.feature.messenger.presentation.conversation.adapter.ConversationAdapterDataObserver
import com.gg.gapo.feature.messenger.presentation.conversation.model.ConversationViewData
import com.gg.gapo.feature.messenger.presentation.conversation.model.FolderViewData
import com.gg.gapo.feature.messenger.presentation.conversation.viewmodel.ConversationViewEvent
import com.gg.gapo.feature.messenger.presentation.conversation.viewmodel.ConversationViewModel
import com.gg.gapo.feature.messenger.presentation.conversation.viewmodel.ConversationViewState
import com.gg.gapo.feature.messenger.presentation.helper.Constant
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.SubThreadSetting
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.SubthreadMessengerActivity
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.openSubThreadAction
import com.gg.gapo.feature.messenger.utils.copy
import com.gg.gapo.feature.messenger.utils.navToCallGroup
import com.gg.gapo.feature.messenger.utils.navToConversation
import com.gg.gapo.feature.messenger.utils.navToMessage
import com.gg.gapo.feature.messenger.utils.navToSubthread
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.ConversationFragmentBinding
import com.gg.gapo.messenger.presentation.events.ConversationDefaultUnRead
import com.gg.gapo.messenger.presentation.features.create.CreateConversationActivity
import com.gg.gapo.messenger.presentation.features.folder.FolderListActivity
import com.gg.gapo.messenger.presentation.features.search.SearchActivity
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import ru.whalemare.sheetmenu.ActionItem
import ru.whalemare.sheetmenu.SheetMenu
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 21/06/2022
 */
class ConversationFragment :
    Fragment(),
    ConversationBottomSheetFragment.Listener,
    FolderLongPressBottomFragment.Listener,
    AddToFolderBottomFragment.Listener {

    private var binding by autoCleared<ConversationFragmentBinding> {
        it.listConversation.adapter = null
    }
    private val conversationViewModel by viewModel<ConversationViewModel>()
    private var glideRequests by autoCleared<GlideRequests>()

    private var conversationAdapter by autoCleared<ConversationAdapter>()

    private val VIDEO_CALL_PERMISSION =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.BLUETOOTH_CONNECT
            )
        } else {
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO
            )
        }

    private val videoCallPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (!result.values.contains(false)) {
                if (conversationViewModel.threadForGroupCall == null || conversationViewModel.roomIdForGroupCall.isNullOrEmpty()) {
                    return@registerForActivityResult
                }
                navToCallGroup(
                    roomId = conversationViewModel.roomIdForGroupCall.orEmpty(),
                    thread = conversationViewModel.threadForGroupCall!!,
                    userIds = emptyList()
                )
                conversationViewModel.threadForGroupCall = null
                conversationViewModel.roomIdForGroupCall = null
            } else {
                onDeniedPermission()
            }
        }

    private fun onDeniedPermission() {
        makeNormalSnackbar(GapoStrings.shared_permission_denied_msg, isLineLimited = false)?.show()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ConversationFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.conversationViewModel = conversationViewModel
        glideRequests = GapoGlide.with(this)
        conversationViewModel.setUp(requireContext())
        initViews()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val viewLifecycleOwner = viewLifecycleOwner

        conversationViewModel.typingLiveData.observe(
            viewLifecycleOwner
        ) {
            val conversationId = it.first
            val userTyping = it.second
            conversationViewModel.addUserTyping(conversationId, userTyping)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                conversationViewModel.foldersFlow
                    .collectLatest {
                        updateFolderTab(it)
                    }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                conversationViewModel.conversationsFlow
                    .collectLatest {
                        conversationAdapter.submitData(it)
                        Timber.d("conversationAdapter.submitData(it)")
                    }
                if (isActive) {
                    val type =
                        binding.tabFolder.getTabAt(binding.tabFolder.selectedTabPosition)?.tag as? FolderType
                            ?: FolderType.Default
                    conversationViewModel.fetchPartners(type)
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                conversationAdapter.loadStateFlow.collectLatest { loadStates ->
                    if ((loadStates.append is LoadState.NotLoading && loadStates.append.endOfPaginationReached) || loadStates.refresh is LoadState.Error) {
                        if (conversationAdapter.itemCount < 1) conversationViewModel.setState(
                            ConversationViewState.Empty
                        )
                        else {
                            conversationViewModel.setState(ConversationViewState.Done)
                        }
                    } else {
                        conversationViewModel.setState(ConversationViewState.Done)
                    }

                    //  if (loadStates.refresh is LoadState.NotLoading)
                    if (loadStates.mediator?.refresh is LoadState.NotLoading || loadStates.mediator?.refresh is LoadState.Error) {
                        binding.swipeRefreshLayout.isRefreshing = false
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                // TODO tim cach move doan nay vao ViewModel
                while (isActive) {
                    val type =
                        binding.tabFolder.getTabAt(binding.tabFolder.selectedTabPosition)?.tag as? FolderType
                            ?: FolderType.Default
                    conversationViewModel.fetchTimeStampUserOnline(type)
                    delay(Constant.ONLINE_STATUS_MAX_TIME_1_MINUTE)
                }
            }
        }

        conversationViewModel.isSwipeRefreshLayoutVisibleLiveData.observe(viewLifecycleOwner) {
            binding.swipeRefreshLayout.isRefreshing = it
        }

        conversationViewModel.viewEventLiveData
            .observe(
                viewLifecycleOwner,
                EventObserver {
                    onViewEvent(it)
                }
            )

        conversationViewModel.userProfileLiveData
            .observe(viewLifecycleOwner) {
                // TODO sua thanh binding livedata cho TextView
                binding.textName.text = it.displayName
                // TODO sua thanh binding livedata cho GapoAvatarImageView
                binding.imageAvatar.loadCircle(
                    glideRequests,
                    GapoThumbPattern.AVATAR_MEDIUM_SIZE.parse(it.avatar, it.avatarThumbPattern),
                    it.displayName,
                    GapoAutoDimens._32dp
                )
            }

        conversationViewModel.navToConversationEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver { conversation ->
                    if (conversation.unreadCount > 0) {
                        navToMessage(
                            conversation.id.toString(),
                            conversation.firstUnreadMessageId.toString()
                        )
                    } else {
                        navToConversation(conversation.id.toString())
                    }
                }
            )

        conversationViewModel.navToSubConversationEventLiveData
            .debounce()
            .observe(
                viewLifecycleOwner,
                EventObserver { conversationId ->
                    navToSubthread(
                        conversationId
                    )
                }
            )
    }

    override fun onResume() {
        super.onResume()
        conversationViewModel.fetchFolders()
    }

    override fun onClickButtonNotificationConversation(
        conversationId: Long,
        isTurnOffNotification: Boolean
    ) {
        conversationViewModel.onClickButtonNotificationConversation(
            conversationId,
            isTurnOffNotification
        )
    }

    override fun onClickButtonPinConversation(conversationId: Long, isPinned: Boolean) {
        conversationViewModel.onClickButtonPinConversation(conversationId, isPinned)
    }

    override fun onClickButtonDeleteConversation(conversationId: Long) {
        onClickDeleteConversationBottomSheet(conversationId)
    }

    override fun onClickButtonMarkUnRead(conversationId: Long, unread: Boolean) {
        conversationViewModel.onClickButtonMarkUnReadConversation(conversationId, unread)
    }

    override fun onClickButtonAddToFolder(conversationId: Long, currentFolderAlias: String) {
        AddToFolderBottomFragment.newInstance(conversationId, currentFolderAlias)
            .show(childFragmentManager, AddToFolderBottomFragment.TAG)
    }

    override fun onClickButtonDeleteFromFolder(conversationId: Long, currentFolderAlias: String) {
        conversationViewModel.deleteFromFolder(conversationId, currentFolderAlias)
    }

    private fun initViews() {
        val linearLayoutManager = LinearLayoutManager(context)
        binding.listConversation.apply {
            itemAnimator = null
            setHasFixedSize(true)
            setItemViewCacheSize(4)
            layoutManager = linearLayoutManager
            setRecycledViewPool(
                RecyclerView.RecycledViewPool()
                    .apply {
                        setMaxRecycledViews(R.layout.conversation_item, 8)
                    }
            )
            adapter = ConversationAdapter(
                context,
                viewLifecycleOwner,
                conversationViewModel,
                glideRequests
            )
                .also { conversationAdapter = it }
        }

        binding.tabFolder.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                conversationViewModel.selectFolder(tab.tag as FolderType)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}

            override fun onTabReselected(tab: TabLayout.Tab) {
                scrollToTop()
            }
        })

        conversationAdapter.registerAdapterDataObserver(
            ConversationAdapterDataObserver(linearLayoutManager, conversationViewModel)
        )
    }

    private fun onViewEvent(event: ConversationViewEvent) {
        when (event) {
            is ConversationViewEvent.OnScrollToTopPosition -> {
                scrollToTop()
            }

            is ConversationViewEvent.OnOpenMessengerFolder -> {
                // val currentFolder = binding.tabFolder.getTabAt(binding.tabFolder.selectedTabPosition)?.tag as? FolderType ?: FolderType.Default
                // MessengerFolderActivity.start(requireContext(), currentFolder.alias)

                startForResult.launch(Intent(requireContext(), FolderListActivity::class.java))
            }

            is ConversationViewEvent.OnCreateConversation -> {
                GAPOAnalytics.getInstance(requireContext())
                    .logEventCreate(screenName = GAPOAnalyticsEvents.SCREEN_CHAT_CONVERSATION)
                CreateConversationActivity.start(requireContext())
            }

            is ConversationViewEvent.OnOpenSearch -> {
                SearchActivity.start(requireContext())
            }

            ConversationViewEvent.OnOpenSearchMention -> {
                SearchActivity.start(requireContext(), isOnlyMention = true)
            }

            is ConversationViewEvent.OnShowSnackBarRes -> {
                makeNormalSnackbar(event.stringRes)?.show()
            }

            is ConversationViewEvent.OnShowSnackBarMessage -> {
                makeNormalSnackbar(event.message)?.show()
            }

            is ConversationViewEvent.OnClickDeleteConversation -> {
                onClickDeleteConversation(event.conversation)
            }

            is ConversationViewEvent.OnLongClickConversation -> {
                onLongClickConversation(event.conversationId)
            }

            is ConversationViewEvent.OnLongClickSubConversation -> {
                onLongClickSubConversation(event.conversationId)
            }

            is ConversationViewEvent.OnRefreshData -> {
                conversationAdapter.refresh()
            }

            is ConversationViewEvent.OnShowNormalSnackBar -> {
                val checkmarkCircleDrawable = ContextCompat.getDrawable(
                    requireContext(),
                    GapoDrawables.ic24_fill_checkmark_circle
                )
                checkmarkCircleDrawable?.setTint(
                    ContextCompat.getColor(
                        requireContext(),
                        GapoColors.accentPrimary
                    )
                )
                makeNormalSnackbar(
                    message = event.text,
                    leftDrawable = checkmarkCircleDrawable
                )?.showOnTop()
            }

            is ConversationViewEvent.OnClickSubThreadConversation -> {
                conversationViewModel.getConversationById(event.conversation.id) { conversation ->
                    conversationViewModel.toggleSubThreadLockOption(
                        conversation.id,
                        conversation.collabId.orEmpty()
                    )
                }
            }

            is ConversationViewEvent.OnErrorShowSnackBarMessage -> {
                makeNegativeSnackbar(event.message)?.show()
            }

            is ConversationViewEvent.onClickGroupCall -> {
                videoCallPermissionLauncher.launch(VIDEO_CALL_PERMISSION)
            }
        }
    }

    private fun onLongClickConversation(conversationId: Long) {
        val folderSelected =
            binding.tabFolder.getTabAt(binding.tabFolder.selectedTabPosition)?.tag as? FolderType
                ?: FolderType.Default
        ConversationBottomSheetFragment.newInstance(conversationId, folderSelected.alias)
            .show(childFragmentManager, ConversationBottomSheetFragment.TAG)
    }

    private fun onLongClickSubConversation(conversationId: Long) {
        conversationViewModel.getConversationById(conversationId) { conversation ->
            context?.openSubThreadAction(
                fragmentManager = childFragmentManager,
                conversation = conversation,
                title = conversation.name,
                isShowLockSubThread = true
            ) {
                when (it) {
                    SubThreadSetting.ORIGINAL_MESSAGE -> {
                        navToMessage(
                            conversationId = conversation.parentId.toString(),
                            messageId = conversation.rootMessageId.toString()
                        )
                    }

                    SubThreadSetting.NOTIFICATION -> {
                        conversation.parentId?.let {
                            conversationViewModel.toggleNotification(
                                conversation.parentId,
                                conversation.id
                            ) {
                            }
                        }
                    }

                    SubThreadSetting.LOCK -> {
                        conversationViewModel.toggleSubThreadLockOption(
                            conversationId,
                            conversation.collabId.orEmpty()
                        )
                    }

                    SubThreadSetting.COPY_LINK_SUB_THREAD -> {
                        val webDeepLink =
                            SubthreadMessengerActivity.getWebDeepLinkUrl() + conversationId
                        requireContext().copy(url = webDeepLink)
                    }
                }
            }
        }
    }

    private fun onClickDeleteConversation(conversationViewData: ConversationViewData) {
        // TODO ko dung Spanny nua
        val label = Spanny()
        label.append(resources.getString(GapoStrings.messenger_title_delete_conversation_warning_spanny))
            .append(" ${conversationViewData.name} ", StyleSpan(Typeface.BOLD))
            .append("?")
        val actionsItems = ArrayList<ActionItem>()
        actionsItems.add(
            ActionItem(
                R.id.action_remove_1_way,
                getString(GapoStrings.messenger_delete_lv1),
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_remove_conversation_1_way)
            )
        )
        // TODO ko dung SheetMenu
        SheetMenu(
            title = label,
            actions = actionsItems,
            onClick = {
                when (it.id) {
                    R.id.action_remove_1_way -> {
                        conversationViewModel.deleteConversation(conversationViewData.id)
                    }
                }
            },
            showIcons = true
        ).show(requireContext(), lifecycle)
    }

    private fun onClickDeleteConversationBottomSheet(conversationId: Long) {
        // TODO dung GapoAlertDialogTheme
        AlertDialog.Builder(requireContext(), R.style.AlertDialogThemeBlackWhite).apply {
            setTitle(GapoStrings.messenger_remove_1_way_conversation_title)
            setMessage(GapoStrings.messenger_remove_1_way_conversation_description)
            setNegativeButton(GapoStrings.messenger_remove_mess_alert_cancel) { dialog, _ ->
                dialog.dismiss()
            }
            setPositiveButton(GapoStrings.messenger_remove_mess_alert_remove) { dialog, _ ->
                conversationViewModel.deleteConversation(conversationId)
                dialog.dismiss()
            }
            show()
        }
    }

    private fun updateFolderTab(folders: List<FolderViewData>) {
        val tabFolder = binding.tabFolder
        folders.forEachIndexed { index, folder ->
            // xoa so tab thua cuoi cung cua list
            if (tabFolder.tabCount > folders.size) {
                for (pos in folders.size until tabFolder.tabCount) tabFolder.removeTabAt(pos)
            }

            val currentTab = tabFolder.getTabAt(index) ?: tabFolder.newTab()
            currentTab
                .setText(folder.name)
                .setTag(folder.alias)
                .apply {
                    if (!(this.tag is FolderType.Default || folder.unreadCount == 0)) {
                        orCreateBadge.number = folder.unreadCount
                        orCreateBadge.maxCharacterCount = 3
                        orCreateBadge.backgroundColor =
                            ContextCompat.getColor(requireContext(), GapoColors.red)
                    } else {
                        removeBadge()
                    }

                    val isDefaultFolder =
                        this.tag is FolderType.Default || this.tag is FolderType.Unread ||
                            this.tag is FolderType.SubThread

                    if (!isDefaultFolder) {
                        this.view.setOnLongClickListener {
                            FolderLongPressBottomFragment.newInstance(folder.alias.alias)
                                .show(childFragmentManager, FolderLongPressBottomFragment.TAG)
                            return@setOnLongClickListener false
                        }
                    }
                }

            if (tabFolder.getTabAt(index) == null) {
                // them moi
                tabFolder.addTab(currentTab)
            }

            if (folder.alias == FolderType.Unread) {
                ConversationDefaultUnRead(folder.unreadCount).postEvent()
            }
        }
    }

    private val startForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val aliasIds =
                    result.data?.getStringArrayListExtra(FolderListActivity.TAG).orEmpty()
                if (aliasIds.isNotEmpty()) {
                    conversationViewModel.sortFolder(aliasIds)
                }
            }

            // refresh tab folder current selected
            conversationAdapter.refresh()
        }

    private fun scrollToTop() {
        try {
            (binding.listConversation.layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(
                0,
                0
            )
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    companion object {
        fun newInstance() = ConversationFragment()
    }

    override fun onClickButtonDeleteFolder(alias: String) {
        GapoAlertBottomSheetFragment.Builder()
            .setIconRes(R.drawable.ic_messenger_alert_delete)
            .setTitleRes(GapoStrings.messenger_folder_list_delete_title)
            .setDescriptionRes(GapoStrings.messenger_folder_list_delete_description)
            .setFirstButtonStylesRes(com.gg.gapo.core.ui.R.style.GapoButton_Medium_BgSecondary)
            .setFirstButtonTextRes(GapoStrings.messenger_group_leave_no)
            .setSecondButtonStylesRes(com.gg.gapo.core.ui.R.style.GapoButton_Medium_NegativeSecondary)
            .setSecondButtonTextRes(GapoStrings.messenger_remove_mess_alert_remove)
            .setListener(object :
                    GapoAlertBottomSheetFragment.Listener {
                    override fun onClickSecondButton(companionObject: Any?) {
                        conversationViewModel.deleteFolder(alias)
                    }

                    override fun onClickFirstButton(companionObject: Any?) {
                    }
                })
            .create()
            .lifecycleOwner(viewLifecycleOwner)
            .show(
                childFragmentManager,
                GapoAlertBottomSheetFragment.TAG
            )
    }

    override fun onClickButtonEditFolder(alias: String, avatar: String, name: String) {
        val folder = com.gg.gapo.messenger.domain.models.FolderModel(
            alias = alias,
            avatar = avatar,
            name = name
        )
        val intent = Intent(requireContext(), FolderListActivity::class.java).apply {
            this.putExtra(FolderListActivity.TAG, folder)
        }
        startForResult.launch(intent)
    }

    override fun onClickMoveToFolder(
        conversationId: Long,
        newFolderAlias: String,
        newFolderName: String
    ) {
        conversationViewModel.addToFolder(conversationId, newFolderAlias, newFolderName)
    }
}
