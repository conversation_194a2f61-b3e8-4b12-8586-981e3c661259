package com.gg.gapo.feature.messenger.data.messenger.message

import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.ConversationCache
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.ConversationEntity
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.mapToEntity
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.response.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.response.mapToMessageModel
import com.gg.gapo.feature.messenger.data.messenger.message.cache.MessageCache
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.*
import com.gg.gapo.feature.messenger.data.messenger.message.paging.MessageMediator
import com.gg.gapo.feature.messenger.data.messenger.message.paging.MessagePagingSource
import com.gg.gapo.feature.messenger.data.messenger.message.remote.MessageRemote
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.StatusMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.*
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.MessageCreateResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.ReportViolationResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmDispatcher
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmProvider
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.*
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageMultipleTargetRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.mapToRequestDto
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageBodyType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageMediaUploadStatus
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageSendStatus
import com.gg.gapo.feature.messenger.logger.MessengerLogger
import com.gg.gapo.feature.messenger.utils.MessengerLog
import com.gg.gapo.messenger.data.sources.models.ChatBotDto
import com.gg.gapo.messenger.data.sources.models.bases.ListDataResponseModel
import com.gg.gapo.messenger.data.sources.models.mapToDomain
import com.gg.gapo.messenger.data.sources.remote.response.SearchMessageResponse.Links
import com.gg.gapo.messenger.domain.models.ChatBotModel
import com.gg.gapo.messenger.domain.models.SearchMessage
import io.realm.RealmList
import io.realm.Sort
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import timber.log.Timber

internal class MessageRepositoryImpl(
    private val conversationCache: ConversationCache,
    private val messageRemote: MessageRemote,
    private val messageCache: MessageCache,
    private val coroutineDispatchers: CoroutineDispatchers,
    private val realmDispatcher: RealmDispatcher,
    private val userManager: UserManager,
    private val messengerLogger: MessengerLogger,
    private val realmProvider: RealmProvider
) : MessageRepository {

    // TODO khi logout cần set lại về 0 vì MessageRepositoryImpl là singleton
    private val initialMessageIdFlow = MutableStateFlow(0)

    override suspend fun jumpTo(messageId: Int) {
        MessengerLog.logeE("Paging repository jump $messageId")
        initialMessageIdFlow.emit(messageId)
    }

    @OptIn(ExperimentalPagingApi::class, ExperimentalCoroutinesApi::class)
    override fun messagesFlow(conversationId: Long): Flow<PagingData<MessageModel>> {
        return initialMessageIdFlow.flatMapLatest { messageId ->
            val conversationCached = conversationCache.findBy(conversationId)
            val conversationModel = conversationCached?.mapToDomain()
            val messageIdInit = if (messageId == 0) {
                // co nen dung conversationCache.findBy(conversationId)?.messages sort lấy messageId max
                if (conversationModel?.isSubthread == true) {
                    conversationCached.messages?.where()?.isNull("subThread")?.findAll()
                        ?.max(MessageEntity.ID)?.toInt() ?: 0
                } else {
                    conversationCached?.messages?.max(MessageEntity.ID)?.toInt() ?: 0
                }
            } else {
                messageId
            }
            MessengerLog.logeE("Paging init messageId $messageIdInit messageId: $messageId conversationModel?.isSubthread ${conversationModel?.isSubthread}")
            Pager(
                config = PagingConfig(
                    pageSize = 32,
                    prefetchDistance = 64,
                    initialLoadSize = 32,
                    enablePlaceholders = false
                ),
                initialKey = messageIdInit,
                remoteMediator = MessageMediator(
                    conversationId,
                    messageIdInit,
                    conversationCache,
                    messageCache,
                    messageRemote,
                    coroutineDispatchers,
                    realmDispatcher,
                    this,
                    (conversationModel?.settings?.deleteMsgAfterDays ?: 0) > 0
                ),
                pagingSourceFactory = {
                    MessagePagingSource(
                        conversationId,
                        conversationCache,
                        messageCache,
                        realmDispatcher,
                        userManager,
                        conversationModel,
                        messageIdInit
                    )
                }
            ).flow
                .catch { }
        }.flowOn(realmDispatcher)
    }

    private suspend fun setMessageIdsPinned(conversationId: Long, messageIdsPinned: List<Int>) {
        messageCache.setMessageIdsPinned(conversationId, messageIdsPinned)
    }

    override suspend fun fetchPinCollections(
        conversationId: Long,
        isSave: Boolean
    ): List<MessageModel> {
        return try {
            withContext(realmDispatcher) {
                // val conversationEntity = conversationCache.findBy(conversationId)
                // val messageCount = conversationEntity?.messages?.max(MessageEntity.ID)?.toInt() ?: 0

                withContext(coroutineDispatchers.io) {
                    val messagesPinedModel = messageRemote.fetchPinCollections(
                        conversationId,
                        Int.MAX_VALUE
                    ).data?.mapToDomain() ?: listOf()

                    if (messagesPinedModel.isNotEmpty()) {
                        if (isSave) {
                            withContext(realmDispatcher) {
                                // val conversationEntity = conversationCache.findBy(conversationId)
                                val messagesPinedEntity = messagesPinedModel.map {
                                    messageCache.findById(conversationId, it.id) ?: it.mapToEntity(
                                        conversationId
                                    )
                                    // val messagePinedEntity = it.mapToEntity(conversationId)
                                    // messagePinedEntity.conversation = conversationEntity
                                    // messagePinedEntity
                                }
                                messageCache.save(messagesPinedEntity)
                            }
                        }
                        setMessageIdsPinned(conversationId, messagesPinedModel.map { it.id })
                    } else {
                        setMessageIdsPinned(conversationId, listOf())
                    }

                    messagesPinedModel
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            emptyList()
        }
    }

    override fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>> =
        messageCache.messagesPinedFlow(conversationId).flowOn(realmDispatcher)

    override suspend fun pinMessage(messageId: Int, query: HashMap<String, String>) {
        return withContext(coroutineDispatchers.io) {
            messageRemote.pinMessage(messageId, query)
        }
    }

    override suspend fun unpinMessage(query: HashMap<String, String>) {
        return withContext(coroutineDispatchers.io) {
            messageRemote.unpinMessage(query)
        }
    }

    // TODO tạo DTO request
    override suspend fun reactMessage(conversationId: Long, messageId: Int, reactType: Int) {
        updateReactMessageCache(conversationId, messageId, reactType, userManager.userId)
        return withContext(coroutineDispatchers.io) {
            messageRemote.reactMessage(
                hashMapOf(
                    "thread_id" to conversationId.toString(),
                    "message_id" to messageId.toString(),
                    "react_type" to reactType.toString()
                )
            )
        }
    }

    override suspend fun createMessage(messageRequestModel: MessageRequestModel): MessageCreateResponse? {
        return withContext(coroutineDispatchers.io) {
            if (messageRequestModel.body?.type == MessageBodyType.POLL) {
                val body = messageRequestModel.body.metadata?.pollInformation?.mapToPollRequestBody(
                    messageRequestModel.threadId
                )
                body?.let {
                    val result = messageRemote.createPoll(body).data
                    result?.let {
                        val eventId = it.eventId
                        val threadId = messageRequestModel.threadId
                        val messageId = it.messageId
                        val clientId = messageRequestModel.clientId.toString()

                        MessageCreateResponse(eventId, threadId, messageId, clientId)
                    }
                }
            } else {
                messageRemote.createMessage(messageRequestModel.mapToRequestDto()).data
            }
        }
    }

    override suspend fun createMessageForMultipleTarget(messageMultipleTargetRequestModel: MessageMultipleTargetRequestModel): MessageCreateResponse? {
        return withContext(coroutineDispatchers.io) {
            messageRemote.createMessage(messageMultipleTargetRequestModel.mapToRequestDto()).data
        }
    }

    override suspend fun editMessageAction(conversationId: Long, messageId: Int) {
        withContext(realmDispatcher) {
            val messageRequestDto =
                messageCache.findById(conversationId, messageId)?.mapToRequestModel(conversationId)
                    ?.mapToRequestDto()

            messageRequestDto?.let {
                withContext(coroutineDispatchers.io) {
                    messageRemote.editMessage(messageId, messageRequestDto)
                }
            }
        }
    }

    override suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel) {
        withContext(realmDispatcher) {
            messageCache.editMessageCacheAction(messageRequestModel)
        }
    }

    override suspend fun createMessageMqtt(messageEntity: MessageEntity, conversationId: Long) {
        withContext(realmDispatcher) {
            try {
                val messageCached = messageCache.findById(conversationId, messageEntity.id)

                // messageCached != null có TH:
                // 1 - là event edit message
                // 2 - là message đã send saved trong realm
                // 3 - duplicate call func createMessageMqtt
                if (messageCached != null) { // && (messageCached.clientId == null || messageCached.clientId == 0L)) {
                    messageCache.update(conversationId, messageEntity)
                } else messageCache.save(messageEntity)

                // if la message action change pin
                if (messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody != null &&
                    messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody != 0
                ) {
                    fetchPinCollections(conversationId, true)
                }
            } catch (e: Exception) {
                messengerLogger.captureException(e)
                Timber.e(e)
            }

            // mqtt message, update read  them sender, ngươi gửi message này chắc chắn đã seen nó
            readAtMessageMqtt(
                messageEntity.conversation?.id ?: 0L,
                messageEntity.id,
                messageEntity.sender?.id.orEmpty()
            )
        }
    }

    override suspend fun saveMessageCreatorRequest(messageEntity: MessageEntity) {
        withContext(realmDispatcher) {
            messageCache.save(messageEntity)
        }
    }

    override suspend fun deleteMessageError(conversationId: Long, createAt: Long) {
        withContext(realmDispatcher) {
            messageCache.deleteMessageError(conversationId, createAt)
        }
    }

    override suspend fun autoDeleteMessages(threadId: Long) =
        withContext(realmDispatcher) {
            messageCache.autoDeleteMessages(threadId)
        }

    override suspend fun existAutoDeleteMessages(threadId: Long): Int {
        return withContext(realmDispatcher) {
            messageCache.existAutoDeleteMessage(threadId)
        }
    }

    override suspend fun fetchBotById(botId: String): ChatBotModel {
        return withContext(coroutineDispatchers.io) {
            messageRemote.fetchBotById(botId).data?.mapFromEntity() ?: ChatBotModel()
        }
    }

    override suspend fun getMessageRequestModelError(
        conversationId: Long,
        createAt: Long
    ): MessageRequestModel? {
        return withContext(realmDispatcher) {
            val messageRequest = messageCache.getMessageRequestError(conversationId, createAt)
                ?.mapToRequestModel(conversationId)
            messageRequest?.let { messageRequestModel ->

                messageRequestModel.body?.messageImages?.forEach {
                    it.statusUpLoading = MessageMediaUploadStatus.UPLOADING
                }

                messageRequestModel.body?.messageVideos?.forEach {
                    it.statusUpLoading = MessageMediaUploadStatus.UPLOADING
                }

                messageRequestModel.body?.messageFiles?.forEach {
                    it.statusUpLoading = MessageMediaUploadStatus.UPLOADING
                }

                messageRequestModel.body?.messageVoices?.forEach {
                    it.statusUpLoading = MessageMediaUploadStatus.UPLOADING
                }

                messageRequestModel.messageSendStatus = MessageSendStatus.SENDING
            }

            messageRequest
        }
    }

    override suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel) {
        withContext(realmDispatcher) {
            messageCache.updateMessageCreatorRequest(messageRequestModel)
        }
    }

    override suspend fun findByClientId(
        clientId: Long,
        conversationId: Long
    ): MessageRequestModel? {
        return withContext(realmDispatcher) {
            messageCache.findByClientId(clientId)?.mapToRequestModel(conversationId)
        }
    }

    override suspend fun deleteMessageMqtt(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    ) {
        withContext(realmDispatcher) {
            messageCache.deleteMessageMqtt(conversationId, messageId, level)
        }
    }

    override suspend fun editMessageMqtt(statusMqttDto: StatusMqttDto) {
        val conversationId = statusMqttDto.body?.threadId
        val messageId = statusMqttDto.body?.messageId

        // update body message, mqtt trả về chỗ này k có read nên chỉ cần bổ sung fetch/get
        // userVotes-pollInformationMetadataBody, pinnedMessageActionNoteDataChangedMetadataBody
        if (conversationId != null && messageId != null) {
            statusMqttDto.body?.body?.mapToDomain()?.let { bodyModel ->

                val isMeActor = statusMqttDto.body?.actorId == userManager.userId

                // update thong tin cho model con thieu
                fillBodyMessage(bodyModel, conversationId, messageId)

                messageCache.editMessageMqtt(conversationId, messageId, bodyModel, isMeActor)
            }
        }
    }

    // TODO chi dung RealmDispatcher cho luong Read/Write vao Database
    override suspend fun readAtMessageMqtt(conversationId: Long, messageId: Int, userId: String) {
        withContext(coroutineDispatchers.default) {
            var messageIdNeedRead = messageId
            val lastMessageSucceeded = withContext(realmDispatcher) {
                messageCache.getLastMessageSucceeded(conversationId)
            }
            lastMessageSucceeded?.let {
                // if messageIdNeedRead == 0 là from user typing event
                if (messageIdNeedRead == 0) messageIdNeedRead = lastMessageSucceeded.id

                // can check neu messageId > lastMessageId thi gan messageId = lastMessageId
                if (messageIdNeedRead > lastMessageSucceeded.id) messageIdNeedRead =
                    lastMessageSucceeded.id

                // hoac userId đã read messageId rồi thì return
                if (lastMessageSucceeded.read?.find { userRead -> userRead.id == userId } != null) {
                    return@withContext
                }

                val userSeenModel =
                    getUsersWithIds(listOf(userId)).firstOrNull() ?: return@withContext

                val results = withContext(realmDispatcher) {
                    val realm = realmProvider.retrieveRealm()
                    val messagesCached = messageCache.findByConversation(conversationId)
                        .sort(
                            MessageEntity.ID,
                            Sort.DESCENDING,
                            MessageEntity.CREATED_AT,
                            Sort.DESCENDING
                        )
                    if (messagesCached != null) {
                        realm.copyFromRealm(messagesCached)
                    } else {
                        emptyList()
                    }
                }

                if (results.isEmpty()) return@let
                for (result in results) {
                    result.read?.removeAll { it.userId == userId }
                    if (result.id == messageId && result.read?.firstOrNull { it.userId == userId } == null) {
                        result.read?.add(userSeenModel.mapToEntity())
                    }
                }

                withContext(realmDispatcher) {
                    messageCache.save(results)
                }

                // Following: RealmError null Exception backtrace: messageEntity.read?

//                messageCache.findByConversation(conversationId)
//                    .sort(MessageEntity.ID, Sort.DESCENDING, MessageEntity.CREATED_AT, Sort.DESCENDING)
//                    .createSnapshot()
//                    ?.forEach { messageEntity ->
//
//                        val userNeedDeleteManaged = messageEntity.read?.find { userManaged ->
//                            userManaged.id == userId
//                        }
//
//                        // xoa read message cu
//                        if (userNeedDeleteManaged != null) {
//                            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
//                                messageEntity.readsRemove(userNeedDeleteManaged)
//                            }
//                            return@forEach
//                        }
//
//                        // update read message moi
//                        if (messageEntity.id == messageIdNeedRead && messageEntity.uid.contains("_$messageIdNeedRead")) {
//                            // lay thong tin user moi
//                            val userSeenModel = getUsersWithIds(listOf(userId)).firstOrNull()
//
//                            userSeenModel?.let {
//                                realmProvider.retrieveRealm()
//                                    .executeTransactionAwait(realmDispatcher) { realm ->
//                                        messageEntity.readsAdd(realm.copyToRealmOrUpdate(userSeenModel.mapToEntity()))
//                                    }
//                            }
//                        }
//                    }
            }
        }
    }

    override suspend fun reactMessageMqtt(statusMqttDto: StatusMqttDto) {
        val conversationId = statusMqttDto.body?.threadId ?: 0L
        val messageId = statusMqttDto.body?.messageId ?: 0
        val reactType = statusMqttDto.body?.reactType ?: 0
        val reactUserId = statusMqttDto.body?.reactUserId.orEmpty()
        val previousReaction = statusMqttDto.body?.previousReaction ?: 0

        updateReactMessageCache(conversationId, messageId, reactType, reactUserId, previousReaction)
    }

    /**
     * Lọc những userId chưa có trong realm
     * Fetch info những userId đó, và save vao realm
     * Lấy lại info list user từ realm
     */
    override suspend fun getUsersWithIds(userIds: List<String>): List<MessageUserModel> {
        return withContext(realmDispatcher) {
            val userIdsNeedUpdate: ArrayList<String> = arrayListOf()

            // TODO query 1 phat list
            userIds.forEach { userId ->
                val userSeenEntity = messageCache.getUsersWithId(userId)
                if (userSeenEntity == null || userSeenEntity.displayName.isNullOrEmpty()) { // can them dk la currentTime - lastTimeUpdated > 1 week, 1 month
                    userIdsNeedUpdate.add(userId)
                }
            }
            if (userIdsNeedUpdate.isNotEmpty()) {
                fetchUsersWithIds(userIdsNeedUpdate)
            }

            messageCache.getUsersWithIds(userIds.toTypedArray()).map { it.mapToDomain() }
        }
    }

    /**
     * Fetch info userIds
     * save response to realm
     */
    override suspend fun fetchUsersWithIds(userIds: List<String>): List<MessageUserModel> {
        return withContext(coroutineDispatchers.io) {
            val usersSeenModel =
                messageRemote.fetchUsersWithIds(userIds.joinToString(",")).data?.map { it.mapToDomain() }
            withContext(realmDispatcher) {
                val usersSeenEntity = usersSeenModel?.map {
                    it.mapToEntity()
                }
                if (!usersSeenEntity.isNullOrEmpty()) {
                    messageCache.saveUsers(usersSeenEntity)
                    conversationCache.savePartners(usersSeenModel.map { it.mapToPartnerEntity() })
                }
            }
            usersSeenModel ?: emptyList()
        }
    }

    override suspend fun read(conversationId: Long, messageId: Int) =
        withContext(coroutineDispatchers.io) {
            messageRemote.read(conversationId, messageId)
        }

    override suspend fun markUnread(conversationId: Long, messageId: Int) =
        withContext(coroutineDispatchers.io) {
            messageRemote.markUnread(conversationId, messageId)
        }

    override suspend fun fetchPreviewLink(url: String): MessageModel.MessagePreviewLinkModel? =
        withContext(coroutineDispatchers.io) {
            messageRemote.fetchPreviewLink(url).data?.mapToDomain()
        }

    override suspend fun updateMessageCache(messageEntity: MessageEntity, conversationId: Long) {
        withContext(realmDispatcher) {
            val conversationCached = conversationCache.findBy(conversationId)
            messageEntity.conversation = conversationCached
            // conversationCached?.let { fillLinkMessage(messageEntity, it) }
            messageCache.save(messageEntity)
        }
    }

    override suspend fun updateMessageSubthreadMqtt(
        conversationModel: ConversationModel?,
        statusMqttDto: StatusMqttDto
    ) {
        val conversationId = statusMqttDto.body?.subthread?.parentId
        val subthreadId = statusMqttDto.body?.subthread?.id ?: 0L
        val messageId = statusMqttDto.body?.subthread?.rootMessageId
        val sender =
            statusMqttDto.body?.sender?.mapToDomain()?.mapToMessageUserEntity()?.mapToDomain()
        if (conversationId != null && messageId != null) {
            withContext(realmDispatcher) {
                val messageCached = messageCache.findById(conversationId, messageId)
                val subthreadModel = conversationCache.findBy(subthreadId)?.mapToDomain()
                messageCached?.let {
                    val messageModel = it.mapToDomain()
                    val commenters =
                        messageModel.subThread?.commenters?.toMutableList() ?: mutableListOf()
                    sender?.let { commenter ->
                        if (commenters.none { it.id == commenter.id }) {
                            commenters.add(commenter)
                        }
                    }
                    val updatedMessage = messageModel.copy(
                        subThread = statusMqttDto.body?.subthread?.mapToDomain()
                            ?.copy(commenters = commenters.toList()),
                        subThreadId = statusMqttDto.body?.subthread?.id ?: 0L
                    )

                    if (subthreadModel != null) {
                        conversationCache.update(subthreadModel.copy(referencedMessage = messageModel).mapToEntity())
                    } else {
                        conversationModel?.let {
                            conversationCache.update(it.copy(referencedMessage = messageModel).mapToEntity())
                        }
                    }

                    fillAndSaveMessageEntity(
                        updatedMessage.mapToEntity(conversationId),
                        conversationId
                    )
                }
            }
        }
    }

    override suspend fun updateMessageSubthreadMqtt(
        conversationId: Long?,
        messageId: Int?,
        sender: MessageUserModel?,
        subThreadModel: SubThreadModel?
    ) {
        if (conversationId != null && messageId != null) {
            withContext(realmDispatcher) {
                val messageCached = messageCache.findById(conversationId, messageId)
                messageCached?.let {
                    val messageModel = it.mapToDomain()
                    val commenters =
                        messageModel.subThread?.commenters?.toMutableList() ?: mutableListOf()
                    sender?.let { commenter ->
                        if (commenters.none { it.id == commenter.id }) {
                            commenters.add(commenter)
                        }
                    }
                    val updatedMessage = messageModel.copy(
                        subThread = subThreadModel
                            ?.copy(commenters = commenters.toList()),
                        subThreadId = subThreadModel?.id ?: 0L
                    )

                    fillAndSaveMessageEntity(
                        updatedMessage.mapToEntity(conversationId),
                        conversationId
                    )
                }
            }
        }
    }

    override suspend fun fetchMessageFirstPageInConversations(conversationsCached: List<ConversationEntity>) {
        // fetch remote
        // cache to realm
        conversationsCached.forEach { conversationCached ->
            withContext(realmDispatcher) {
                val conversationId = conversationCached.id
                val lastMessageId = conversationCached.messageCount
                val response = withContext(coroutineDispatchers.io) {
                    messageRemote.fetchMessages(
                        conversationId,
                        lastMessageId,
                        lastMessageId - 16
                    ) // fetch pageSize / 2 = 16
                }

                response.data?.let { messagesDto ->
                    val messagesModel =
                        withContext(coroutineDispatchers.default) { messagesDto.mapToDomain() }

                    // TODO optimize run song song
                    messagesModel
                        .filter {
                            it.threadId == conversationId
                        }
                        .forEach { messageModel ->
                            fillMessage(messageModel, conversationId)
                        }

                    val messagesEntity = messagesModel.mapToEntity(conversationId)
                    messagesEntity.forEach { messageEntity ->
//                        fillLinkMessage(messageEntity, conversationCached)
                        messageEntity.conversation = conversationCached
//                        val pinnedMessageId = messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody
//                        if (pinnedMessageId != null && pinnedMessageId != 0) {
//                            // query message co ID = pinnedMessageIdActionNoteDataChangedMetadataBody
//                            messageEntity.pinnedMessageActionNoteDataChangedMetadataBody =
//                                getMessageById(conversationId, pinnedMessageId)
//                        }
                    }
                    messageCache.updateFirstPage(conversationId, messagesEntity)
                }
            }
        }
    }

    override suspend fun getMessageById(conversationId: Long, messageId: Int): MessageEntity? {
        return try {
            if (messageId == 0) return null
            withContext(realmDispatcher) {
                val messageCached = messageCache.findById(conversationId, messageId)
                if (messageCached == null) {
                    val messageDto = withContext(coroutineDispatchers.io) {
                        messageRemote.fetchMessage(conversationId, messageId).data
                    }

                    var messageToSave: MessageEntity? = null
                    messageDto?.mapToDomain()?.let { messageModel ->
                        messageToSave =
                            fillMessage(messageModel, conversationId).mapToEntity(conversationId)
                    }
                    messageToSave?.let {
                        it.conversation = conversationCache.findBy(conversationId)
                        messageCache.save(it)
                    }
                    messageToSave
                } else {
                    messageCached
                }
            }
        } catch (e: Exception) {
            messengerLogger.captureException(e)
            null
        }
    }

    override suspend fun getMessageByIdNoSave(
        conversationId: Long,
        messageId: Int
    ): MessageEntity? {
        return try {
            if (messageId == 0) {
                return null
            }
            withContext(realmDispatcher) {
                val messageCached = messageCache.findById(conversationId, messageId)
                if (messageCached == null) {
                    val messageDto = withContext(coroutineDispatchers.io) {
                        messageRemote.fetchMessage(conversationId, messageId).data
                    }

                    var messageNoSave: MessageEntity? = null
                    messageDto?.mapToDomain()?.let { messageModel ->
                        messageNoSave =
                            fillMessage(messageModel, conversationId).mapToEntity(conversationId)
                    }
                    messageNoSave
                } else {
                    messageCached
                }
            }
        } catch (e: Exception) {
            messengerLogger.captureException(e)
            null
        }
    }

    override suspend fun getMessageCachedById(conversationId: Long, messageId: Int): MessageModel? =
        withContext(realmDispatcher) {
            messageCache.findById(conversationId, messageId)?.mapToDomain()
        }

    /**
     * Hàm này để lấy thông tin đầy đủ của user, do trong message nhiều field chỉ có userIds và 1 số field khác
     * ex: read, userVotes-pollInformationMetadataBody, pinnedMessageActionNoteDataChangedMetadataBody
     */
    override suspend fun fillMessage(
        messageModel: MessageModel,
        conversationId: Long
    ): MessageModel {
        try {
            if (messageModel.read.isNotEmpty()) {
                messageModel.read = getUsersWithIds(messageModel.read.map { it.id })
            }

            val commentersSubThread = messageModel.subThread?.commenters
            if (!commentersSubThread.isNullOrEmpty()) {
                messageModel.subThread.commenters =
                    getUsersWithIds(commentersSubThread.map { it.id })
            }

            fillBodyMessage(messageModel.body, conversationId, messageModel.id)
        } catch (e: Exception) {
            messengerLogger.captureException(e)
            Timber.e(e)
        }

        return messageModel
    }

    override suspend fun fillBodyMessage(
        messageBodyModel: MessageModel.MessageBodyModel,
        conversationId: Long,
        messageId: Int
    ): MessageModel.MessageBodyModel {
        return withContext(realmDispatcher) {
            try {
                if (!messageBodyModel.metadata?.pollInformation?.votes.isNullOrEmpty()) {
                    messageBodyModel.metadata?.pollInformation?.votes?.forEach { vote ->
                        if (vote.userVotes.isNotEmpty()) vote.userVotes =
                            getUsersWithIds(vote.userVotes.map { it.id })
                    }
                }

                if (messageBodyModel.metadata?.actionNoteDataChanged?.pinnedMessageId != null &&
                    messageBodyModel.metadata.actionNoteDataChanged.pinnedMessageId != 0
                ) {
                    messageBodyModel.metadata.actionNoteDataChanged.pinnedMessage =
                        getMessageByIdNoSave(
                            conversationId,
                            messageBodyModel.metadata.actionNoteDataChanged.pinnedMessageId
                        )?.mapToDomain()
                }
            } catch (e: Exception) {
                messengerLogger.captureException(e)
                Timber.e(e)
            }

            messageBodyModel
        }
    }

    override suspend fun fillAndUpdateLastMessagesCached(lastMessages: List<MessageEntity>) {
        withContext(realmDispatcher) {
            try {
                val lastMessagesNeedUpdate: MutableList<MessageEntity> = mutableListOf()
                lastMessages.forEach { lastMessage ->
                    var needUpdate = false
                    val conversationId = lastMessage.conversation?.id
                    if (conversationId != null) {
                        // lastMessage.read luon empty
                        if (!lastMessage.read.isNullOrEmpty()) {
                            val reads = RealmList<MessageUserEntity>()
                            reads.addAll(
                                getUsersWithIds(
                                    lastMessage.read?.map { it.id }
                                        ?: listOf()
                                ).map { it.mapToEntity() }
                            )

                            lastMessage.read = reads
                            needUpdate = true
                        }

                        if (lastMessage.pinnedMessageIdActionNoteDataChangedMetadataBody != null &&
                            lastMessage.pinnedMessageIdActionNoteDataChangedMetadataBody != 0
                        ) {
                            lastMessage.pinnedMessageActionNoteDataChangedMetadataBody =
                                getMessageById(
                                    conversationId,
                                    lastMessage.pinnedMessageIdActionNoteDataChangedMetadataBody
                                        ?: 0
                                )
                            needUpdate = true
                        }

                        if (lastMessage.replyToMsg != null && lastMessage.replyToMsg != 0) {
                            lastMessage.replyToMsgObjectBody =
                                getMessageById(conversationId, lastMessage.replyToMsg ?: 0)
                            needUpdate = true
                        }
                    }

                    if (needUpdate) lastMessagesNeedUpdate.add(lastMessage)
                }

                if (lastMessagesNeedUpdate.isNotEmpty()) messageCache.save(lastMessagesNeedUpdate)
            } catch (e: Exception) {
                messengerLogger.captureException(e)
                Timber.e(e)
            }
        }
    }

    override suspend fun fillAndUpdateMessagesCached(messages: List<MessageEntity>) {
        withContext(realmDispatcher) {
            try {
                val messagesNeedUpdate: MutableList<MessageEntity> = mutableListOf()
                messages.forEach { message ->
                    var needUpdate = false
                    val conversationId = message.conversation?.id
                    if (conversationId != null) {
                        if (!message.read.isNullOrEmpty()) {
                            val reads = RealmList<MessageUserEntity>()
                            reads.addAll(
                                getUsersWithIds(
                                    message.read?.map { it.id }
                                        ?: listOf()
                                ).map { it.mapToEntity() }
                            )

                            message.read = reads
                            needUpdate = true
                        }

                        if (!message.pollInformationMetadataBody?.votes.isNullOrEmpty()) {
                            message.pollInformationMetadataBody?.votes?.forEach { vote ->
                                if (vote.userVotes?.isNotEmpty() == true) {
                                    val userVotes = RealmList<MessageUserEntity>()
                                    userVotes.addAll(
                                        getUsersWithIds(
                                            vote.userVotes?.map { it.id }
                                                ?: listOf()
                                        ).map { it.mapToEntity() }
                                    )

                                    vote.userVotes = userVotes
                                    needUpdate = true
                                }
                            }
                        }

                        if (message.pinnedMessageIdActionNoteDataChangedMetadataBody != null &&
                            message.pinnedMessageIdActionNoteDataChangedMetadataBody != 0
                        ) {
                            message.pinnedMessageActionNoteDataChangedMetadataBody =
                                getMessageById(
                                    conversationId,
                                    message.pinnedMessageIdActionNoteDataChangedMetadataBody ?: 0
                                )
                            needUpdate = true
                        }

                        if (message.replyToMsg != null && message.replyToMsg != 0) {
                            message.replyToMsgObjectBody =
                                getMessageById(conversationId, message.replyToMsg ?: 0)
                            needUpdate = true
                        }
                    }

                    if (needUpdate) messagesNeedUpdate.add(message)
                }

                if (messagesNeedUpdate.isNotEmpty()) messageCache.save(messagesNeedUpdate)
            } catch (e: Exception) {
                messengerLogger.captureException(e)
                Timber.e(e)
            }
        }
    }

    override suspend fun fillAndSaveMessageEntity(
        messageEntity: MessageEntity,
        conversationId: Long
    ) {
        withContext(realmDispatcher) {
            try {
                if (!messageEntity.read.isNullOrEmpty()) {
                    val reads = RealmList<MessageUserEntity>()
                    reads.addAll(
                        getUsersWithIds(
                            messageEntity.read?.map { it.id }
                                ?: listOf()
                        ).map { it.mapToEntity() }
                    )

                    messageEntity.read = reads
                }

                if (messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody != null &&
                    messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody != 0
                ) {
                    messageEntity.pinnedMessageActionNoteDataChangedMetadataBody =
                        getMessageById(
                            conversationId,
                            messageEntity.pinnedMessageIdActionNoteDataChangedMetadataBody ?: 0
                        )
                }

                if (messageEntity.replyToMsg != null && messageEntity.replyToMsg != 0) {
                    messageEntity.replyToMsgObjectBody =
                        getMessageById(conversationId, messageEntity.replyToMsg ?: 0)
                }

                updateMessageCache(messageEntity, conversationId)
            } catch (e: Exception) {
                messengerLogger.captureException(e)
                Timber.e(e)
            }
        }
    }

    override suspend fun searchUsers(
        queries: Map<String, String>,
        groupId: String
    ): Pair<List<MessageUserModel>, Map<String, String>> =
        withContext(coroutineDispatchers.io) {
            messageRemote.searchUsersInGroup(groupId, queries)
        }

    override fun lastMessageFlow(conversationId: Long): Flow<MessageModel> =
        messageCache.lastMessageFlow(conversationId).flowOn(realmDispatcher)

    override suspend fun lastMessage(conversationId: Long): MessageModel? =
        messageCache.lastMessage(conversationId)

    override suspend fun fetchMessengerReactedUsers(
        messageId: Int,
        queries: Map<String, String>
    ): Pair<List<ReactedUserModel>, Map<String, String>> = withContext(coroutineDispatchers.io) {
        messageRemote.fetchMessengerReactedUsers(messageId, queries)
    }

    override suspend fun getSenderById(userId: String): MessageModel.SenderModel? =
        withContext(realmDispatcher) {
            messageCache.getSenderById(userId)?.mapToDomain()
        }

    override suspend fun sendMessagesToSave(body: SaveMessageRequestBody) {
        withContext(coroutineDispatchers.io) {
            messageRemote.sendMessagesToSave(body)
        }
    }

    override suspend fun deleteMessages(
        messageIds: List<Int>,
        conversationId: Long,
        level: MessageLevelDeleteType
    ) {
        withContext(realmDispatcher) {
            if (messageIds.size == 1) {
                messageCache.removeById(conversationId, messageIds.first(), level)
            } else {
                messageCache.removeByIds(conversationId, messageIds, level)
            }
        }

        withContext(coroutineDispatchers.io) {
            messageRemote.deleteMessages(messageIds, conversationId, level)
        }
    }

    override suspend fun createVote(
        conversationId: Long,
        messageId: Int,
        voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel
    ) {
        withContext(realmDispatcher) {
            messageCache.createVote(conversationId, messageId, voteAdded)
        }
        withContext(coroutineDispatchers.io) {
            val response = messageRemote.createVote(
                VoteRequestBody(
                    threadId = conversationId,
                    messageId = messageId,
                    body = PollVoteRequest(
                        title = voteAdded.title.orEmpty()
                    )
                )
            )

            response.data?.mapToDomain()?.mapToEntity(conversationId)?.let { message ->
                withContext(realmDispatcher) {
                    messageCache.update(conversationId, message)
                }
            }
        }
    }

    override suspend fun chooseVote(
        conversationId: Long,
        messageId: Int,
        voteId: String,
        isChoose: Boolean
    ) {
        // co the save cache trc o day
        withContext(coroutineDispatchers.io) {
            val body = ChooseVoteRequestBody(
                threadId = conversationId,
                messageId = messageId,
                voteId = voteId
            )
            val response =
                if (isChoose) messageRemote.chooseVote(body) else messageRemote.unChooseVote(body)

            val messageModel = response.data?.mapToDomain()

            messageModel?.let {
                fillMessage(messageModel, conversationId)

                messageModel.mapToEntity(conversationId).let { message ->
                    withContext(realmDispatcher) {
                        messageCache.update(conversationId, message)
                    }
                }
            }
        }
    }

    override suspend fun getUserVoted(
        conversationId: Long,
        messageId: Int,
        voteId: String
    ): List<MessageUserModel> {
        return withContext(realmDispatcher) {
            messageCache.getUserVoted(conversationId, messageId, voteId).map { it.mapToDomain() }
        }
    }

    override suspend fun fetchUsersVoted(queryMap: Map<String, String>): List<MessageUserModel> {
        return withContext(coroutineDispatchers.io) {
            messageRemote.fetchUsersVoted(queryMap).data?.mapToDomain() ?: listOf()
        }
    }

    override suspend fun sendTypingEvent(conversationId: Long) =
        withContext(coroutineDispatchers.io) {
            messageRemote.sendTypingEvent(conversationId)
        }

    override suspend fun blockUser(body: BlockUserRequestBody) =
        withContext(coroutineDispatchers.io) {
            messageRemote.blockUser(body)
        }

    override suspend fun fetchBotCommands(botId: String): List<MessageBotCommandModel> =
        withContext(coroutineDispatchers.io) {
            messageRemote.fetchBotCommands(botId).data?.map { it.mapToDomain() } ?: listOf()
        }

    override suspend fun fetchUserParticipants(threadId: Long): List<MessageUserParticipantsModel> {
        return withContext(realmDispatcher) {
            val conversation = conversationCache.findBy(threadId)
            if (conversation != null) {
                val isGroup =
                    ConversationType.getByType(conversation.type) == ConversationType.GROUP
                if (isGroup) {
                    withContext(coroutineDispatchers.io) {
                        val response =
                            messageRemote.fetchUserParticipants(threadId).data ?: listOf()

                        if (response.isNotEmpty()) {
                            withContext(realmDispatcher) {
                                // xoa db
                                conversationCache.deleteUserParticipants(threadId)
                                // save db
                                conversationCache.saveUserParticipants(
                                    response.map {
                                        it.mapToDomain().mapToEntity(threadId)
                                    }
                                )
                            }
                            return@withContext response.map { it.mapToDomain() }
                        } else {
                            return@withContext ArrayList<MessageUserParticipantsModel>()
                        }
                    }
                } else {
                    return@withContext ArrayList<MessageUserParticipantsModel>()
                }
            } else {
                return@withContext ArrayList<MessageUserParticipantsModel>()
            }
        }
    }

    override suspend fun getCreateAtLastMessageInConversationLocal(conversationId: Long): Long {
        return withContext(realmDispatcher) {
            messageCache.getCreateAtLastMessageInConversationLocal(conversationId)
        }
    }

    override suspend fun updateMessageTextDraft(
        conversationId: Long,
        messageDraft: MessageDraftModel?
    ) {
        withContext(realmDispatcher) {
            if (messageDraft == null) {
                messageCache.deleteMessageTextDraft(conversationId)
            } else {
                messageCache.updateMessageTextDraft(messageDraft.mapToEntity(conversationId))
            }
        }
    }

    private suspend fun updateReactMessageCache(
        conversationId: Long,
        messageId: Int,
        reactType: Int,
        reactUserId: String,
        previousReaction: Int = 0
    ) {
        messageCache.updateReactMessageCache(
            conversationId,
            messageId,
            reactType,
            reactUserId,
            userManager.userId,
            previousReaction
        )
    }

    override suspend fun searchBot(params: Map<String, Any>): ListDataResponseModel<ChatBotDto> {
        return messageRemote.searchBot(params)
    }

    override suspend fun getActionNotes(
        threadId: Long,
        lastCreatedAt: Long,
        prevCreatedAt: Long,
        middleCreatedAt: Long,
        pageSize: Int
    ) = withContext(coroutineDispatchers.io) {
        messageRemote.getActionNotes(
            threadId,
            lastCreatedAt,
            prevCreatedAt,
            middleCreatedAt,
            pageSize
        ).data.orEmpty()
            .map { it.mapToMessageModel() }
    }

    override suspend fun searchConversations(params: MutableMap<String, Any>): List<ConversationModel> {
        return withContext(coroutineDispatchers.io) {
            messageRemote.searchConversations(params).data?.map { it.mapFromConversationModel() }
                .orEmpty()
        }
    }

    override suspend fun searchMentionMessages(params: Map<String, Any>): Pair<Links?, List<SearchMessage>> {
        return withContext(coroutineDispatchers.io) {
            val result = messageRemote.searchMentionMessages(params)
            Pair(result.links, result.searchMessages.orEmpty().map { it.mapToDomain() })
        }
    }

    override suspend fun joinGroupCall(client: String, roomId: String): String {
        return withContext(coroutineDispatchers.io) {
            messageRemote.joinGroupCall(client = client, roomId = roomId)
        }
    }

    override suspend fun getRoomInfo(clientId: String, ids: List<String>): List<RoomModel> {
        return withContext(coroutineDispatchers.io) {
            messageRemote.getRoomInfo(clientId, ids).data.orEmpty().map { it.mapToDomain() }
        }
    }

    override suspend fun getSignedRequest(botId: String, threadId: String): String {
        return withContext(coroutineDispatchers.io) {
            messageRemote.getSignedRequest(
                botId = botId,
                threadId = threadId
            ).data?.signedRequest.orEmpty()
        }
    }

    override suspend fun reportViolation(body: ReportViolationRequestBody): ReportViolationResponse {
        return withContext(coroutineDispatchers.io) {
            messageRemote.reportViolation(body)
        }
    }

    override suspend fun getQuickMessages(): List<QuickMessageModel> {
        return withContext(coroutineDispatchers.io) {
            messageRemote.getQuickMessages().map { it.mapToDomain() }
        }
    }

    override suspend fun fetchUsersById(id: String): MessageUserModel? {
        return withContext(coroutineDispatchers.io) {
            messageRemote.fetchUsersById(id).data?.mapToDomain()
        }
    }
}
