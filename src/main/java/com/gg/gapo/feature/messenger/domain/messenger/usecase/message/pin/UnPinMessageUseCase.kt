package com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerExceptionHandler
import timber.log.Timber

/**
 * Created by bacnd on 01/07/2022.
 */
internal class UnPinMessageUseCase(
    private val messengerRepository: MessengerRepository,
    private val exceptionHandler: MessengerExceptionHandler
) {
    suspend operator fun invoke(conversationId: Long, messageId: Int): Result<Unit> {
        return try {
            val response = messengerRepository.unpinMessage(
                hashMapOf(
                    "thread_id" to conversationId.toString(),
                    "id" to messageId.toString()
                )
            )
            Result.Success(response)
        } catch (e: Exception) {
            Timber.e(e)
            Result.Error(exceptionHandler(e))
        }
    }
}
