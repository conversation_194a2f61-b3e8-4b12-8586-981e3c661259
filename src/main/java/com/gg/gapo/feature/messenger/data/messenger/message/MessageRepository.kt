package com.gg.gapo.feature.messenger.data.messenger.message

import androidx.paging.PagingData
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.ConversationEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageEntity
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.StatusMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.BlockUserRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.ReportViolationRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.SaveMessageRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.MessageCreateResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.ReportViolationResponse
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.*
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageBotCommandModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageDraftModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.ReactedUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageMultipleTargetRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.messenger.data.sources.models.ChatBotDto
import com.gg.gapo.messenger.data.sources.models.bases.ListDataResponseModel
import com.gg.gapo.messenger.data.sources.remote.response.SearchMessageResponse.Links
import com.gg.gapo.messenger.domain.models.ChatBotModel
import com.gg.gapo.messenger.domain.models.SearchMessage
import kotlinx.coroutines.flow.Flow

/**
 * Created by bacnd on 10/06/2022.
 */
internal interface MessageRepository {
    suspend fun jumpTo(messageId: Int)

    fun messagesFlow(conversationId: Long): Flow<PagingData<MessageModel>>

    suspend fun fetchPinCollections(
        conversationId: Long,
        isSave: Boolean = true
    ): List<MessageModel>

    fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>>

    suspend fun pinMessage(messageId: Int, query: HashMap<String, String>)

    suspend fun unpinMessage(query: HashMap<String, String>)

    suspend fun reactMessage(conversationId: Long, messageId: Int, reactType: Int)

    suspend fun createMessage(messageRequestModel: MessageRequestModel): MessageCreateResponse?

    suspend fun createMessageForMultipleTarget(messageMultipleTargetRequestModel: MessageMultipleTargetRequestModel): MessageCreateResponse?

    suspend fun editMessageAction(conversationId: Long, messageId: Int)

    suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel)

    suspend fun createMessageMqtt(messageEntity: MessageEntity, conversationId: Long)

    suspend fun saveMessageCreatorRequest(messageEntity: MessageEntity)

    suspend fun deleteMessageError(conversationId: Long, createAt: Long)

    suspend fun getMessageRequestModelError(
        conversationId: Long,
        createAt: Long
    ): MessageRequestModel?

    suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel)

    suspend fun findByClientId(clientId: Long, conversationId: Long): MessageRequestModel?

    suspend fun deleteMessageMqtt(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    )

    suspend fun editMessageMqtt(statusMqttDto: StatusMqttDto)

    suspend fun readAtMessageMqtt(conversationId: Long, messageId: Int, userId: String)

    suspend fun reactMessageMqtt(statusMqttDto: StatusMqttDto)

    suspend fun getUsersWithIds(userIds: List<String>): List<MessageUserModel>

    suspend fun fetchUsersWithIds(userIds: List<String>): List<MessageUserModel>

    suspend fun read(conversationId: Long, messageId: Int)

    suspend fun markUnread(conversationId: Long, messageId: Int)

    suspend fun fetchPreviewLink(url: String): MessageModel.MessagePreviewLinkModel?

    suspend fun updateMessageCache(messageEntity: MessageEntity, conversationId: Long)

    suspend fun updateMessageSubthreadMqtt(conversationModel: ConversationModel?, statusMqttDto: StatusMqttDto)

    suspend fun updateMessageSubthreadMqtt(
        conversationId: Long?,
        messageId: Int?,
        sender: MessageUserModel?,
        subThreadModel: SubThreadModel?
    )

    suspend fun fetchMessageFirstPageInConversations(conversationsCached: List<ConversationEntity>)

    suspend fun getMessageById(conversationId: Long, messageId: Int): MessageEntity?

    suspend fun getMessageByIdNoSave(conversationId: Long, messageId: Int): MessageEntity?

    suspend fun getMessageCachedById(conversationId: Long, messageId: Int): MessageModel?

    suspend fun fillMessage(messageModel: MessageModel, conversationId: Long): MessageModel

    suspend fun fillBodyMessage(
        messageBodyModel: MessageModel.MessageBodyModel,
        conversationId: Long,
        messageId: Int
    ): MessageModel.MessageBodyModel

    suspend fun fillAndUpdateLastMessagesCached(lastMessages: List<MessageEntity>)

    suspend fun fillAndUpdateMessagesCached(messages: List<MessageEntity>)

    suspend fun fillAndSaveMessageEntity(messageEntity: MessageEntity, conversationId: Long)

    suspend fun searchUsers(
        queries: Map<String, String>,
        groupId: String
    ): Pair<List<MessageUserModel>, Map<String, String>>

    fun lastMessageFlow(conversationId: Long): Flow<MessageModel>

    suspend fun lastMessage(conversationId: Long): MessageModel?

    suspend fun fetchMessengerReactedUsers(
        messageId: Int,
        queries: Map<String, String>
    ): Pair<List<ReactedUserModel>, Map<String, String>>

    suspend fun getSenderById(userId: String): MessageModel.SenderModel?

    suspend fun sendMessagesToSave(body: SaveMessageRequestBody)

    suspend fun deleteMessages(
        messageIds: List<Int>,
        conversationId: Long,
        level: MessageLevelDeleteType
    )

    suspend fun createVote(
        conversationId: Long,
        messageId: Int,
        voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel
    )

    suspend fun chooseVote(conversationId: Long, messageId: Int, voteId: String, isChoose: Boolean)

    suspend fun getUserVoted(
        conversationId: Long,
        messageId: Int,
        voteId: String
    ): List<MessageUserModel>

    suspend fun fetchUsersVoted(queryMap: Map<String, String>): List<MessageUserModel>

    suspend fun sendTypingEvent(conversationId: Long)

    suspend fun blockUser(body: BlockUserRequestBody)

    suspend fun fetchBotCommands(botId: String): List<MessageBotCommandModel>

    suspend fun fetchUserParticipants(threadId: Long): List<MessageUserParticipantsModel>

    suspend fun getCreateAtLastMessageInConversationLocal(conversationId: Long): Long

    suspend fun updateMessageTextDraft(conversationId: Long, messageDraft: MessageDraftModel?)

    suspend fun autoDeleteMessages(threadId: Long): Int

    suspend fun existAutoDeleteMessages(threadId: Long): Int

    suspend fun fetchBotById(botId: String): ChatBotModel

    suspend fun searchBot(params: Map<String, Any>): ListDataResponseModel<ChatBotDto>

    suspend fun getActionNotes(
        threadId: Long,
        lastCreatedAt: Long,
        prevCreatedAt: Long,
        middleCreatedAt: Long,
        pageSize: Int
    ): List<MessageModel>

    suspend fun searchConversations(params: MutableMap<String, Any>): List<ConversationModel>

    suspend fun searchMentionMessages(params: Map<String, Any>): Pair<Links?, List<SearchMessage>>

    suspend fun joinGroupCall(client: String, roomId: String): String

    suspend fun getRoomInfo(clientId: String, ids: List<String>): List<RoomModel>

    suspend fun getSignedRequest(botId: String, threadId: String): String

    suspend fun reportViolation(body: ReportViolationRequestBody): ReportViolationResponse

    suspend fun getQuickMessages(): List<QuickMessageModel>

    suspend fun fetchUsersById(id: String): MessageUserModel?
}
