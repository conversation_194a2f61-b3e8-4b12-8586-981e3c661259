package com.gg.gapo.feature.messenger.di

import com.gg.gapo.core.utilities.api.GapoApiVersion
import com.gg.gapo.core.utilities.di.apiBaseUrl
import com.gg.gapo.core.utilities.di.appEnvironment
import com.gg.gapo.core.utilities.di.commonOkHttp
import com.gg.gapo.core.utilities.di.downloadOkHttp
import com.gg.gapo.core.utilities.di.messengerApiBaseUrl
import com.gg.gapo.core.utilities.di.noAuthenticatorDownloadOkHttp
import com.gg.gapo.core.utilities.di.uploadApiBaseUrl
import com.gg.gapo.core.utilities.di.uploadOkHttp
import com.gg.gapo.core.utilities.retrofit.RetrofitFactory
import com.gg.gapo.feature.messenger.MessengerCenterV2
import com.gg.gapo.feature.messenger.data.messenger.MessengerRepositoryImpl
import com.gg.gapo.feature.messenger.data.messenger.conversation.ConversationRepository
import com.gg.gapo.feature.messenger.data.messenger.conversation.ConversationRepositoryImpl
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.ConversationCache
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.ConversationCacheImpl
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.ConversationRemote
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.ConversationRemoteImpl
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.api.ConversationApiService
import com.gg.gapo.feature.messenger.data.messenger.folder.FolderRepository
import com.gg.gapo.feature.messenger.data.messenger.folder.FolderRepositoryImpl
import com.gg.gapo.feature.messenger.data.messenger.folder.cache.FolderCache
import com.gg.gapo.feature.messenger.data.messenger.folder.cache.FolderCacheImpl
import com.gg.gapo.feature.messenger.data.messenger.folder.remote.FolderRemote
import com.gg.gapo.feature.messenger.data.messenger.folder.remote.FolderRemoteImpl
import com.gg.gapo.feature.messenger.data.messenger.folder.remote.api.FolderApiService
import com.gg.gapo.feature.messenger.data.messenger.message.MessageRepository
import com.gg.gapo.feature.messenger.data.messenger.message.MessageRepositoryImpl
import com.gg.gapo.feature.messenger.data.messenger.message.cache.MessageCache
import com.gg.gapo.feature.messenger.data.messenger.message.cache.MessageCacheImpl
import com.gg.gapo.feature.messenger.data.messenger.message.remote.MessageRemote
import com.gg.gapo.feature.messenger.data.messenger.message.remote.MessageRemoteImpl
import com.gg.gapo.feature.messenger.data.messenger.message.remote.api.MessageApiService
import com.gg.gapo.feature.messenger.data.messenger.message.remote.api.MessageUserApiService
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmDispatcher
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmMessengerModule
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmProvider
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmProviderImpl
import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerExceptionHandler
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerExceptionHandlerImpl
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.AddOrganisationConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.AddUserToConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.ConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.CreateConversationGroupUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.CreateConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.CreateOrganisationConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.CreateSubThreadUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.DeleteConversationCacheUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.DeleteConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.EnableNotifyConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchCollabByIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchConversationByCollabIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchConversationByFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchConversationsInCommonUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchPartnerStatusUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchTimeStampUserOnlineUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationByIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationsByLastVisitAtUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationsFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetSubthreadsFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.JoinCollabUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.JoinLinkCollabUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.MoveConversationToFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.PinConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.SearchConversationsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.SendTypingEventUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.SetAutoDeleteUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.ToggleMuteAllSubThreadUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.ToggleMuteAllUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.ToggleSubthreadNotificationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UnPinConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UpdateLastVisitAtConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UpdateMarkUnReadConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.UpdateMessageDraftConversationUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.DeleteFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.FetchListFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.FolderUnReadFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.FoldersFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.GetFolderByIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.MoveFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.SelectFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.folder.SortFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.AutoDeleteMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.BlockUserUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ChooseVoteUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CountAutoDeleteMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CreateMessageMultipleTargetUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CreateMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CreateVoteUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.DeleteMessageErrorUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.DeleteMessagesUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.EditMessageActionUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.EditMessageCacheActionUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchActionNotesUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchBotCommandsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchBotListUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchMessageFirstPageConversationsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchMessengerReactedUsersUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchPreviewLinkUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchQuickMessagesUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchUsersByIdsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchUsersByUserIdsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchUsersParticipantsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchVotedUsersUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetLastMessageFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetLastMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageBotUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageRequestErrorUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessageUserUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetMessagesFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetReactedUsersInDirectUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetSearchMentionMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetSenderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetSignedRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetUsersParticipantsByIdUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.GetVotedUsersUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.JoinGroupCallUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.JumpMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.MarkUnreadMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReactMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReadMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.ReportViolationMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SaveMessageCreatorRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SearchUsersUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SendMessagesToSaveUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.UpdateMessageCreatorRequestUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.UpdateReadMessageFromTypingEventUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin.GetMessagesPinedFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin.PinMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin.PinedMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.pin.UnPinMessageUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.ConnectedMessageMqttUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.ConnectedMessengerMqttUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.CreateMessageMqttUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.DeleteMessageMqttUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.MessageForMessengerFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.mqtt.StatusForMessengerFlowUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.user.FetchChatPendingUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.user.ReviewChatPendingUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.user.SearchBotListUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.user.UserUseCase
import com.gg.gapo.feature.messenger.logger.MessengerLogger
import com.gg.gapo.feature.messenger.logger.MessengerLoggerImpl
import com.gg.gapo.feature.messenger.presentation.bot.BotListBottomSheetViewModel
import com.gg.gapo.feature.messenger.presentation.collab.MessengerCollabRequestViewModel
import com.gg.gapo.feature.messenger.presentation.conversation.viewmodel.ConversationViewModel
import com.gg.gapo.feature.messenger.presentation.folder.list.viewmodel.MessengerFolderViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.action.avatar.MessengerAvatarActionViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.action.message.MessengerMessageActionViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.media.viewmodel.SelectedMediaViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.react.viewmodel.ReactedUsersViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.seen.MessengerSeenUserViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.SubthreadMessengerViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.subthread.list.SubthreadListViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerCollabViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.vote.viewmodel.VotedUsersViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.bot.viewmodel.BotListViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.command.viewmodel.BotCommandViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.MessengerInputViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.widget.input.media.MessengerInputMediaViewModel
import com.gg.gapo.feature.messenger.presentation.setting.commongroups.CommonGroupsViewModel
import com.gg.gapo.feature.messenger.presentation.test.ConversationTestViewModel
import com.gg.gapo.feature.messenger.presentation.test.message.MessageTestViewModel
import com.gg.gapo.feature.messenger.worker.EditMessageWorker
import com.gg.gapo.feature.messenger.worker.MessageCreatorWorker
import com.gg.gapo.feature.messenger.worker.MessageUploaderWorker
import com.gg.gapo.feature.messenger.worker.MessengerDownloadWorker
import com.gg.gapo.feature.messenger.worker.message_first_page.MessageFirstPageWorker
import io.realm.RealmConfiguration
import org.koin.android.ext.koin.androidApplication
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.androidx.workmanager.dsl.worker
import org.koin.core.qualifier.qualifier
import org.koin.dsl.module
import org.koin.dsl.onClose

val featureMessenger = module {
    // Remote Api
    single {
        RetrofitFactory.create<ConversationApiService>(messengerApiBaseUrl, commonOkHttp)
    }
    single {
        RetrofitFactory.create<FolderApiService>(messengerApiBaseUrl, commonOkHttp)
    }
    single {
        RetrofitFactory.create<MessageApiService>(messengerApiBaseUrl, commonOkHttp)
    }
    factory {
        RetrofitFactory.create<MessageUserApiService>(apiBaseUrl, commonOkHttp)
    }

    // Realm
    single { RealmDispatcher("MessengerNewV2") }
    single<RealmConfiguration>(qualifier("MessengerNewV2")) {
//        Realm.getDefaultConfiguration() ?:
        RealmConfiguration.Builder()
            .name("messenger_module.realm")
            .allowQueriesOnUiThread(false)
            .allowWritesOnUiThread(false)
            .modules(RealmMessengerModule)
            .deleteRealmIfMigrationNeeded()
            .build()
    }
    single<RealmProvider> { RealmProviderImpl(get(qualifier("MessengerNewV2"))) }

    // Cache
    single<ConversationCache> { ConversationCacheImpl(get(), get(), get()) }
    single<FolderCache> { FolderCacheImpl(get(), get()) }
    factory<MessageCache> { MessageCacheImpl(androidApplication(), get(), get(), get()) }

    // Remote
    single<ConversationRemote> { ConversationRemoteImpl(get(), get()) }
    single<FolderRemote> { FolderRemoteImpl(get()) }
    factory<MessageRemote> { MessageRemoteImpl(get(), get()) }

    // Repository
    factory<MessengerRepository> {
        MessengerRepositoryImpl(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }

    single<ConversationRepository> {
        ConversationRepositoryImpl(
            androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    single<FolderRepository> { FolderRepositoryImpl(get(), get(), get(), get()) }
    factory<MessageRepository> {
        MessageRepositoryImpl(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }

    // UseCase
    factory { FetchListFolderUseCase(get(), get()) }
    factory { FoldersFlowUseCase(get()) }
    factory { FolderUnReadFlowUseCase(get()) }
    factory { SelectFolderUseCase(get()) }
    factory { SortFolderUseCase(get()) }
    factory { GetFolderByIdUseCase(get(), get()) }
    factory { DeleteFolderUseCase(get(), get()) }
    factory { MoveFolderUseCase(get(), get()) }
    factory { GetConversationsFlowUseCase(get()) }
    factory { ConversationUseCase(get(), get(), get(), get(), get(), get(), get()) }
    factory { GetSubthreadsFlowUseCase(get()) }
    factory { GetMessagesFlowUseCase(get()) }
    factory { GetMessagesPinedFlowUseCase(get()) }
    factory { PinConversationUseCase(get(), get()) }
    factory { UnPinConversationUseCase(get(), get()) }
    factory { EnableNotifyConversationUseCase(get(), get()) }
    factory { DeleteConversationUseCase(get(), get()) }
    factory { SendMessagesToSaveUseCase(get(), get()) }
    factory { DeleteMessagesUseCase(get(), get()) }
    factory { JumpMessageUseCase(get()) }
    factory { ReactMessageUseCase(get(), get()) }
    factory { PinedMessageUseCase(get(), get(), get()) }
    factory { PinMessageUseCase(get(), get(), get()) }
    factory { UnPinMessageUseCase(get(), get()) }
    factory { CreateMessageUseCase(get(), get()) }
    factory { CreateVoteUseCase(get(), get()) }
    factory { ChooseVoteUseCase(get(), get()) }
    factory { DeleteConversationCacheUseCase(get(), get()) }
    factory { GetConversationFlowUseCase(get()) }
    factory { SaveMessageCreatorRequestUseCase(get()) }
    factory { UpdateMessageCreatorRequestUseCase(get()) }
    factory { EditMessageCacheActionUseCase(get(), get()) }
    factory { EditMessageActionUseCase(get(), get()) }
    factory { DeleteMessageErrorUseCase(get(), get()) }
    factory { GetMessageRequestErrorUseCase(get(), get()) }
    factory { GetMessageRequestUseCase(get()) }
    factory { GetMessageUseCase(get(), get()) }
    factory { GetMessageBotUseCase(get(), get()) }
    factory { GetSenderUseCase(get(), get()) }
    factory { GetVotedUsersUseCase(get(), get()) }
    factory { FetchVotedUsersUseCase(get(), get()) }
    factory { FetchPreviewLinkUseCase(get(), get()) }
    factory { FetchMessageFirstPageConversationsUseCase(get(), get()) }
    factory { SearchUsersUseCase(get(), get()) }
    factory { GetLastMessageFlowUseCase(get()) }
    factory { GetLastMessageUseCase(get()) }
    factory { FetchMessengerReactedUsersUseCase(get(), get()) }
    factory { GetReactedUsersInDirectUseCase(get(), get()) }
    factory { GetConversationByIdUseCase(get(), get()) }
    factory { GetConversationsUseCase(get(), get()) }
    factory { CreateConversationUseCase(get(), get()) }
    factory { CreateOrganisationConversationUseCase(get(), get()) }
    factory { AddOrganisationConversationUseCase(get(), get()) }
    factory { FetchConversationUseCase(get(), get()) }
    factory { SendTypingEventUseCase(get(), get()) }
    factory { UserUseCase(get(), get(), get(), get(), get()) }
    factory { GetMessageUserUseCase(get(), get()) }
    factory { BlockUserUseCase(get(), get()) }
    factory { FetchBotCommandsUseCase(get(), get()) }
    factory { FetchUsersParticipantsUseCase(get(), get()) }
    factory { GetUsersParticipantsByIdUseCase(get(), get()) }
    factory { UpdateMessageDraftConversationUseCase(get(), get()) }
    factory { MoveConversationToFolderUseCase(get(), get()) }
    factory { FetchConversationByCollabIdUseCase(get(), get()) }
    factory { FetchCollabByIdUseCase(get(), get()) }
    factory { JoinCollabUseCase(get(), get()) }
    factory { JoinLinkCollabUseCase(get(), get()) }
    factory { UpdateLastVisitAtConversationUseCase(get(), get()) }
    factory { GetConversationsByLastVisitAtUseCase(get(), get()) }
    factory { UpdateMarkUnReadConversationUseCase(get(), get()) }
    factory { FetchChatPendingUseCase(get(), get()) }
    factory { ReviewChatPendingUseCase(get(), get()) }
    factory { CreateSubThreadUseCase(get(), get()) }
    factory { AddUserToConversationUseCase(get(), get()) }

    factory { ReadMessageUseCase(get(), get()) }
    factory { MarkUnreadMessageUseCase(get(), get()) }
    factory { UpdateReadMessageFromTypingEventUseCase(get(), get()) }
    factory { FetchTimeStampUserOnlineUseCase(get(), get()) }
    factory { FetchPartnerStatusUseCase(get(), get()) }

    factory { CreateMessageMqttUseCase(get()) }
    factory { ConnectedMessengerMqttUseCase(get()) }
    factory { ConnectedMessageMqttUseCase(get()) }
    factory { DeleteMessageMqttUseCase(get()) }
    factory { StatusForMessengerFlowUseCase(get(), get()) }
    factory { MessageForMessengerFlowUseCase(get(), get()) }
    factory { ToggleSubthreadNotificationUseCase(get(), get()) }
    factory { SetAutoDeleteUseCase(get(), get()) }
    factory { AutoDeleteMessageUseCase(get(), get()) }
    factory { CountAutoDeleteMessageUseCase(get(), get()) }
    factory { FetchConversationsInCommonUseCase(get(), get()) }
    factory { CreateConversationGroupUseCase(get(), get()) }
    factory { FetchBotListUseCase(get(), get()) }
    factory { CreateMessageMultipleTargetUseCase(get(), get()) }
    factory { FetchUsersByUserIdsUseCase(get(), get()) }
    factory { JoinGroupCallUseCase(get(), get(), get()) }
    factory { FetchActionNotesUseCase(get(), get()) }

    factory { SearchConversationsUseCase(get(), get()) }

    factory { SearchBotListUseCase(get(), get()) }

    factory { ToggleMuteAllUseCase(get(), get()) }

    factory { ToggleMuteAllSubThreadUseCase(get(), get()) }

    factory { GetSearchMentionMessageUseCase(get(), get()) }

    factory { GetSignedRequestUseCase(get()) }

    factory { ReportViolationMessageUseCase(get(), get()) }

    factory { FetchQuickMessagesUseCase(get(), get()) }

    factory { FetchConversationByFolderUseCase(get(), get()) }

    factory { FetchUsersByIdsUseCase(get(), get()) }

    // ViewModel
    viewModel {
        ConversationViewModel(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            application = androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel { parameters ->
        MessengerViewModel(
            androidApplication(),
            appEnvironment = appEnvironment,
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            "$apiBaseUrl${GapoApiVersion.MEETING_API_VERSION}/api/record/",
            conversationId = parameters.get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel { parameters ->
        MessengerInputViewModel(
            androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            conversationId = parameters.get()
        )
    }
    viewModel { parameters ->
        SubthreadListViewModel(
            context = androidApplication(),
            userManager = get(),
            getSubthreadsFlowUseCase = get(),
            toggleSubthreadNotificationUseCase = get(),
            coroutineDispatchers = get(),
            conversationId = parameters.get(),
            isAdminOrOwner = parameters.get(),
            collabId = parameters.get(),
            toggleMuteAllSubThreadUseCase = get(),
            toggleMuteAllUseCase = get()
        )
    }
    viewModel {
        MessengerCollabViewModel(
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        MessengerCollabRequestViewModel(
            androidApplication(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        SubthreadMessengerViewModel(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        ConversationTestViewModel(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        MessageTestViewModel(
            androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        MessengerFolderViewModel(androidApplication(), get(), get(), get(), get(), get(), get())
    }
    viewModel {
        ReactedUsersViewModel(androidApplication(), get(), get(), get(), get(), get(), get())
    }
    viewModel {
        VotedUsersViewModel(get(), get())
    }
    viewModel {
        MessengerAvatarActionViewModel(get(), get(), get())
    }
    viewModel {
        MessengerMessageActionViewModel(androidApplication(), get(), get(), get(), get(), get())
    }
    viewModel {
        BotCommandViewModel(get(), get())
    }
    viewModel {
        CommonGroupsViewModel(
            get(),
            get()
        )
    }
    viewModel {
        BotListViewModel(
            get()
        )
    }

    viewModel {
        MessengerInputMediaViewModel(
            get(),
            androidApplication()
        )
    }

    viewModel {
        SelectedMediaViewModel(get(), get())
    }

    viewModel { BotListBottomSheetViewModel(get()) }

    viewModel { MessengerSeenUserViewModel(get()) }

    single {
        MessengerCenterV2(
            application = androidApplication(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(qualifier("MessengerNewV2"))
        )
    }.onClose {
        it?.destroy()
    }
    single<MessengerLogger> { MessengerLoggerImpl() }
    factory<MessengerExceptionHandler> { MessengerExceptionHandlerImpl(get()) }

    // worker
    worker { params ->
        MessageUploaderWorker(
            androidContext(),
            params.get(),
            uploadApiBaseUrl,
            uploadOkHttp,
            get(),
            get(),
            get()
        )
    }
    worker { params ->
        MessageCreatorWorker(androidContext(), params.get(), get(), get(), get(), get(), get())
    }
    worker { params ->
        MessageFirstPageWorker(
            androidContext(),
            params.get(),
            get()
        )
    }
    worker { params ->
        EditMessageWorker(
            androidContext(),
            params.get(),
            get()
        )
    }
    worker { params ->
        MessengerDownloadWorker(
            androidContext(),
            params.get(),
            downloadOkHttp,
            noAuthenticatorDownloadOkHttp,
            get()
        )
    }
}
