package com.gg.gapo.feature.messenger.domain.messenger.usecase.message

import com.gg.gapo.feature.messenger.domain.messenger.MessengerRepository
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel

internal class GetLastMessageUseCase(
    private val messengerRepository: MessengerRepository
) {
    suspend operator fun invoke(conversationId: Long): MessageModel? {
        return messengerRepository.lastMessage(conversationId)
    }
}
