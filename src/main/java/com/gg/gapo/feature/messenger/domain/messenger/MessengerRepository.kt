package com.gg.gapo.feature.messenger.domain.messenger

import androidx.paging.PagingData
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.request.CreateThreadRequestBody
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.request.ThreadOrgcRequestBody
import com.gg.gapo.feature.messenger.data.messenger.conversation.remote.model.response.ConversationCreatedResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Maker
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.MessageCreatedMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Receiver
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.Settings
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.dto.StatusMqttDto
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.BlockUserRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.request.SaveMessageRequestBody
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.JoinLinkCollabResponse
import com.gg.gapo.feature.messenger.data.messenger.message.remote.model.response.MessageCreateResponse
import com.gg.gapo.feature.messenger.domain.messenger.model.common.MessengerScreenScopeEnum
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ChatPendingModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ChatPendingStatus
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationRole
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderModel
import com.gg.gapo.feature.messenger.domain.messenger.model.folder.FolderType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.*
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageUserParticipantsModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageMultipleTargetRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.feature.messenger.domain.messenger.model.mqtt.MqttEventType
import com.gg.gapo.messenger.data.sources.models.bases.Links
import com.gg.gapo.messenger.data.sources.models.collab.CollabModel
import com.gg.gapo.messenger.domain.models.ChatBotModel
import kotlinx.coroutines.flow.Flow

internal interface MessengerRepository {
    // conversations
    fun conversationsFlow(): Flow<PagingData<ConversationModel>>

    suspend fun pinConversationRemote(conversationId: Long)

    suspend fun unPinConversation(conversationId: Long)

    suspend fun toggleNotifyConversationCache(conversationId: Long)

    suspend fun toggleNotifyConversation(conversationId: Long)

    suspend fun clearHistory(conversationId: Long)

    suspend fun fetchTimeStampUserOnline(folderType: FolderType)

    fun conversationFlow(conversationId: Long): Flow<ConversationModel?>

    suspend fun getConversationById(conversationId: Long): ConversationModel?

    suspend fun getForwardConversation(): List<ConversationModel>

    suspend fun sendTypingEvent(conversationId: Long)

    suspend fun moveConversationToFolder(conversationId: Long, folderAlias: String)

    suspend fun fetchThreadByCollabId(collabId: String): ConversationCreatedResponse?

    suspend fun fetchCollabById(collabId: String): CollabModel?

    suspend fun joinCollab(collabId: String)

    suspend fun joinLinkCollab(inviteToken: String): JoinLinkCollabResponse?

    suspend fun updateConversationLastVisit(conversationId: Long, lastVisitAt: Long)

    suspend fun getConversationsByVisit(): List<ConversationModel>

    suspend fun setUnreadFlag(conversationId: Long, unread: Boolean)

    suspend fun fetchChatPending(collabId: String, queries: Map<String, String>): Pair<List<ChatPendingModel>, Map<String, String>>

    suspend fun reviewsChatPending(collabId: String, pendingId: String, status: ChatPendingStatus)

    suspend fun createSubThread(conversationId: Long, messageId: Int): ConversationModel?

    // folder
    fun foldersFlow(): Flow<List<FolderModel>>

    fun folderUnreadFlow(): Flow<FolderModel?>

    // suspend fun fetchListFolders()

    suspend fun selectFolder(folderType: FolderType)

    suspend fun sortFolders(aliasFolders: List<String>)

    suspend fun getFolderById(alias: String): FolderModel?

    suspend fun deleteFolder(alias: String)

    suspend fun moveFolders(threadIds: List<Long>, newFolder: String)

    // message
    fun messagesFlow(conversationId: Long): Flow<PagingData<MessageModel>>

    fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>>

    suspend fun jumpTo(messageId: Int)

    suspend fun pinMessage(messageId: Int, query: HashMap<String, String>)

    suspend fun unpinMessage(query: HashMap<String, String>)

    suspend fun reactMessage(conversationId: Long, messageId: Int, reactType: Int)

    suspend fun createMessage(messageRequestModel: MessageRequestModel): MessageCreateResponse?

    suspend fun createMessageForMultipleTarget(messageMultipleTargetRequestModel: MessageMultipleTargetRequestModel): MessageCreateResponse?

    suspend fun saveMessageCreatorRequest(messageRequestModel: MessageRequestModel)

    suspend fun deleteMessageError(conversationId: Long, createAt: Long)

    suspend fun getMessageRequestModelError(conversationId: Long, createAt: Long): MessageRequestModel?

    suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel)

    suspend fun findByClientId(clientId: Long, conversationId: Long): MessageRequestModel?

    suspend fun read(conversationId: Long, messageId: Int)

    suspend fun markUnread(conversationId: Long, messageId: Int)

    suspend fun updateReadMessageFromTypingEvent(conversationId: Long, userId: String)

    suspend fun fetchMessageFirstPageInConversations(folderType: FolderType)

    suspend fun searchUsers(queries: Map<String, String>, groupId: String): Pair<List<MessageUserModel>, Map<String, String>>

    fun lastMessageFlow(conversationId: Long): Flow<MessageModel>

    suspend fun lastMessage(conversationId: Long): MessageModel?

    suspend fun getMessageById(conversationId: Long, messageId: Int): MessageModel?

    suspend fun fetchMessengerReactedUsers(messageId: Int, queries: Map<String, String>): Pair<List<ReactedUserModel>, Map<String, String>>

    suspend fun getReactedUsersInDirect(conversationId: Long, messageId: Int): List<ReactedUserModel>

    suspend fun getSenderById(userId: String): MessageModel.SenderModel?

    suspend fun sendMessagesToSave(body: SaveMessageRequestBody)

    suspend fun deleteMessages(messageIds: List<Int>, conversationId: Long, level: MessageLevelDeleteType)

    suspend fun createConversation(body: CreateThreadRequestBody): ConversationCreatedResponse?

    suspend fun createOrganisation(body: ThreadOrgcRequestBody): ConversationCreatedResponse?

    suspend fun addOrganisation(body: ThreadOrgcRequestBody)

    suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel)

    suspend fun editMessageAction(conversationId: Long, messageId: Int)

    suspend fun createVote(conversationId: Long, messageId: Int, voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel)

    suspend fun chooseVote(conversationId: Long, messageId: Int, voteId: String, isChoose: Boolean)

    suspend fun getUserVoted(conversationId: Long, messageId: Int, voteId: String): List<MessageUserModel>

    suspend fun fetchUsersVoted(queryMap: Map<String, String>): List<MessageUserModel>

    suspend fun getUsersWithIds(userIds: List<String>): List<MessageUserModel>

    suspend fun fetchUserParticipants(threadId: Long): List<MessageUserParticipantsModel>

    suspend fun getUserParticipantsById(conversationId: Long, userId: String): MessageUserParticipantsModel?

    suspend fun updateMessageTextDraft(conversationId: Long, messageDraft: MessageDraftModel?)

    // mqtt
    suspend fun createMessageMqtt(messageCreatedMqttDto: MessageCreatedMqttDto, scope: MessengerScreenScopeEnum)

    suspend fun deleteConversationMqtt(conversationId: Long)

    suspend fun pinConversationCache(conversationId: Long, pinnedAt: Long, folder: FolderType? = null)

    suspend fun readAtConversationMqtt(conversationId: Long, messageId: Int, userId: String)

    suspend fun connectedMessengerMqtt()

    suspend fun connectedMessageMqtt(conversationId: Long)

    suspend fun fetchConversation(conversationId: Long): ConversationModel?

    suspend fun deleteMessageMqtt(conversationId: Long, messageId: Int, level: MessageLevelDeleteType)

    suspend fun editMessageMqtt(statusMqttDto: StatusMqttDto)

    suspend fun updateMessageSubthreadMqtt(statusMqttDto: StatusMqttDto)

    suspend fun reactMessageMqtt(statusMqttDto: StatusMqttDto)

    suspend fun blockConversationMqtt(maker: Maker, receiver: Receiver, block: MqttEventType, isMeMaker: Boolean)

    suspend fun updateSettingsMqtt(conversationId: Long, settings: Settings)

    suspend fun updateAuthorizeMqtt(conversationId: Long, userId: String, role: ConversationRole)

    suspend fun updateMarkUnReadMqtt(conversationId: Long, markUnread: Boolean)

    suspend fun fetchPreviewLink(url: String): MessageModel.MessagePreviewLinkModel?

    suspend fun statusForMessengerFlow(data: StatusMqttDto)

    suspend fun messageForMessengerFlow(data: MessageCreatedMqttDto, scope: MessengerScreenScopeEnum)

    suspend fun blockUser(body: BlockUserRequestBody)

    suspend fun fetchBotCommands(botId: String): List<MessageBotCommandModel>

    suspend fun fetchBotById(botId: String): ChatBotModel

    suspend fun getBotList(conversationId: Long): List<ChatBotModel>

    suspend fun autoDeleteMessages(threadId: Long): Int

    suspend fun existAutoDeleteMessages(threadId: Long): Int

    suspend fun searchBot(params: Map<String, Any>): Pair<Links, List<ChatBotModel>>

    suspend fun getActionNotes(threadId: Long, lastCreatedAt: Long, prevCreatedAt: Long, middleCreatedAt: Long, pageSize: Int): List<MessageModel>

    suspend fun joinGroupCall(client: String, roomId: String): String

    suspend fun getQuickMessages(): List<QuickMessageModel>

    suspend fun fetchUsersById(id: String): MessageUserModel?
}
