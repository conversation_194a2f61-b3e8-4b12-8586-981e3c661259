package com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message

import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 29/12/2022
 */
enum class MessageCarouselButtonType {

    @SerializedName("postback")
    POSTBACK {
        override val type: String
            get() = "postback"
    },

    @SerializedName("web_url")
    WEB_URL {
        override val type: String
            get() = "web_url"
    },

    @SerializedName("phone_number")
    PHONE_NUMBER {
        override val type: String
            get() = "phone_number"
    };

    abstract val type: String

    companion object {
        fun getByType(type: String?) = values().find { it.type == type }
    }
}
