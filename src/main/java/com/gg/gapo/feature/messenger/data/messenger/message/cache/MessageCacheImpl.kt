package com.gg.gapo.feature.messenger.data.messenger.message.cache

import android.content.Context
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.ConversationCache
import com.gg.gapo.feature.messenger.data.messenger.conversation.cache.model.ConversationMessagesPinnedEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.*
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.AsanaTaskInformationEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessagePollInformationVoteEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageUserEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.RemoteKeys
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.SenderEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.mapToDomain
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmDispatcher
import com.gg.gapo.feature.messenger.data.messenger.realm.RealmProvider
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageBodyType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageSendStatus
import io.realm.RealmResults
import io.realm.Sort
import io.realm.kotlin.executeTransactionAwait
import io.realm.kotlin.toFlow
import io.realm.kotlin.where
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import org.joda.time.DateTime
import org.joda.time.DateTimeZone
import timber.log.Timber

/**
 * Created by bacnd on 11/06/2022.
 */
internal class MessageCacheImpl(
    private val context: Context,
    private val realmProvider: RealmProvider,
    private val conversationCache: ConversationCache,
    private val realmDispatcher: RealmDispatcher
) : MessageCache {
    override suspend fun findAll(): RealmResults<MessageEntity> =
        realmProvider.retrieveRealm().where(MessageEntity::class.java).findAll()

    override suspend fun findById(conversationId: Long, messageId: Int): MessageEntity? =
        realmProvider.retrieveRealm().where(MessageEntity::class.java).equalTo(
            MessageEntity.UID,
            "${conversationId}_$messageId"
        ).findFirst()

    override suspend fun findByClientId(clientId: Long): MessageEntity? =
        realmProvider.retrieveRealm().where(MessageEntity::class.java).equalTo(
            MessageEntity.CLIENT_ID,
            clientId
        ).findFirst()

    override suspend fun findByConversation(conversationId: Long): RealmResults<MessageEntity> =
        realmProvider.retrieveRealm().where(MessageEntity::class.java)
            .contains(MessageEntity.UID, "${conversationId}_").findAll()

    override suspend fun getLastMessageSucceeded(conversationId: Long): MessageEntity? {
        val messagesCached = realmProvider.retrieveRealm().where(MessageEntity::class.java)
            .contains(MessageEntity.UID, "${conversationId}_")
            .notEqualTo(MessageEntity.MESSAGE_SEND_STATUS, MessageSendStatus.ERROR.status)
            .notEqualTo(MessageEntity.MESSAGE_SEND_STATUS, MessageSendStatus.SENDING.status)
            .findAll()

        messagesCached.maxByOrNull { it.id }?.let {
            return realmProvider.retrieveRealm().copyFromRealm(it)
        }

        return null
    }

    override suspend fun getCreateAtLastMessageInConversationLocal(conversationId: Long): Long {
        return realmProvider.retrieveRealm().where(MessageEntity::class.java)
            .contains(MessageEntity.UID, "${conversationId}_")
            .max(MessageEntity.CREATED_AT)?.toLong() ?: Long.MIN_VALUE
    }

    override suspend fun save(message: MessageEntity) = withContext(realmDispatcher) {
        try {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                realm.copyToRealmOrUpdate(message)
            }
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    override suspend fun save(messages: List<MessageEntity>) = try {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(messages)
        }
    } catch (e: Exception) {
        Timber.e(e)
    }

    override suspend fun updateFirstPageAndDeleteAll(
        conversationId: Long,
        messagesEntity: List<MessageEntity>,
        messagesPinedEntity: List<MessageEntity>
    ) {
        // val messagesCached = conversationCache.findBy(conversationId)?.messages
        // val messagesCached = findByConversation(conversationId)
        val messagesCached = realmProvider.retrieveRealm().where(MessageEntity::class.java)
            .contains(MessageEntity.UID, "${conversationId}_")
            .notEqualTo(MessageEntity.MESSAGE_SEND_STATUS, MessageSendStatus.ERROR.status)
            .notEqualTo(MessageEntity.MESSAGE_SEND_STATUS, MessageSendStatus.SENDING.status)
            .findAll()

        val mentionsCached = realmProvider.retrieveRealm().where(MessageMentionEntity::class.java)
            .contains(MessageMentionEntity.ID, "${conversationId}_")
            .findAll()

        val imagesCached = messagesCached.mapNotNull { it.messageImages }
        val videosCached = messagesCached.mapNotNull { it.messageVideos }
        val filesCached = messagesCached.mapNotNull { it.messageFiles }
        val voicesCached = messagesCached.mapNotNull { it.messageVoices }
        val optionsCached = messagesCached.mapNotNull { it.options }

        val carouselCardsCached = messagesCached.mapNotNull { it.carouselCards }

        if (mentionsCached.isNullOrEmpty()) {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                messagesEntity.firstOrNull()?.let { realm.copyToRealmOrUpdate(it) }
                realm.copyToRealmOrUpdate(messagesPinedEntity)
                realm.copyToRealmOrUpdate(messagesEntity)
            }
            setMessageIdsPinned(
                conversationId,
                messagesPinedEntity.map { messagePinned -> messagePinned.id }
            )
        } else {
            messagesCached.maxByOrNull { it.id }?.let {
                val lastMessageCached = realmProvider.retrieveRealm().copyFromRealm(it)
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->

                    // TODO deleteAllFromRealm field's Message
                    realm.delete(AsanaTaskInformationEntity::class.java)
                    realm.delete(AttachmentInfoEntity::class.java)
                    imagesCached.forEach {
                        it.deleteAllFromRealm()
                    }
                    videosCached.forEach {
                        it.deleteAllFromRealm()
                    }
                    filesCached.forEach {
                        it.deleteAllFromRealm()
                    }
                    voicesCached.forEach {
                        it.deleteAllFromRealm()
                    }
                    optionsCached.forEach {
                        it.deleteAllFromRealm()
                    }
                    carouselCardsCached.forEach {
                        it.forEach {
                            it.buttons?.deleteAllFromRealm()
                        }
                        it.deleteAllFromRealm()
                    }
                    mentionsCached.deleteAllFromRealm()
                    messagesCached.deleteAllFromRealm()

                    realm.copyToRealmOrUpdate(lastMessageCached)
                    realm.copyToRealmOrUpdate(messagesPinedEntity)
                    realm.copyToRealmOrUpdate(messagesEntity)
                }

                setMessageIdsPinned(
                    conversationId,
                    messagesPinedEntity.map { messagePinned -> messagePinned.id }
                )
            }
        }
    }

    override suspend fun updateFirstPage(
        conversationId: Long,
        messagesEntity: List<MessageEntity>
    ) {
        // TODO dam bao rang messages da linking vs conversation
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(messagesEntity)
        }
    }

    override suspend fun getPinCollections(): RealmResults<MessageEntity> =
        realmProvider.retrieveRealm().where(MessageEntity::class.java)
            .notEqualTo(MessageEntity.PINNED_AT, 0.toLong())
            .findAll()

    override suspend fun getUsersWithId(userId: String): MessageUserEntity? =
        realmProvider.retrieveRealm().where(MessageUserEntity::class.java)
            .equalTo(MessageUserEntity.ID, userId).findFirst()

    override suspend fun getUsersWithIds(userIds: Array<String>): List<MessageUserEntity> {
        return realmProvider.retrieveRealm().where(MessageUserEntity::class.java)
            .`in`(MessageUserEntity.ID, userIds).findAll()
    }

    override suspend fun saveUsers(users: List<MessageUserEntity>) = try {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
            it.copyToRealmOrUpdate(users)
        }
    } catch (e: Exception) {
        Timber.e(e)
    }

    override suspend fun deleteMessageError(conversationId: Long, createAt: Long) {
        val realm = realmProvider.retrieveRealm()
        val messageError = realm.where(MessageEntity::class.java).equalTo(
            MessageEntity.UID,
            "${conversationId}_$createAt"
        ).findFirst()

        val conversationCached = messageError?.conversation
        val conversationName = conversationCached?.name

        messageError?.let {
            realm.executeTransactionAwait(realmDispatcher) {
                messageError.deleteFromRealm()

                // refresh conversations
                conversationCached?.name = conversationName
            }
        }
    }

    override suspend fun autoDeleteMessages(threadId: Long): Int {
        val realm = realmProvider.retrieveRealm()
        realm.where(MessageEntity::class.java).contains(MessageEntity.UID, "${threadId}_")
            .greaterThan(MessageEntity.WILL_DELETE_AT, 0).lessThan(
                MessageEntity.WILL_DELETE_AT,
                DateTime(
                    DateTimeZone.UTC
                ).millis
            ).findAll()?.let { messages ->
                if (messages.isNotEmpty()) {
                    realm.executeTransactionAwait(realmDispatcher) {
                        messages.deleteAllFromRealm()
                    }
                }
                return messages.size
            }
        return 0
    }

    override suspend fun existAutoDeleteMessage(threadId: Long): Int {
        val realm = realmProvider.retrieveRealm()
        return realm.where(MessageEntity::class.java).contains(MessageEntity.UID, "${threadId}_")
            .greaterThan(MessageEntity.WILL_DELETE_AT, 0).findAll().orEmpty().size
    }

    override suspend fun getMessageRequestError(
        conversationId: Long,
        createAt: Long
    ): MessageEntity? {
        val realm = realmProvider.retrieveRealm()
        val result = realm.where(MessageEntity::class.java).equalTo(
            MessageEntity.UID,
            "${conversationId}_$createAt"
        ).findFirst()
        return if (result == null) null
        else realm.copyFromRealm(result)
    }

    override suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel) {
        val messageRequestEntity = findByClientId(messageRequestModel.clientId)

        val conversationCached = messageRequestEntity?.conversation
        val conversationName = conversationCached?.name

        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            if (messageRequestModel.messageSendStatus == MessageSendStatus.SUCCESS) {
                // replace
                messageRequestModel.clientId =
                    messageRequestModel.id.toLong() // để cho hàm mapToEntity
                realm.copyToRealmOrUpdate(messageRequestModel.mapToEntity(messageRequestModel.threadId))
                messageRequestEntity?.deleteFromRealm()
            } else {
                // update
                messageRequestEntity?.updateMessageCreatorRequest(realm, messageRequestModel)
            }

            // refresh conversation
            conversationCached?.name = conversationName
        }
    }

    override suspend fun deleteMessageMqtt(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    ) {
        val conversationEntity = conversationCache.findBy(conversationId)
        val conversationName = conversationEntity?.name
        val messageEntity = findById(conversationId, messageId)
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
            if (level == MessageLevelDeleteType.ONLY_ME) {
                messageEntity?.deleteFromRealm()
            } else if (level == MessageLevelDeleteType.FOR_ALL) {
                messageEntity?.deleted = true
                messageEntity?.textBody =
                    context.getString(GapoStrings.messenger_message_already_deleted)
                messageEntity?.typeBody = MessageBodyType.TEXT.type

                messageEntity?.messageImages?.deleteAllFromRealm()
                messageEntity?.messageVideos?.deleteAllFromRealm()
                messageEntity?.messageFiles?.deleteAllFromRealm()
                messageEntity?.messageVoices?.deleteAllFromRealm()
                messageEntity?.messageFileMeeting?.deleteFromRealm()
                messageEntity?.pollInformationMetadataBody?.deleteFromRealm()
                messageEntity?.callInformationMetadataBody?.deleteFromRealm()
            }
            // refresh ui conversation
            conversationEntity?.name = conversationName
        }
    }

    override suspend fun update(conversationId: Long, message: MessageEntity) {
        val messageCacheEdited = findById(conversationId, message.id)
        if (messageCacheEdited != null) {
            val uid = messageCacheEdited.uid
            // tai sao phai mapToDomain
            val messageModel = message.mapToDomain()
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                messageCacheEdited.updateBody(
                    messageModel.body,
                    conversationId,
                    uid,
                    realm,
                    messageModel = messageModel
                )
            }
        }
    }

    override suspend fun updateReactMessageCache(
        conversationId: Long,
        messageId: Int,
        reactType: Int,
        reactUserId: String,
        myUserId: String,
        previousReaction: Int
    ) {
        withContext(realmDispatcher) {
            val conversationEntity = conversationCache.findBy(conversationId)
            val conversationType = conversationEntity?.type
            val messageEntityManaged = findById(conversationId, messageId)

            messageEntityManaged?.let {
                val messageEntityUnManaged =
                    realmProvider.retrieveRealm().copyFromRealm(messageEntityManaged)
                messageEntityUnManaged.updateReactMessageUnmanaged(
                    ConversationType.getByType(conversationType) ?: ConversationType.DIRECT,
                    reactType,
                    reactUserId,
                    myUserId,
                    previousReaction
                )
                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                    messageEntityManaged.updateReactMessageManaged(messageEntityUnManaged)
                }
            }
        }
    }

    override suspend fun editMessageMqtt(
        conversationId: Long,
        messageId: Int,
        messageBodyModel: MessageModel.MessageBodyModel,
        isMeActor: Boolean
    ) {
        withContext(realmDispatcher) {
            val conversationContainMessageEdited = conversationCache.findBy(conversationId)
            val messageCacheEdited = findById(conversationId, messageId)
            if (messageCacheEdited != null) {
                val uid = messageCacheEdited.uid
                val conversationName = conversationContainMessageEdited?.name

                // edit message với poll vote, cần check nếu actor_id là mình thì để nguyên myself_votes từ mqtt để save
                // actor_id k phải mình thì cần để nguyên myself_votes trong cache, lấy myself_votes trong cache gán cho myself_votes mqtt
                if (messageBodyModel.type == MessageBodyType.POLL && !isMeActor) {
                    messageBodyModel.metadata?.pollInformation?.myselfVotes =
                        messageCacheEdited.pollInformationMetadataBody?.myselfVotes?.split(",")
                            ?.map { it.trim() } ?: emptyList()
                }

                realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                    messageCacheEdited.updateBody(messageBodyModel, conversationId, uid, realm)

                    // refresh data conversation to update UI
                    conversationContainMessageEdited?.name = conversationName
                }
            }
        }
    }

//    private val messageIdsPinnedFlow = MutableStateFlow(listOf<Int>())

    override suspend fun setMessageIdsPinned(conversationId: Long, messageIdsPinned: List<Int>) {
        val conversationMessagesPinned =
            ConversationMessagesPinnedEntity(conversationId, messageIdsPinned.joinToString())
        withContext(realmDispatcher) {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                realm.copyToRealmOrUpdate(conversationMessagesPinned)
            }
        }

//        messageIdsPinnedFlow.emit(messageIdsPinned)
    }

    override fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>> {
        return flow { emit(realmProvider.retrieveRealm()) }
            .flatMapLatest { realm ->
                realmProvider.retrieveRealm().where(ConversationMessagesPinnedEntity::class.java)
                    .equalTo(ConversationMessagesPinnedEntity.ID, conversationId)
                    .findAll()
                    .toFlow()
                    .map {
                        it.firstOrNull()?.getMessagePinnedIds().orEmpty()
                    }
                    .filter { it.isNotEmpty() }
                    .map { messageIdsPinnedCached ->
                        val messageIdsPinnedArray = messageIdsPinnedCached.toTypedArray()
                        val realmResults = realm.where(MessageEntity::class.java)
                            .contains(MessageEntity.UID, "${conversationId}_")
                            .notEqualTo(
                                MessageEntity.MESSAGE_SEND_STATUS,
                                MessageSendStatus.ERROR.status
                            )
                            .notEqualTo(
                                MessageEntity.MESSAGE_SEND_STATUS,
                                MessageSendStatus.SENDING.status
                            )
                            .`in`(MessageEntity.ID, messageIdsPinnedArray)
                            // .sort(MessageEntity.PINNED_AT, Sort.DESCENDING)
                            .findAll()

                        val messagesModel =
                            realmResults.realm.copyFromRealm(realmResults).mapToDomain()
                        buildList {
                            messageIdsPinnedCached.map { messageIdPinned ->
                                messagesModel.find { it.id == messageIdPinned }?.let {
                                    add(it)
                                }
                            }
                        }
                    }
            }
    }
    // .notEqualTo(MessageEntity.PINNED_AT, 0.toLong())

    override suspend fun lastMessage(conversationId: Long): MessageModel? {
        return withContext(realmDispatcher) {
            realmProvider.retrieveRealm().where(MessageEntity::class.java)
                .contains(MessageEntity.UID, "${conversationId}_")
                .sort(
                    MessageEntity.ID,
                    Sort.DESCENDING,
                    MessageEntity.CREATED_AT,
                    Sort.DESCENDING
                )
                .findFirst()?.mapToDomain()
        }
    }

    override fun lastMessageFlow(conversationId: Long): Flow<MessageModel> =
        flow { emit(realmProvider.retrieveRealm()) }
            .flatMapLatest { realm ->
                realm.where(MessageEntity::class.java)
                    .contains(MessageEntity.UID, "${conversationId}_").findAll()
                    .toFlow()
                    .mapNotNull {
                        it.sort(
                            MessageEntity.ID,
                            Sort.DESCENDING,
                            MessageEntity.CREATED_AT,
                            Sort.DESCENDING
                        ).first()
                    }
//                .sort(MessageEntity.ID, Sort.DESCENDING, MessageEntity.CREATED_AT, Sort.DESCENDING).first()
//                .toFlow()
                    .mapNotNull {
                        it.mapToDomain()
                    }
                    .distinctUntilChanged()

//            realm.where(ConversationEntity::class.java).equalTo(ConversationEntity.ID, conversationId)
//                .findFirst()
//                .toFlow()
//                .mapNotNull { it?.messages?.max(MessageEntity.ID)?.toInt() ?: 0 }
//                .mapNotNull {
//                    realm.where(MessageEntity::class.java).contains(MessageEntity.UID, "${conversationId}_").findAll()
//                        .sort(MessageEntity.ID, Sort.DESCENDING, MessageEntity.CREATED_AT, Sort.DESCENDING).first()?.mapToDomain()
//                }

                // .mapNotNull { it?.messages?.firstOrNull()?.id }
                // .distinctUntilChangedBy { it }
            }

    override suspend fun removeById(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    ) {
        deleteMessageMqtt(conversationId, messageId, level)
//        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
//            realm.where(MessageEntity::class.java).equalTo(MessageEntity.UID, "${conversationId}_${messageId}").findFirst()
//                ?.deleteFromRealm()
//        }
    }

    override suspend fun removeByIds(
        conversationId: Long,
        messageIds: List<Int>,
        level: MessageLevelDeleteType
    ) {
        val conversationEntity = conversationCache.findBy(conversationId)
        val conversationName = conversationEntity?.name
        val messagesEntity = conversationEntity?.messages?.filter {
            messageIds.contains(it.id)
        }
        messagesEntity?.let {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                messagesEntity.forEach { messageEntity ->
                    if (level == MessageLevelDeleteType.ONLY_ME) {
                        messageEntity.deleteFromRealm()
                    } else if (level == MessageLevelDeleteType.FOR_ALL) {
                        messageEntity?.deleted = true
                        messageEntity?.textBody =
                            context.getString(GapoStrings.messenger_message_already_deleted)
                        messageEntity?.typeBody = MessageBodyType.TEXT.type

                        messageEntity?.messageImages?.deleteAllFromRealm()
                        messageEntity?.messageVideos?.deleteAllFromRealm()
                        messageEntity?.messageFiles?.deleteAllFromRealm()
                        messageEntity?.messageVoices?.deleteAllFromRealm()
                        messageEntity?.messageFileMeeting?.deleteFromRealm()
                        messageEntity?.pollInformationMetadataBody?.deleteFromRealm()
                    }
                }

                // refresh ui conversation
                conversationEntity.name = conversationName
            }
        }
    }

    override suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel) {
        val messageCached = findById(messageRequestModel.threadId, messageRequestModel.id)
        messageCached?.let {
            val messageUid = "${messageRequestModel.threadId}_${messageRequestModel.id}"
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) {
                messageCached.editMessageCacheAction(messageUid, messageRequestModel)
            }
        }
    }

    override suspend fun createVote(
        conversationId: Long,
        messageId: Int,
        voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel
    ) {
        val messageManaged = findById(conversationId, messageId)
        messageManaged?.let {
            realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
                messageManaged.updateCreateVote(voteAdded, realm)
            }
        }
    }

    override suspend fun getUserVoted(
        conversationId: Long,
        messageId: Int,
        voteId: String
    ): List<MessageUserEntity> {
//        val userVote =
//            findById(conversationId, messageId)?.pollInformationMetadataBody?.votes?.find { vote -> vote.id == voteId }?.userVotes
//                ?: return listOf()

        val userVote =
            realmProvider.retrieveRealm().where(MessagePollInformationVoteEntity::class.java)
                .equalTo(MessagePollInformationVoteEntity.ID, voteId).findFirst()?.userVotes
                ?: return listOf()
        return realmProvider.retrieveRealm().copyFromRealm(userVote)
    }

    override suspend fun getRemoteKeyForFirstItem(): Int? = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().where(RemoteKeys::class.java).max(RemoteKeys.MESSAGE_ID)
            ?.toInt()
    }

    override suspend fun getRemoteKeyForLastItem(): Int? = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().where(RemoteKeys::class.java).min(RemoteKeys.MESSAGE_ID)
            ?.toInt()
    }

    override suspend fun clearRemoteKeys() = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.delete(RemoteKeys::class.java)
        }
    }

    override suspend fun insertRemoteKeys(remoteKey: RemoteKeys) = withContext(realmDispatcher) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(remoteKey)
        }
    }

    override suspend fun isPageMessageFetched(keyMessageId: Int, pageSize: Int): Boolean =
        withContext(realmDispatcher) {
            realmProvider.retrieveRealm().where(RemoteKeys::class.java)
                .equalTo(RemoteKeys.MESSAGE_ID, keyMessageId).findFirst() != null
        }

    override suspend fun getSenderById(userId: String): SenderEntity? =
        realmProvider.retrieveRealm().where(SenderEntity::class.java)
            .equalTo(SenderEntity.ID, userId).findFirst()

    override suspend fun getActionNotes(threadId: Long, lastId: Long, pageSize: Int) =
        realmProvider.retrieveRealm()
            .where(MessageEntity::class.java)
            .contains(MessageEntity.UID, "${threadId}_")
            .equalTo(MessageEntity.TYPE_ACTION_NOTE, MessageBodyType.ACTION_NOTE.type)
            .sort(MessageEntity.CREATED_AT)
            .findAll()

    override suspend fun updateMessageTextDraft(messageDraft: MessageDraftEntity) {
        realmProvider.retrieveRealm().executeTransactionAwait(realmDispatcher) { realm ->
            realm.copyToRealmOrUpdate(messageDraft)
        }
    }

    override suspend fun deleteMessageTextDraft(conversationId: Long) {
        val realm = realmProvider.retrieveRealm()
        val messageDraft = realm.where(MessageDraftEntity::class.java)
            .equalTo(MessageDraftEntity.ID, conversationId).findFirst()
        messageDraft?.let {
            realm.executeTransactionAwait(realmDispatcher) {
                messageDraft.deleteFromRealm()
            }
        }
    }
}
