package com.gg.gapo.feature.messenger.data.messenger.message.cache

import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageDraftEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.MessageUserEntity
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.RemoteKeys
import com.gg.gapo.feature.messenger.data.messenger.message.cache.model.SenderEntity
import com.gg.gapo.feature.messenger.domain.messenger.model.message.MessageModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageLevelDeleteType
import io.realm.RealmResults
import kotlinx.coroutines.flow.Flow

/**
 * Created by bacnd on 11/06/2022.
 */
internal interface MessageCache {
    suspend fun findAll(): RealmResults<MessageEntity>

    suspend fun findById(conversationId: Long, messageId: Int): MessageEntity?

    suspend fun findByClientId(clientId: Long): MessageEntity?

    suspend fun findByConversation(conversationId: Long): RealmResults<MessageEntity>

    suspend fun getLastMessageSucceeded(conversationId: Long): MessageEntity?

    suspend fun getCreateAtLastMessageInConversationLocal(conversationId: Long): Long

    suspend fun save(message: MessageEntity)

    suspend fun save(messages: List<MessageEntity>)

    suspend fun updateFirstPageAndDeleteAll(
        conversationId: Long,
        messagesEntity: List<MessageEntity>,
        messagesPinedEntity: List<MessageEntity>
    )

    suspend fun updateFirstPage(conversationId: Long, messagesEntity: List<MessageEntity>)

    suspend fun getPinCollections(): RealmResults<MessageEntity>

    suspend fun getUsersWithId(userId: String): MessageUserEntity?

    suspend fun getUsersWithIds(userIds: Array<String>): List<MessageUserEntity>

    suspend fun saveUsers(users: List<MessageUserEntity>)

    suspend fun deleteMessageError(conversationId: Long, createAt: Long)

    suspend fun autoDeleteMessages(threadId: Long): Int

    suspend fun existAutoDeleteMessage(threadId: Long): Int

    suspend fun getMessageRequestError(conversationId: Long, createAt: Long): MessageEntity?

    suspend fun updateMessageCreatorRequest(messageRequestModel: MessageRequestModel)

    suspend fun deleteMessageMqtt(
        conversationId: Long,
        messageId: Int,
        level: MessageLevelDeleteType
    )

    suspend fun update(conversationId: Long, message: MessageEntity)

    suspend fun updateReactMessageCache(
        conversationId: Long,
        messageId: Int,
        reactType: Int,
        reactUserId: String,
        myUserId: String,
        previousReaction: Int
    )

    suspend fun editMessageMqtt(
        conversationId: Long,
        messageId: Int,
        messageBodyModel: MessageModel.MessageBodyModel,
        isMeActor: Boolean
    )

    suspend fun setMessageIdsPinned(conversationId: Long, messageIdsPinned: List<Int>)

    fun messagesPinedFlow(conversationId: Long): Flow<List<MessageModel>>

    fun lastMessageFlow(conversationId: Long): Flow<MessageModel>

    suspend fun lastMessage(conversationId: Long): MessageModel?

    suspend fun removeById(conversationId: Long, messageId: Int, level: MessageLevelDeleteType)

    suspend fun removeByIds(
        conversationId: Long,
        messageIds: List<Int>,
        level: MessageLevelDeleteType
    )

    suspend fun editMessageCacheAction(messageRequestModel: MessageRequestModel)

    suspend fun createVote(
        conversationId: Long,
        messageId: Int,
        voteAdded: MessageRequestModel.MessagePollInformationVoteRequestModel
    )

    suspend fun getUserVoted(
        conversationId: Long,
        messageId: Int,
        voteId: String
    ): List<MessageUserEntity>

    suspend fun updateMessageTextDraft(messageDraft: MessageDraftEntity)

    suspend fun deleteMessageTextDraft(conversationId: Long)

    // RemoteKeys
    suspend fun getRemoteKeyForFirstItem(): Int?

    suspend fun getRemoteKeyForLastItem(): Int?

    suspend fun clearRemoteKeys()

    suspend fun insertRemoteKeys(remoteKey: RemoteKeys)

    suspend fun isPageMessageFetched(keyMessageId: Int, pageSize: Int): Boolean

    // sender
    suspend fun getSenderById(userId: String): SenderEntity?

    suspend fun getActionNotes(threadId: Long, lastId: Long, pageSize: Int): List<MessageEntity>
}
