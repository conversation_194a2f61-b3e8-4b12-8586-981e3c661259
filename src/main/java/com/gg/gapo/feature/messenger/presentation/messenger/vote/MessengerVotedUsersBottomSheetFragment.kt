package com.gg.gapo.feature.messenger.presentation.messenger.vote

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.ui.bottomsheet.GapoHeightLimitedBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.feature.messenger.presentation.messenger.seen.adapter.SeenUserAdapter
import com.gg.gapo.feature.messenger.presentation.messenger.viewmodel.MessengerViewModel
import com.gg.gapo.feature.messenger.presentation.messenger.vote.decoration.MessengerPollVoteVoterDecoration
import com.gg.gapo.feature.messenger.presentation.messenger.vote.viewmodel.VotedUsersViewModel
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.MessengerSeenUsersBottomSheetFragmentBinding
import org.koin.androidx.viewmodel.ext.android.activityViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import kotlin.getValue

/**
 * Created by bacnd on 10/09/2022.
 */
internal class MessengerVotedUsersBottomSheetFragment internal constructor() :
    GapoHeightLimitedBottomSheetFragment(),
    SeenUserAdapter.SeenUsersListener {

    private val votedUsersViewModel by viewModel<VotedUsersViewModel>()

    private var binding by autoCleared<MessengerSeenUsersBottomSheetFragmentBinding>()
    private var votedUserAdapter by autoCleared<SeenUserAdapter>()
    private var glideRequests by autoCleared<GlideRequests>()
    private val messengerViewModel by activityViewModel<MessengerViewModel>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = MessengerSeenUsersBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        // binding.viewModel = reactedUsersViewModel
        glideRequests = GapoGlide.with(this)

        initRcvVotedUsers()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        votedUsersViewModel.conversationId = arguments?.getLong(CONVERSATION_ID_ARG) ?: 0L
        votedUsersViewModel.messageId = arguments?.getInt(MESSAGE_ID_ARG) ?: 0
        votedUsersViewModel.voteId = arguments?.getString(VOTE_ID_ARG).orEmpty()
        val voteCount = arguments?.getInt(VOTE_COUNT_ARG) ?: 0
        binding.textTitle.text = requireContext().resources.getQuantityString(
            com.gg.gapo.core.ui.GapoPlurals.messenger_plurals_poll_message_voters_dialog,
            voteCount,
            voteCount
        )
        binding.textTitle.setTextColor(ContextCompat.getColor(requireContext(), R.color.color_26282C))

        votedUsersViewModel.fetchUsersVoted(false)

        votedUsersViewModel.usersVotedLiveData.observe(viewLifecycleOwner) {
            if (binding.linearLoading.isVisible) {
                binding.linearLoading.isVisible = false
            }
            votedUserAdapter.submitList(it)
        }
    }

    private fun initRcvVotedUsers() {
        votedUserAdapter = SeenUserAdapter(requireContext(), this)
        val linearLayoutManager = LinearLayoutManager(requireContext())
        binding.listSeenUsers.run {
            setHasFixedSize(true)
            itemAnimator = null
            addItemDecoration(MessengerPollVoteVoterDecoration(requireContext()))
            addOnScrollListener(object : InfiniteScrollListener(linearLayoutManager) {

                override fun isDataLoading(): Boolean {
                    return votedUsersViewModel.isFetchingUserVoted
                }

                override fun onLoadMore() {
                    votedUsersViewModel.fetchMoreMembersByDepartment()
                }
            })
            layoutManager = linearLayoutManager
            adapter = votedUserAdapter
        }
    }

    override fun onClickViewUserProfile(userId: String) {
        messengerViewModel.onMessengerMessageOnClickUserAvatar(userId)
        dismiss()
    }

    companion object {
        internal val TAG = MessengerVotedUsersBottomSheetFragment::class.java.simpleName
        private const val CONVERSATION_ID_ARG = "CONVERSATION_ID_ARG"
        private const val MESSAGE_ID_ARG = "MESSAGE_ID_ARG"
        private const val VOTE_ID_ARG = "VOTE_ID_ARG"
        private const val VOTE_COUNT_ARG = "VOTE_COUNT_ARG"

        fun createInstance(conversationId: Long, messageId: Int, voteId: String, voteCount: Int) =
            MessengerVotedUsersBottomSheetFragment().apply {
                arguments = Bundle().apply {
                    putLong(CONVERSATION_ID_ARG, conversationId)
                    putInt(MESSAGE_ID_ARG, messageId)
                    putString(VOTE_ID_ARG, voteId)
                    putInt(VOTE_COUNT_ARG, voteCount)
                }
            }
    }
}
