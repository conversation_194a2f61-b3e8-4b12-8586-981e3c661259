package com.gg.gapo.feature.group.presentation.list.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.gg.gapo.core.navigation.deeplink.group.GroupDetailsDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.ui.shimmer.stopAllShimmers
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.group.R
import com.gg.gapo.feature.group.databinding.GroupWatchAllFragmentBinding
import com.gg.gapo.feature.group.presentation.common.DividerItemDecorator
import com.gg.gapo.feature.group.presentation.list.adapter.GroupListAdapter
import com.gg.gapo.feature.group.presentation.list.viewmodel.GroupListViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

internal class GroupWatchAllFragment : Fragment(), GroupListAdapter.GroupListCallback {

    private var binding by autoCleared<GroupWatchAllFragmentBinding> {
        it.linearGroupAllListLoadingView.layoutLoadingRoot.stopAllShimmers()
    }
    private lateinit var glideRequests: GlideRequests
    private var groupAdapter by autoCleared<GroupListAdapter>()

    private val groupListViewModel by viewModel<GroupListViewModel>()
    private val args by navArgs<GroupWatchAllFragmentArgs>()
    private val onRefreshListener = SwipeRefreshLayout.OnRefreshListener {
        groupListViewModel.resetAllValues()
        groupListViewModel.clearWatchAllList()
        groupListViewModel.getAllGroupList(args.categoryType)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = GroupWatchAllFragmentBinding.inflate(inflater, container, false).apply {
            viewModel = groupListViewModel
        }
        binding.lifecycleOwner = viewLifecycleOwner
        glideRequests = GapoGlide.with(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupView()
        setupObservers()
        groupListViewModel.getAllGroupList(args.categoryType)
    }

    private fun setupView() {
        binding.buttonError.setDebouncedClickListener {
            onRefreshData()
        }

        binding.buttonWifiTryAgain.setDebouncedClickListener {
            onRefreshData()
        }

        binding.swipeRefreshLayoutGroupWatchAll.setOnRefreshListener(onRefreshListener)
        binding.listGroupAllList.apply {
            val linearLayoutManager = LinearLayoutManager(context)
            layoutManager = linearLayoutManager
            AppCompatResources.getDrawable(
                requireActivity(),
                R.drawable.group_divider_recycler_view_1dp
            )
                ?.apply {
                    addItemDecoration(DividerItemDecorator(this))
                }
            groupAdapter = GroupListAdapter(glideRequests, this@GroupWatchAllFragment)
            adapter = groupAdapter
            addOnScrollListener(object : InfiniteScrollListener(linearLayoutManager, 10) {
                override fun onLoadMore() {
                    if (!groupListViewModel.isLastPage) {
                        groupListViewModel.setLoadingMore()
                        groupListViewModel.getAllGroupList(args.categoryType)
                    }
                }

                override fun isDataLoading(): Boolean {
                    return groupListViewModel.isLoadMore
                }
            })
        }
    }

    private fun onRefreshData() {
        groupListViewModel.resetAllValues()
        groupAdapter.submitList(listOf())
        groupListViewModel.clearWatchAllList()
        groupListViewModel.getAllGroupList(args.categoryType)
    }

    private fun setupObservers() {
        groupListViewModel.allGroupListLiveData.observe(
            viewLifecycleOwner
        ) {
            groupAdapter.submitList(it.toList())
        }

        groupListViewModel.isRefreshing.observe(
            viewLifecycleOwner
        ) {
            binding.swipeRefreshLayoutGroupWatchAll.isRefreshing = it
        }
    }

    override fun onGroupClick(groupId: String) {
        navByDeepLink(GroupDetailsDeepLink(groupId))
    }
}
