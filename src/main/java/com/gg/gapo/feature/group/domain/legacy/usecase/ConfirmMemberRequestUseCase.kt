package com.gg.gapo.feature.group.domain.legacy.usecase

import com.gg.gapo.feature.group.domain.legacy.GroupLegacyRepository
import com.google.gson.JsonObject
import io.reactivex.Single
import okhttp3.ResponseBody

/**
 * <AUTHOR>
 * move từ domain
 */
internal class ConfirmMemberRequestUseCase(private val groupRepository: GroupLegacyRepository) {
    fun confirmMemberRequest(groupId: String, bodyObject: JsonObject): Single<ResponseBody> {
        return groupRepository.confirmMemberRequest(groupId, bodyObject)
    }

    fun confirmAllMemberRequest(groupId: String, bodyObject: JsonObject): Single<ResponseBody> {
        return groupRepository.confirmAllMemberRequest(groupId, bodyObject)
    }
}
