package com.gg.gapo.feature.group.domain.legacy.model.domain.userprofile

import android.os.Parcelable
import com.gg.gapo.feature.group.domain.legacy.model.remote.userprofile.InfoDto
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * move từ domain
 */
@Parcelize
internal data class InfoDataModel(
    @SerializedName("website")
    var website: String = "",
    @SerializedName("bio")
    var bio: String = "",
    @SerializedName("address")
    var addressDataModel: AddressDataModel = AddressDataModel(),
    @SerializedName("relationship")
    var relationshipDataModel: RelationshipDataModel = RelationshipDataModel(),
    @SerializedName("education")
    var educationDataModel: MutableList<EducationDataModel> = mutableListOf(),
    @SerializedName("work")
    var workDataModel: MutableList<WorkDataModel> = mutableListOf()
) : Parcelable {

    fun toRemoteDto(): InfoDto {
        return InfoDto(
            website,
            bio,
            addressDataModel.toRemoteDto(),
            relationshipDataModel.toRemoteDto(),
            educationDataModel.map { it.toRemoteDto() }.toMutableList(),
            workDataModel.map { it.toRemoteDto() }.toMutableList()
        )
    }
}
