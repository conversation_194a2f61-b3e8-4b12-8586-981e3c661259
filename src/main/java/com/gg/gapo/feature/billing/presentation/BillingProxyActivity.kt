package com.gg.gapo.feature.billing.presentation

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.deeplink.billing.BillingNoticeDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.activity.GapoTranslucentBaseActivity
import com.gg.gapo.core.ui.bottomsheet.GapoAlertBottomSheetFragment
import com.gg.gapo.core.ui.bottomsheet.lifecycleOwner
import com.gg.gapo.core.utilities.billing.GapoBillingErrorType
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.feature.billing.R
import org.koin.androidx.viewmodel.ext.android.viewModel

@AppDeepLink(BillingNoticeDeepLink.APP_DEEP_LINK)
internal class BillingProxyActivity :
    GapoTranslucentBaseActivity(),
    PremiumFeatureBillingBottomSheetFragment.BillingListener {

    private val billingBlockViewModel by viewModel<BillingBlockViewModel>()

    private var currentBillingErrorType: GapoBillingErrorType? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.billing_proxy_activity)

        if (savedInstanceState == null) {
            handlerUI()
        }
    }

    private fun handlerUI() {
        val billingType =
            intent.extras?.getInt(BillingNoticeDeepLink.BILLING_TYPE_EXTRA)
                ?.let { GapoBillingErrorType.getValue(it) }
        if (currentBillingErrorType == billingType) return

        billingType?.let {
            when (billingType) {
                GapoBillingErrorType.LIMIT_TRIAL -> {
                    showLimitTrialBottomSheet()
                }
                GapoBillingErrorType.PREMIUM_FEATURE -> {
                    PremiumFeatureBillingBottomSheetFragment.createInstance()
                        .show(supportFragmentManager, null)
                }
                GapoBillingErrorType.OUT_OF_SPACE -> {
                    showOutOfSpaceBottomSheet()
                }
            }
        } ?: finish()
        currentBillingErrorType = billingType
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlerUI()
    }

    private fun finishActivityResult() {
        val intent = Intent()
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onDismissDialog() {
        finishActivityResult()
    }

    private fun showLimitTrialBottomSheet() {
        val description = if (billingBlockViewModel.isAdmin) {
            getString(GapoStrings.billing_limit_trial_admin_description)
                .replaceByBrandingName(billingBlockViewModel.brandName)
        } else {
            getString(GapoStrings.billing_limit_trial_member_description)
        }
        GapoAlertBottomSheetFragment.Builder().setIconRes(R.drawable.billing_ic_block_64dp_red)
            .setTitleRes(GapoStrings.billing_limit_trial_title)
            .setDescription(description)
            .setFirstButtonTextRes(GapoStrings.billing_title_understand)
            .setFirstButtonStylesRes(GapoStyles.GapoButton_Large_BgSecondary)
            .setListener(object : GapoAlertBottomSheetFragment.Listener {
                override fun onClickFirstButton(companionObject: Any?) {
                    onDismiss()
                }

                override fun onDismiss() {
                    finishActivityResult()
                }
            }).create().lifecycleOwner(this)
            .show(supportFragmentManager, GapoAlertBottomSheetFragment.TAG)
    }

    private fun showOutOfSpaceBottomSheet() {
        val titleRes = if (billingBlockViewModel.isAdmin) {
            GapoStrings.billing_out_of_space_admin_title
        } else {
            GapoStrings.billing_out_of_space_member_title
        }
        val description = if (billingBlockViewModel.isAdmin) {
            getString(GapoStrings.billing_out_of_space_admin_description)
                .replaceByBrandingName(billingBlockViewModel.brandName)
        } else {
            getString(GapoStrings.billing_out_of_space_member_description)
        }
        GapoAlertBottomSheetFragment.Builder().setIconRes(R.drawable.billing_ic_block_64dp_red)
            .setTitleRes(titleRes)
            .setDescription(description)
            .setFirstButtonTextRes(GapoStrings.billing_title_understand)
            .setFirstButtonStylesRes(GapoStyles.GapoButton_Large_BgSecondary)
            .setListener(object : GapoAlertBottomSheetFragment.Listener {
                override fun onClickFirstButton(companionObject: Any?) {
                    onDismiss()
                }

                override fun onDismiss() {
                    finishActivityResult()
                }
            }).create().lifecycleOwner(this)
            .show(supportFragmentManager, GapoAlertBottomSheetFragment.TAG)
    }
}
