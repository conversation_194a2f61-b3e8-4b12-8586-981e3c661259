package com.gg.gapo.feature.billing.data.api

import com.gg.gapo.core.utilities.api.GapoApiVersion
import com.gg.gapo.feature.billing.data.remote.response.BillingWorkspaceResponse
import retrofit2.http.GET
import retrofit2.http.Path

/**
 * <AUTHOR>
 * @since 15/11/2022.
 */
internal interface BillingApiService {
    @GET("${GapoApiVersion.CONFIG_API_VERSION}/workspaces/{id}/enable")
    suspend fun checkWorkSpaceStatus(@Path("id") id: String): BillingWorkspaceResponse
}
