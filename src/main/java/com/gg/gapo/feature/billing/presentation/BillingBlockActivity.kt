package com.gg.gapo.feature.billing.presentation

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthLogoutDeepLink
import com.gg.gapo.core.navigation.deeplink.billing.BillingBlockDeepLink
import com.gg.gapo.core.navigation.deeplink.home.HomeDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceInvitationDeepLink
import com.gg.gapo.core.utilities.interfacee.DoNotShowFlashMessage
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.core.workspace.eventbus.WorkspaceSwitchedBusEvent
import com.gg.gapo.feature.billing.R
import com.gg.gapo.feature.billing.databinding.BlockActivityBinding
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 02/11/2022.
 */
@AppDeepLink(BillingBlockDeepLink.APP_DEEP_LINK)
internal class BillingBlockActivity : AppCompatActivity(), DoNotShowFlashMessage {

    private lateinit var binding: BlockActivityBinding

    private val billingBlockViewModel by viewModel<BillingBlockViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.block_activity)
        binding.lifecycleOwner = this
        binding.viewModel = billingBlockViewModel

        billingBlockViewModel.checkWorkspaceStatus()
        billingBlockViewModel.updateUIChangeWorkspace()

        binding.textChangeWorkspace.setDebouncedClickListener {
            navByDeepLink(
                WorkspaceInvitationDeepLink(
                    GapoDeepLink.Options(
                        bundleOf(
                            WorkspaceInvitationDeepLink.TYPE_EXTRA to WorkspaceInvitationDeepLink.TYPE_BACK_WITH_LOGOUT_EXTRA
                        )
                    )
                )
            )
        }

        binding.buttonLogOut.setDebouncedClickListener {
            navByDeepLink(AuthLogoutDeepLink())
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        billingBlockViewModel.checkWorkspaceStatus()
        billingBlockViewModel.updateUIChangeWorkspace()
    }

    override fun onStart() {
        super.onStart()
        registerEventBus()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterEventBus()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onSwitchedWorkspace(event: WorkspaceSwitchedBusEvent) {
        navByDeepLink(HomeDeepLink())
    }
}
