package com.gg.gapo.feature.hashtag.feed.presentation

import android.app.Application
import androidx.lifecycle.*
import com.gg.gapo.analytic.GAPOAnalyticsEvents
import com.gg.gapo.analytic.features.GAPOAnalytics
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.hashtag.feed.domain.usecase.*
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 25/05/2021
 */
internal class FeedHashtagViewModel(
    private val application: Application,
    private val fetchFeedHashtagInfoUseCase: FetchFeedHashtagInfoUseCase,
    private val followHashtagUseCase: FollowHashtagUseCase,
    private val unFollowHashtagUseCase: UnFollowHashtagUseCase,
    private val fetchPreviewLinkUseCase: FetchPreviewLinkUseCase,
    private val appDispatchers: CoroutineDispatchers
) : ViewModel() {

    val tagLiveData: LiveData<String>
        get() = _tagLiveData.map {
            if (it.isNotEmpty() && !it.startsWith(TAG_SYMBOL)) {
                return@map "${TAG_SYMBOL}$it"
            }
            it
        }
    private val _tagLiveData = MutableLiveData<String>("")

    val trendingHashtagLiveData: LiveData<TrendingHashtagViewData>
        get() = _trendingHashtagLiveData
    private val _trendingHashtagLiveData = MutableLiveData<TrendingHashtagViewData>()

    val textFollowLiveData: LiveData<String> = trendingHashtagLiveData.map {
        if (it.isFollowing) application.getString(GapoStrings.trending_hash_tag_followed)
        else application.getString(GapoStrings.trending_hash_tag_follow)
    }

    val onUnFollowHashtagLiveData: LiveData<Event<Unit>>
        get() = _onUnFollowHashtagLiveData
    private val _onUnFollowHashtagLiveData = MutableLiveData<Event<Unit>>()

    fun onClickFollow() {
        val isFollowing = _trendingHashtagLiveData.value?.isFollowing ?: return
        trackingClickEvent(
            if (!isFollowing) GAPOAnalyticsEvents.EVENT_UNFOLLOW
            else GAPOAnalyticsEvents.EVENT_FOLLOW
        )
        if (!isFollowing) {
            followHashtag()
            return
        }
        _onUnFollowHashtagLiveData.value = Event(Unit)
    }

    fun fetchTrendingHashtagInfo(
        tag: String?,
        context: String?
    ) {
        val hashTag = tag.orEmpty().removePrefix(TAG_SYMBOL)
        _tagLiveData.value = hashTag
        if (hashTag.isEmpty()) {
            return
        }

        viewModelScope.launch {
            when (val result = fetchFeedHashtagInfoUseCase(hashTag, context.orEmpty())) {
                is Result.Success -> {
                    _trendingHashtagLiveData.value = result.data.mapToViewData()
                }
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun unFollowHashtag() {
        val hashTagId = _trendingHashtagLiveData.value?.id ?: return
        viewModelScope.launch {
            when (val result = unFollowHashtagUseCase(hashTagId)) {
                is Result.Success -> {
                    updateFollowState(false)
                }
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun trackingClickEvent(event: String) {
        GAPOAnalytics.getInstance(application)
            .logEventClick(
                objectEventValue = event,
                screenName = GAPOAnalyticsEvents.SCREEN_HASH_TAG_DETAIL
            )
    }

    private fun followHashtag() {
        val hashTagId = _trendingHashtagLiveData.value?.id ?: return
        viewModelScope.launch {
            when (val result = followHashtagUseCase(hashTagId)) {
                is Result.Success -> {
                    updateFollowState(true)
                }
                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun updateFollowState(isFollowing: Boolean) {
        val hashTag = _trendingHashtagLiveData.value ?: return
        hashTag.isFollowing = isFollowing
        _trendingHashtagLiveData.value = hashTag
    }

    companion object {
        private const val TAG_SYMBOL = "#"
        private const val WORK_PRIVACY = 5
    }
}
