package com.gg.gapo.feature.hashtag.group.presentation.list.bottomsheet

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.hashtag.group.domain.usecase.PinGroupHashtagUseCase
import com.gg.gapo.feature.hashtag.group.domain.usecase.UnPinGroupHashtagUseCase
import com.gg.gapo.feature.hashtag.group.utils.getErrorMessage
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @since 24/11/2021
 */
internal class GroupHashtagOptionViewModel(
    val application: Application,
    val pinGroupHashtagUseCase: PinGroupHashtagUseCase,
    val unPinGroupHashtagUseCase: UnPinGroupHashtagUseCase
) : ViewModel() {
    private var pinGroupHashtagJob: Job? = null
    private var unpinGroupHashtagJob: Job? = null

    val updateResponse: LiveData<Event<Pair<Boolean, String>>>
        get() = _updateResponse
    private val _updateResponse = MutableLiveData<Event<Pair<Boolean, String>>>()

    val errorMessage: LiveData<Event<String>>
        get() = _errorMessage
    private val _errorMessage = MutableLiveData<Event<String>>()

    fun pinGroupHashtag(groupId: String, tag: String) {
        pinGroupHashtagJob?.cancel()
        pinGroupHashtagJob = viewModelScope.launch {
            when (
                val result = pinGroupHashtagUseCase(
                    groupId,
                    tag
                )
            ) {
                is Result.Success -> {
                    _updateResponse.postValue(
                        Event(
                            Pair(
                                result.data.first.isSuccess,
                                result.data.second
                            )
                        )
                    )
                }
                is Result.Error -> {
                    application.getErrorMessage(result.exception)?.let {
                        _errorMessage.value = Event(it)
                    }
                }
            }
        }
    }

    fun unPinGroupHashtag(groupId: String, tag: String) {
        unpinGroupHashtagJob?.cancel()
        unpinGroupHashtagJob = viewModelScope.launch {
            when (
                val result = unPinGroupHashtagUseCase(
                    groupId,
                    tag
                )
            ) {
                is Result.Success -> {
                    _updateResponse.postValue(
                        Event(
                            Pair(
                                result.data.first.isSuccess,
                                result.data.second
                            )
                        )
                    )
                }
                is Result.Error -> {
                    application.getErrorMessage(result.exception)?.let {
                        _errorMessage.value = Event(it)
                    }
                }
            }
        }
    }
}
