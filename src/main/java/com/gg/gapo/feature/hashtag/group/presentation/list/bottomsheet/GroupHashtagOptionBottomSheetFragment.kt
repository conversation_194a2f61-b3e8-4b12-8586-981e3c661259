package com.gg.gapo.feature.hashtag.group.presentation.list.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.feature.hashtag.databinding.GroupHashtagBottomSheetFragmentBinding
import com.gg.gapo.feature.hashtag.group.presentation.list.model.GroupHashtagViewData
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 23/11/2021
 */
internal class GroupHashtagOptionBottomSheetFragment :
    GapoBottomSheetFragment() {
    private var binding by autoCleared<GroupHashtagBottomSheetFragmentBinding>()

    private val viewModel by viewModel<GroupHashtagOptionViewModel>()

    private var listener: Listener? = null

    private val groupId by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getString(GROUP_ID_ARG, "").orEmpty()
    }

    private val hashtagViewData by lazy(LazyThreadSafetyMode.NONE) {
        arguments?.getParcelable<GroupHashtagViewData>(HASHTAG_ARG)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = parentFragment as? Listener ?: activity as? Listener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = GroupHashtagBottomSheetFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()

        viewModel.errorMessage.observe(
            viewLifecycleOwner,
            EventObserver {
                GapoToast.makeNormal(requireContext(), it).show()
            }
        )

        viewModel.updateResponse.observe(
            viewLifecycleOwner,
            EventObserver {
                GapoToast.makeNormal(requireContext(), it.second).show()
                dismiss()
                if (it.first) {
                    listener?.onRefreshHashtag(groupId)
                }
            }
        )
    }

    private fun initView() {
        binding.apply {
            hashtagViewData?.let { hashTagViewData ->
                hashtag = hashTagViewData

                textOption.setOnClickListener {
                    if (hashTagViewData.isPin) {
                        viewModel.unPinGroupHashtag(groupId, hashTagViewData.id)
                    } else {
                        viewModel.pinGroupHashtag(groupId, hashTagViewData.id)
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
    }

    interface Listener {
        fun onRefreshHashtag(groupId: String)
    }

    companion object {
        private const val GROUP_ID_ARG = "GROUP_ID_ARG"
        private const val HASHTAG_ARG = "HASHTAG_ARG"

        fun createInstance(
            groupId: String,
            hashtagViewData: GroupHashtagViewData
        ) = GroupHashtagOptionBottomSheetFragment()
            .apply {
                arguments = bundleOf(
                    GROUP_ID_ARG to groupId,
                    HASHTAG_ARG to hashtagViewData
                )
            }
    }
}
