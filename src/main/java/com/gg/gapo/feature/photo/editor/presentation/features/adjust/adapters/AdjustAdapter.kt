package com.gg.gapo.feature.photo.editor.presentation.features.adjust.adapters

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.feature.photo.editor.R
import com.gg.gapo.feature.photo.editor.data.models.AdjustType
import com.gg.gapo.feature.photo.editor.data.models.ItemAdjust

class AdjustAdapter(private val listener: (ItemAdjust) -> Unit) : RecyclerView.Adapter<AdjustAdapter.ViewHolder>() {
    override fun getItemCount(): Int = data.size

    private var currentIndexSelected = 0

    private val data = arrayListOf(
        ItemAdjust(checked = true, localPathSelected = R.drawable.ic_photo_editor_edit_temperature_selected, localPathUnSelected = R.drawable.ic_photo_editor_edit_temperature_un_select, title = GapoStrings.photo_edit_adjust_temperature, type = AdjustType.TEMPERATURE),
        ItemAdjust(checked = false, localPathSelected = R.drawable.ic_photo_editor_edit_brightness_selected, localPathUnSelected = R.drawable.ic_photo_editor_edit_brightness_un_select, title = GapoStrings.photo_edit_adjust_brightness, type = AdjustType.BRIGHTNESS),
        ItemAdjust(checked = false, localPathSelected = R.drawable.ic_photo_editor_edit_contrast_selected, localPathUnSelected = R.drawable.ic_photo_editor_edit_contrast_un_select, title = GapoStrings.photo_edit_adjust_contrast, type = AdjustType.CONTRAST),
        ItemAdjust(checked = false, localPathSelected = R.drawable.ic_photo_editor_edit_structure_selected, localPathUnSelected = R.drawable.ic_photo_editor_edit_structure_un_select, title = GapoStrings.photo_edit_adjust_structure, type = AdjustType.STRUCTURE),
        ItemAdjust(checked = false, localPathSelected = R.drawable.ic_photo_editor_edit_cut_rotation_selected, localPathUnSelected = R.drawable.ic_photo_editor_edit_cut_rotation_un_select, title = GapoStrings.photo_edit_adjust_cut_rotation, type = AdjustType.CUT_ROTATION)
    )

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(R.layout.item_adjust_type, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, @SuppressLint("RecyclerView") position: Int) {
        val item = data[position]
        if (item.checked) {
            holder.image.setImageResource(item.localPathSelected)
            holder.title.setTextColor(ContextCompat.getColor(holder.title.context, R.color.photo_editor_primary))
        } else {
            holder.image.setImageResource(item.localPathUnSelected)
            holder.title.setTextColor(ContextCompat.getColor(holder.title.context, R.color.photo_editor_text))
        }
        holder.title.setText(item.title)
        holder.itemView.setOnClickListener {
            if (position == data.size - 1) {
                // cut and rotation
                listener.invoke(data[position])
            } else if (currentIndexSelected != position) {
                if (currentIndexSelected != -1) {
                    data[currentIndexSelected].checked = false
                }
                currentIndexSelected = position
                data[position].checked = true
                notifyDataSetChanged()
                listener.invoke(data[position])
            }
        }
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        inline val image: AppCompatImageView
            get() = itemView.findViewById(R.id.image)
        inline val title: AppCompatTextView
            get() = itemView.findViewById(R.id.title)
    }
}
