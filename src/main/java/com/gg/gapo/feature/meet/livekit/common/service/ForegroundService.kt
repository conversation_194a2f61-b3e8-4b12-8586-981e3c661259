package com.gg.gapo.feature.meet.livekit.common.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.gg.gapo.core.ui.service.Android15ForegroundService
import timber.log.Timber

/**
 * A foreground service is required for screen capture on API level Q (29) and up.
 * This a simple default foreground service to display a notification while screen
 * capturing.
 */

open class ForegroundService : Android15ForegroundService() {

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createNotificationChannel()
        }

        val actualNotification =
            NotificationCompat.Builder(this, DEFAULT_CHANNEL_ID)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .build()

        startForegroundCompat(
            DEFAULT_NOTIFICATION_ID,
            actualNotification,
            ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
        )
        return START_NOT_STICKY
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            DEFAULT_CHANNEL_ID,
            "Foreground",
            NotificationManager.IMPORTANCE_LOW
        )
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null // not used.
    }

    /**
     * Override special use timeout handling for meet service.
     * For screen capture services, we want to stop when timeout occurs.
     */
    override fun onSpecialUseTimeoutAction() {
        Timber.w("Meet foreground service timed out - stopping screen capture")
        // Stop the service as screen capture is no longer needed
    }

    companion object {
        const val DEFAULT_NOTIFICATION_ID = 3456
        const val DEFAULT_CHANNEL_ID = "livekit_example_foreground"
    }
}
