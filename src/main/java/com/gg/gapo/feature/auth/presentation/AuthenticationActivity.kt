package com.gg.gapo.feature.auth.presentation

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.findNavController
import androidx.navigation.navOptions
import com.airbnb.deeplinkdispatch.DeepLink
import com.gg.gapo.authentication.AuthenticationGraphDirections
import com.gg.gapo.authentication.R
import com.gg.gapo.authentication.databinding.AuthenticationActivityBinding
import com.gg.gapo.core.eventbus.authentication.LogoutBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.WebDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthDeepLinkType
import com.gg.gapo.core.navigation.deeplink.auth.AuthLoginDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthRegisterDeepLink
import com.gg.gapo.core.navigation.deeplink.auth.AuthRegisterFromInvitationDeepLink
import com.gg.gapo.core.utilities.interfacee.DoNotShowFlashMessage
import com.gg.gapo.core.utilities.keyboard.KeyBoardHeightObserver
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.result.exhaustive
import com.gg.gapo.feature.auth.center.AuthCenter
import com.gg.gapo.feature.auth.presentation.common.AuthHideKeyboardBaseActivity
import com.gg.gapo.feature.auth.presentation.common.model.received.IdentifierData
import com.gg.gapo.feature.auth.presentation.common.model.received.OtpReceiveData
import com.gg.gapo.feature.auth.presentation.navigation.AuthNavigationViewModel
import com.gg.gapo.feature.auth.presentation.navigation.AuthRoute
import com.gg.gapo.feature.auth.presentation.navigation.navToCreateWorkspace
import com.gg.gapo.feature.auth.presentation.navigation.navToFeed
import com.gg.gapo.feature.auth.presentation.navigation.navToListWorkspace
import com.gg.gapo.feature.auth.presentation.navigation.navToListWorkspaceAsNewAccount
import com.gg.gapo.feature.auth.recaptcha.RecaptchaRepository
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 06/05/2022
 */
@AppDeepLink(AuthLoginDeepLink.APP_DEEP_LINK)
@WebDeepLink(AuthLoginDeepLink.IDENTIFIER_WEB_DEEP_LINK)
class AuthenticationActivity :
    AuthHideKeyboardBaseActivity(),
    DoNotShowFlashMessage,
    KeyBoardHeightObserver.Callback {

    private lateinit var binding: AuthenticationActivityBinding
    private val authNavigationViewModel by viewModel<AuthNavigationViewModel>()
    private val authViewModel by viewModel<AuthenticationViewModel>()

    private val authCenter: AuthCenter by inject()

    private val keyBoardHeightObserver by lazy(LazyThreadSafetyMode.NONE) {
        KeyBoardHeightObserver(this, this)
    }

    private val navController: NavController
        get() = findNavController(R.id.fragment_container)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(0, 0)
        binding = AuthenticationActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        keyBoardHeightObserver.register()
        setupNavigation()
        setupObserver()

        if (savedInstanceState == null) {
            handleDeepLink(intent)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        keyBoardHeightObserver.unregister()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleDeepLink(intent)
    }

    private fun handleDeepLink(intent: Intent?) {
        val uri = try {
            Uri.parse(intent?.getStringExtra(DeepLink.URI))
        } catch (e: Exception) {
            null
        } ?: return

        val path = uri.path

        val openTypeString = intent?.extras?.getString(AuthLoginDeepLink.OPEN_TYPE_EXTRA)

        val openType =
            if (path?.contains(AuthLoginDeepLink.IDENTIFIER_WEB_DEEP_LINK) == true) {
                AuthDeepLinkType.IDENTIFIER_LOGIN
            } else {
                AuthDeepLinkType.getValue(openTypeString?.toIntOrNull())
                    ?: AuthDeepLinkType.LOGIN
            }

        val email =
            intent?.getStringExtra(AuthLoginDeepLink.EMAIL_EXTRA)
        val phoneNumber =
            intent?.getStringExtra(AuthLoginDeepLink.PHONE_NUMBER_EXTRA)
        val linkSso = intent?.getStringExtra(AuthLoginDeepLink.LINK_SSO_EXTRA)
        val companyName = intent?.getStringExtra(AuthLoginDeepLink.COMPANY_NAME_EXTRA)
        val identifierCode = intent?.getStringExtra(AuthLoginDeepLink.IDENTIFIER_CODE_EXTRA)
        val isFromSecurityPassword =
            intent?.getBooleanExtra(AuthLoginDeepLink.IS_FROM_SECURITY_EXTRA, false)

        when (openType) {
            AuthDeepLinkType.LOGIN -> {
                authNavigationViewModel.setRoute(
                    AuthRoute.Login(
                        email,
                        phoneNumber,
                        linkSso = linkSso
                    )
                )
            }

            AuthDeepLinkType.REGISTER -> {
                if (authNavigationViewModel.isOnPremise) return

                val invitationCode =
                    intent?.getStringExtra(AuthRegisterDeepLink.INVITATION_CODE_EXTRA)
                val errorMessage =
                    intent?.getStringExtra(AuthRegisterDeepLink.ERROR_MESSAGE_EXTRA)
                authNavigationViewModel.setRoute(
                    AuthRoute.Register(
                        invitationCode,
                        errorMessage
                    )
                )
            }

            AuthDeepLinkType.UPDATE_PROFILE -> {
                authNavigationViewModel.setRoute(AuthRoute.UpdateUserProfile())
            }

            AuthDeepLinkType.REGISTER_FROM_INVITATION -> {
                val invitationCode =
                    intent?.getStringExtra(AuthRegisterFromInvitationDeepLink.INVITATION_CODE_EXTRA)
                        .orEmpty()
                val invitationId =
                    intent?.getStringExtra(AuthRegisterFromInvitationDeepLink.INVITATION_ID_EXTRA)
                        .orEmpty()
                authNavigationViewModel.setRoute(
                    AuthRoute.RegisterFromInvitation(
                        invitationId,
                        invitationCode
                    )
                )
            }

            AuthDeepLinkType.CHANGE_PASSWORD -> {
                authNavigationViewModel.setRoute(AuthRoute.ChangePassword)
            }

            AuthDeepLinkType.IDENTIFIER_LOGIN -> {
                authNavigationViewModel.setRoute(AuthRoute.IdentifierLogin)
            }

            AuthDeepLinkType.FORGOT_PASSWORD -> {
                authNavigationViewModel.setRoute(
                    AuthRoute.ForgotPassword(
                        email,
                        phoneNumber,
                        isFromSecurityPassword
                    )
                )
            }

            AuthDeepLinkType.IDENTIFIER_FORGOT_PASSWORD -> {
                authNavigationViewModel.setRoute(
                    AuthRoute.IdentifierForgotPassword(
                        companyName,
                        identifierCode,
                        isFromSecurityPassword
                    )
                )
            }

            else -> {
            }
        }.exhaustive
    }

    private fun setupNavigation() {
        authNavigationViewModel.authRouteLiveData.observe(
            this,
            EventObserver { route ->
                when (route) {
                    is AuthRoute.Login -> {
                        val navOptions = navOptions {
                            popUpTo(R.id.authentication_graph)
                        }

                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalLoginFragment(
                                phone = route.phone,
                                email = route.email,
                                invitationCode = route.invitationCode,
                                errorMessage = route.errorMessage
                            ),
                            navOptions = navOptions
                        )

                        if (route.linkSso?.isNotEmpty() == true) {
                            navController.navigate(
                                AuthenticationGraphDirections.actionGlobalWebviewSsoFragment(
                                    route.linkSso
                                )
                            )
                        } else {
                        }
                    }

                    is AuthRoute.Register -> {
                        if (!authNavigationViewModel.isOnPremise) {
                            val navOptions = navOptions {
                                popUpTo(R.id.authentication_graph)
                            }
                            navController.navigate(
                                directions = AuthenticationGraphDirections.actionGlobalRegisterFragment(
                                    invitationCode = route.invitationCode,
                                    errorMessage = route.errorMessage
                                ),
                                navOptions = navOptions
                            )
                        } else {
                        }
                    }

                    is AuthRoute.RegisterFromInvitation -> {
                        val navOptions = navOptions {
                            popUpTo(R.id.authentication_graph)
                        }
                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalRegisterFromInvitationFragment(
                                invitationCode = route.invitationCode,
                                invitationId = route.invitationId
                            ),
                            navOptions = navOptions
                        )
                    }

                    is AuthRoute.UpdateUserProfile -> {
                        val navOptions = navOptions {
                            popUpTo(R.id.authentication_graph)
                        }
                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalUpdateProfileFragment(
                                invitationCode = route.invitationCode,
                                showInputPhone = route.showInputPhone
                            ),
                            navOptions = navOptions
                        )
                    }

                    AuthRoute.CreateWorkspace -> {
                        navToCreateWorkspace()
                        finish()
                    }

                    AuthRoute.Feed -> {
                        navToFeed()
                        finish()
                    }

                    is AuthRoute.ListWorkspace -> {
                        if (route.isNewAccount) {
                            navToListWorkspaceAsNewAccount(route.invitationErrorMessage)
                        } else {
                            navToListWorkspace(route.invitationErrorMessage)
                        }
                    }

                    is AuthRoute.SignupOtp -> {
                        RecaptchaRepository.initializeClient(this)
                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalSignupOtpFragment(
                                data = OtpReceiveData(
                                    type = route.data.type,
                                    email = route.data.email,
                                    phoneNumber = route.data.phoneNumber,
                                    newDomain = route.data.newDomain,
                                    invitationCode = route.data.invitationCode,
                                    identifierData = IdentifierData(
                                        isFirstSave = route.data.identifierData?.isFirstSave
                                            ?: false,
                                        companyName = route.data.identifierData?.companyName,
                                        identifierCode = route.data.identifierData?.identifierCode,
                                        identifierPhoneNumber = route.data.identifierData?.identifierPhoneNumber,
                                        email = route.data.identifierData?.email
                                    )
                                )
                            )
                        )
                    }

                    is AuthRoute.CreatePassword -> {
                        val navOptions = navOptions {
                            popUpTo(R.id.authentication_graph)
                        }

                        route.run {
                            navController.navigate(
                                directions = AuthenticationGraphDirections.actionGlobalCreatePasswordFragment(
                                    data = OtpReceiveData(
                                        email = route.email,
                                        phoneNumber = route.phoneNumber,
                                        invitationCode = route.invitationCode,
                                        type = route.type,
                                        identifierData = IdentifierData(
                                            route.identifierData?.isFirstSave,
                                            route.identifierData?.companyName,
                                            route.identifierData?.identifierCode,
                                            route.identifierData?.identifierPhoneNumber
                                        )
                                    ),
                                    salt = route.salt
                                ),
                                navOptions = navOptions
                            )
                        }
                    }

                    is AuthRoute.ForgotPassword -> {
                        if (route.isFromSecurityPassword == true) {
                            val navInflater = navController.navInflater
                            val navGraph = navInflater.inflate(R.navigation.authentication_graph)
                            navGraph.setStartDestination(R.id.forgotPasswordFragment)
                            navController.setGraph(navGraph, Bundle())
                        }

                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalForgotPasswordFragment(
                                email = route.email,
                                phoneNumber = route.phoneNumber
                            )
                        )
                    }

                    is AuthRoute.ChangePassword -> {
                        val navOptions = navOptions {
                            popUpTo(R.id.authentication_graph)
                        }
                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalChangePasswordFragment(),
                            navOptions = navOptions
                        )
                    }

                    is AuthRoute.IdentifierLogin -> {
                        val option = NavOptions.Builder().setLaunchSingleTop(true).build()
                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalIdentifierLoginFragment(),
                            navOptions = option
                        )
                    }

                    is AuthRoute.EmailSsoCheck -> {
                        navController.navigate(AuthenticationGraphDirections.actionGlobalEmailSsoFragment())
                    }

                    is AuthRoute.SsoViaWebView -> {
                        navController.navigate(
                            AuthenticationGraphDirections.actionGlobalWebviewSsoFragment(
                                route.authorizeUrl
                            )
                        )
                    }

                    is AuthRoute.IdentifierForgotPassword -> {
                        if (route.isFromSecurityPassword == true) {
                            val navInflater = navController.navInflater
                            val navGraph = navInflater.inflate(R.navigation.authentication_graph)
                            navGraph.setStartDestination(R.id.identifierForgotPasswordFragment)
                            navController.setGraph(navGraph, Bundle())
                        }

                        navController.navigate(
                            directions = AuthenticationGraphDirections.actionGlobalIdentifierForgotPasswordFragment(
                                companyName = route.companyName,
                                identifierCode = route.identifierCode
                            )
                        )
                    }
                }.exhaustive
            }
        )
    }

    /* Nếu trước đó force change password từ identifier và bị kill app, sau khi bật trở lại thì cần force logout*/
    private fun setupObserver() {
        authViewModel.forceUpdatePasswordLiveData.observe(
            this
        ) {
            if (it) {
                authViewModel.saveResetPasswordStatus()
                LogoutBusEvent(false).postEvent()
            }
        }
    }

    companion object {

        fun newIntent(activity: Activity, type: AuthDeepLinkType): Intent {
            return Intent(activity, AuthenticationActivity::class.java)
                .apply {
                    putExtra(AuthLoginDeepLink.OPEN_TYPE_EXTRA, type)
                }
        }
    }

    override fun onKeyboardHeightChanged(height: Int) {
    }
}
