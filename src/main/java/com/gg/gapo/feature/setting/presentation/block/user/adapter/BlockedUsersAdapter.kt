package com.gg.gapo.feature.setting.presentation.block.user.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.ui.image.thumbpattern.GapoThumbPattern
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.setting.data.remote.api.User
import com.gg.gapo.feature.setting.databinding.BlockedUserItemBinding

internal class BlockedUsersAdapter(
    private val requestManager: RequestManager,
    private val onBlockUserListener: OnBlockUserListener
) : RecyclerView.Adapter<BlockedUsersAdapter.ViewHolder>() {

    private val dataSet = mutableListOf<User>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            BlockedUserItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding, requestManager, onBlockUserListener)
    }

    override fun getItemCount(): Int {
        return dataSet.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(dataSet[position])
    }

    override fun onViewRecycled(holder: ViewHolder) {
        holder.onViewRecycled()
    }

    internal fun updateUsers(users: List<User>) {
        dataSet.addAll(users)
        notifyDataSetChanged()
    }

    fun clearData() {
        dataSet.clear()
        notifyDataSetChanged()
    }

    fun removeUserFromList(userId: String) {
        if (dataSet.isNotEmpty()) {
            for (i in 0 until dataSet.size) {
                if (dataSet[i].id.equals(userId, true)) {
                    dataSet.removeAt(i)
                    notifyDataSetChanged()
                    break
                }
            }
        }
    }

    fun isEmpty() = dataSet.isEmpty()

    class ViewHolder(
        private val binding: BlockedUserItemBinding,
        private val requestManager: RequestManager,
        private val onBlockUserListener: OnBlockUserListener
    ) :
        RecyclerView.ViewHolder(binding.root) {

        internal fun bind(user: User) {
            binding.ivUserAvatar.loadCircle(
                requestManager,
                GapoThumbPattern.AVATAR_MEDIUM_SIZE.parse(user.avatar, user.avatarThumbPattern),
                user.displayName.orEmpty(),
                GapoAutoDimens._64dp
            )
            binding.tvUserName.text = user.displayName
            binding.ivUserAvatar.setDebouncedClickListener { unblockUser(user) }
            binding.tvUserName.setDebouncedClickListener { unblockUser(user) }
            binding.btnUnblock.setDebouncedClickListener { unblockUser(user) }
        }

        fun onViewRecycled() {
            requestManager.clear(binding.ivUserAvatar)
        }

        private fun unblockUser(user: User) {
            onBlockUserListener.onUnblockUser(user.id, user.displayName)
        }
    }

    interface OnBlockUserListener {
        fun onUnblockUser(userId: String, userDisplayName: String?)
    }
}
