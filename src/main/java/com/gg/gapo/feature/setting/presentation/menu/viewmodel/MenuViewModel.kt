package com.gg.gapo.feature.setting.presentation.menu.viewmodel

import android.app.Application
import androidx.core.os.bundleOf
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.liveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.gg.gapo.core.eventbus.friend.FriendHaveNewRequestBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.call.CallV2LoggerDeepLink
import com.gg.gapo.core.navigation.deeplink.search.SearchDeepLink
import com.gg.gapo.core.navigation.deeplink.settings.SettingsLanguageDeepLink
import com.gg.gapo.core.navigation.deeplink.timekeeping.NewTimekeepingDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileQRCodeDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceSwitchDeepLink
import com.gg.gapo.core.okhttp.cache.GapoReponseRequestLogger
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.image.thumbpattern.GapoThumbPattern
import com.gg.gapo.core.user.domain.model.request.UpdateUserProfileRequestModel
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.domain.model.FeatureModel
import com.gg.gapo.core.workspace.domain.model.WorkspaceSwitchedFrom
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.feature.setting.domain.friend.usecase.FetchUnseenNewFriendCountUseCase
import com.gg.gapo.feature.setting.domain.friend.usecase.MarkSeenNewFriendUseCase
import com.gg.gapo.feature.setting.presentation.menu.viewdata.MenuViewData
import io.sentry.Sentry
import io.sentry.SentryEvent
import io.sentry.SentryLevel
import io.sentry.protocol.Message
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

/**
 * <AUTHOR>
 * @since 22/07/2022
 */
internal class MenuViewModel(
    private val application: Application,
    private val workspaceManager: WorkspaceManager,
    private val userManager: UserManager,
    val onPremiseManager: OnPremiseManager,
    private val fetchUnseenNewFriendCountUseCase: FetchUnseenNewFriendCountUseCase,
    private val markSeenNewFriendUseCase: MarkSeenNewFriendUseCase,
    private val gapoReponseRequestLogger: GapoReponseRequestLogger,
    isBackButtonVisible: Boolean
) : ViewModel() {

    private val _backButtonVisibleLiveData = MutableLiveData(isBackButtonVisible)
    val backButtonVisibleLiveData: LiveData<Boolean> = _backButtonVisibleLiveData

    val currentWorkspaceLiveData = workspaceManager.currentWorkspaceLiveData
        .distinctUntilChanged()

    val currentWorkspaceVisibleLiveData =
        workspaceManager.workspacesLiveData.map { it.size > 1 }

    val currentWorkspaceNameLiveData =
        workspaceManager.currentWorkspaceLiveData.map { it.name }

    val currentWorkspaceAvatarLiveData =
        currentWorkspaceLiveData.map { it.avatar }

    val currentWorkspaceRoleLiveData = currentWorkspaceLiveData.map {
        it.rolePermission?.role?.name.orEmpty().ifBlank {
            if (it.role.isAdmin) {
                GapoStrings.shared_admin
            } else {
                GapoStrings.shared_member
            }
        }
    }

    private val isAdminCurrentWorkspaceLiveData = currentWorkspaceLiveData.switchMap {
        liveData {
            emit(it.role.isAdmin)
        }
    }

    private val hasPermissionManageMember = currentWorkspaceLiveData.switchMap {
        liveData {
            emit(it.hasPermissionManageMember())
        }
    }

    private val userProfileLiveData = userManager.userProfileLiveData
        .distinctUntilChanged()

    val myAvatarLiveData = userProfileLiveData.map {
        GapoThumbPattern.AVATAR_MEDIUM_SIZE.parse(
            it.avatar,
            it.avatarThumbPattern
        ) to it.displayName
    }

    val myDisplayNameLiveData = userProfileLiveData.map { it.displayName }

    private val _textVersionLiveData = MutableLiveData<String>()
    val textVersionLiveData: LiveData<String> = _textVersionLiveData

    private val _textTimekeepingNowLiveData = MutableLiveData<String>()
    val textTimekeepingNowLiveData: LiveData<String> = _textTimekeepingNowLiveData

    private val _onClickBackEventLiveData = MutableLiveData<Event<Unit>>()
    val onClickBackEventLiveData: LiveData<Event<Unit>> = _onClickBackEventLiveData

    private val _onClickMenuSupportLiveData = MutableLiveData<Event<Unit>>()
    val onClickMenuSupportLiveData: LiveData<Event<Unit>> = _onClickMenuSupportLiveData

    private val _navByDeepLinkEventLiveData = MutableLiveData<Event<GapoDeepLink>>()
    val navByDeepLinkEventLiveData: LiveData<Event<GapoDeepLink>> = _navByDeepLinkEventLiveData

    private val unseenNewFriendCountLiveData = MutableLiveData(0)

    private val _menuItemsLiveData = MediatorLiveData<List<MenuViewData>>()
    val menuItemsLiveData: LiveData<List<MenuViewData>> = _menuItemsLiveData

    private var clickCount = 0
    private var clickJob: Job? = null

    init {
        _textVersionLiveData.value = getAppVersion()
        _menuItemsLiveData.addSource(isAdminCurrentWorkspaceLiveData) { isAdmin ->
            if (hasPermissionManageMember.value != null) {
                setMenuItems(
                    isAdmin,
                    hasPermissionManageMember.value ?: false,
                    unseenNewFriendCountLiveData.value ?: 0
                )
            }
        }

        _menuItemsLiveData.addSource(hasPermissionManageMember) { hasPermission ->
            if (isAdminCurrentWorkspaceLiveData.value != null) {
                setMenuItems(
                    isAdminCurrentWorkspaceLiveData.value ?: false,
                    hasPermission,
                    unseenNewFriendCountLiveData.value ?: 0
                )
            }
        }

        _menuItemsLiveData.addSource(unseenNewFriendCountLiveData) {
            if (hasPermissionManageMember.value != null && isAdminCurrentWorkspaceLiveData.value != null) {
                setMenuItems(
                    isAdminCurrentWorkspaceLiveData.value ?: false,
                    hasPermissionManageMember.value ?: false,
                    it
                )
            }
        }
    }

    fun resetCurrentDateTimekeeping() {
        _textTimekeepingNowLiveData.value = getCurrentDate(System.currentTimeMillis())
    }

    fun onClickBack() {
        _onClickBackEventLiveData.value = Event(Unit)
    }

    fun onClickMyWorkspaces() {
        if (workspaceManager.currentWorkspace?.features?.isEnable(Feature.SWITCH_WORKSPACE) != false) {
            _navByDeepLinkEventLiveData.value = Event(
                WorkspaceSwitchDeepLink(
                    options = GapoDeepLink.Options(
                        bundle = bundleOf(WorkspaceSwitchDeepLink.FROM_EXTRA to WorkspaceSwitchedFrom.MENU.from)
                    )
                )
            )
        }
    }

    fun isDropDownSwitchWorkspaceIconVisible(): Boolean {
        return workspaceManager.currentWorkspace?.features?.isEnable(Feature.SWITCH_WORKSPACE) == true
    }

    fun onClickMyProfile() {
        _navByDeepLinkEventLiveData.value = Event(
            UserProfileDeepLink(userManager.userId)
        )
    }

    fun onClickTimekeeping() {
        _navByDeepLinkEventLiveData.value = Event(NewTimekeepingDeepLink())
    }

    fun onClickQrCode() {
        _navByDeepLinkEventLiveData.value = Event(UserProfileQRCodeDeepLink())
    }

    fun onClickSearch() {
        _navByDeepLinkEventLiveData.value = Event(SearchDeepLink())
    }

    fun onClickMenuItem(menu: MenuViewData) {
        when (menu) {
            is MenuViewData.Support -> {
                _onClickMenuSupportLiveData.value = Event(Unit)
            }

            else -> {
                if (menu is MenuViewData.Friend) {
                    markSeenNewFriend()
                }
                val deepLink = menu.getGapoDeepLink()
                if (deepLink != null) {
                    _navByDeepLinkEventLiveData.value = Event(deepLink)
                }
            }
        }
    }

    fun onClickSwitchLanguage() {
        _navByDeepLinkEventLiveData.value = Event(SettingsLanguageDeepLink())
    }

    fun onClickCallLogger() {
        clickCount++
        clickJob?.cancel()
        clickJob = viewModelScope.launch {
            val resetTimeMillis = 1000L
            delay(resetTimeMillis)
            clickCount = 0
        }

        if (clickCount == 7) {
            clickCount = 0
            _navByDeepLinkEventLiveData.value = Event(CallV2LoggerDeepLink())
        }
    }

    fun updateLanguage(lang: String) {
        viewModelScope.launch {
            userManager.updateUserProfile(
                UpdateUserProfileRequestModel.Builder().lang(lang).build()
            )
        }
    }

    fun fetchUnseenNewFriendCount() {
        viewModelScope.launch {
            when (val result = fetchUnseenNewFriendCountUseCase()) {
                is Result.Success -> {
                    updateHaveNewRequest(result.data)
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    fun updateHaveNewRequest(count: Int) {
        unseenNewFriendCountLiveData.value = count
    }

    private fun markSeenNewFriend() {
        viewModelScope.launch {
            when (val result = markSeenNewFriendUseCase()) {
                is Result.Success -> {
                    FriendHaveNewRequestBusEvent(0).postEvent()
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                }
            }
        }
    }

    private fun setMenuItems(
        isAdminWorkspace: Boolean,
        hasPermissionManageMember: Boolean,
        unseenNewFriendCount: Int
    ) {
        val items = mutableListOf<MenuViewData>()
        items.add(
            MenuViewData.WsManageMember(
//                nameRes = if (isAdminWorkspace && hasPermissionManageMember) {
//                    GapoStrings.setting_menu_ws_manage_member
//                } else GapoStrings.setting_menu_ws_member
                nameRes = GapoStrings.setting_menu_all_member
            )
        )

        val features = workspaceManager.currentWorkspace?.features ?: FeatureModel.DEFAULT

        if (isAdminWorkspace && hasPermissionManageMember && features.isEnable(Feature.INVITE_MEMBER_WORKSPACE)) {
            items.add(MenuViewData.WsAddMember())
        }
        if (features.isEnable(Feature.ADD_FRIEND)) {
            items.add(MenuViewData.Friend(badgeCount = unseenNewFriendCount))
        }
        if (features.isEnable(Feature.SURVEY)) {
            items.add(MenuViewData.Survey())
        }
        if (features.isEnable(Feature.TASK)) {
            items.add(MenuViewData.Task())
        }
        if (features.isEnable(Feature.APPROVAL)) {
            items.add(MenuViewData.Approval())
        }
        if (features.isEnable(Feature.CALENDAR)) {
            items.add(MenuViewData.Meeting())
        }

        if (features.isEnable(Feature.TICKET)) {
            items.add(MenuViewData.Ticket())
        }

        if (features.isEnable(Feature.REWARD_POINTS)) {
            items.add(MenuViewData.Coin())
        }

        items.addAll(
            listOf(
                MenuViewData.Portal(),
                MenuViewData.Settings(),
                MenuViewData.Privacy(),
                MenuViewData.Endorsement(),
                MenuViewData.Support(),
                MenuViewData.Posts()
            )
        )
        _menuItemsLiveData.value = items
    }

    fun isVisibleTimeKeeping() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.TIME_KEEPING) ?: true

    private fun getCurrentDate(
        timeInMillis: Long
    ): String {
        kotlin.runCatching {
            val date = Date(timeInMillis)
            val format: DateFormat = SimpleDateFormat("dd/MM/yyyy")
            format.timeZone = TimeZone.getDefault()
            return format.format(date)
        }.getOrElse {
            return ""
        }
    }

    private fun getAppVersion(): String {
        return try {
            val pInfo = application.packageManager.getPackageInfo(application.packageName, 0)
            pInfo?.versionName.orEmpty()
        } catch (e: Exception) {
            Timber.e(e)
            ""
        }
    }

    fun onClickSubmitReport(content: String) {
        val contextProgressKey = "logs"
        val event = SentryEvent().apply {
            message = Message().apply {
                message =
                    "[${userManager.userProfile?.displayName} - ${userManager.userProfile?.id}] - report log"
            }
            setTag("feature", "report")
            level = SentryLevel.ERROR
        }
        val logs = gapoReponseRequestLogger.getRequestError().toMutableList()
        logs.add(content)
        Sentry.configureScope { scope ->
            scope.setContexts(contextProgressKey, logs)
            Sentry.captureEvent(event)
        }
    }
}
