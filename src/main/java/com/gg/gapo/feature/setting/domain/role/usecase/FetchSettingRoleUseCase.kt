package com.gg.gapo.feature.setting.domain.role.usecase

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.setting.domain.exception.DataNotFoundException
import com.gg.gapo.feature.setting.domain.exception.SettingExceptionHandler
import com.gg.gapo.feature.setting.domain.role.SettingRoleRepository
import com.gg.gapo.feature.setting.domain.role.model.PermissionModel
import com.gg.gapo.feature.setting.domain.role.model.RoleModel

/**
 * <AUTHOR> DucNT
 * @since 13/08/2023
 */
internal class FetchSettingRoleUseCase(
    private val settingRoleRepository: SettingRoleRepository,
    private val settingExceptionHandler: SettingExceptionHandler
) {
    suspend operator fun invoke(): Result<Pair<RoleModel, List<PermissionModel>>> {
        return try {
            val result = settingRoleRepository.fetchSettingRole()
            val role = result.first
            if (role == null) {
                Result.Error(DataNotFoundException(""))
            } else Result.Success(role to result.second)
        } catch (e: Exception) {
            Result.Error(settingExceptionHandler(e))
        }
    }
}
