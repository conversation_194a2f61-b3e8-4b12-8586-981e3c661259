package com.gg.gapo.feature.setting.presentation.menu.viewdata

import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.DiffUtil
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.approval.ApprovalListDeepLink
import com.gg.gapo.core.navigation.deeplink.coin.CoinDeepLink
import com.gg.gapo.core.navigation.deeplink.feed.post.saved.SavedPostDeepLink
import com.gg.gapo.core.navigation.deeplink.group.GroupAchievementDeepLink
import com.gg.gapo.core.navigation.deeplink.meeting.MeetingCreateDeepLink
import com.gg.gapo.core.navigation.deeplink.minitask.MiniTaskListDeepLink
import com.gg.gapo.core.navigation.deeplink.portal.PortalDeepLink
import com.gg.gapo.core.navigation.deeplink.settings.SettingsDeepLink
import com.gg.gapo.core.navigation.deeplink.settings.SettingsTermPolicyDeepLink
import com.gg.gapo.core.navigation.deeplink.survey.SurveyListDeepLink
import com.gg.gapo.core.navigation.deeplink.ticket.TicketDeepLink
import com.gg.gapo.core.navigation.deeplink.user.FriendZoneMyFriendsDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceAddMemberDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceMembersDeepLink
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.feature.setting.R

/**
 * <AUTHOR>
 * @since 22/07/2022
 */
interface MenuViewData {

    val id: String

    val iconRes: Int

    val nameRes: Int

    val badgeCount: Int
        get() = 0

    val isBeta: Boolean
        get() = false

    val isNew: Boolean
        get() = false

    fun getGapoDeepLink(): GapoDeepLink? {
        return null
    }

    fun shallowCopy(): MenuViewData

    fun areItemsTheSame(item: MenuViewData): Boolean {
        return id == item.id
    }

    fun areContentsTheSame(item: MenuViewData): Boolean {
        return iconRes == item.iconRes && nameRes == item.nameRes && badgeCount == item.badgeCount &&
            isBeta == item.isBeta && isNew == item.isNew
    }

    fun getChangePayload(item: MenuViewData): Bundle {
        val bundle = Bundle()
        if (item.nameRes != nameRes) {
            bundle.putBoolean(NAME_RES_CHANGED_EXTRA, true)
        }
        if (item.iconRes != iconRes) {
            bundle.putBoolean(ICON_RES_CHANGED_EXTRA, true)
        }
        if (item.badgeCount != badgeCount || item.isBeta != isNew || item.isNew != isNew) {
            bundle.putBoolean(BADGE_COUNT_CHANGED_EXTRA, true)
        }
        return bundle
    }

    data class WsManageMember(
        override val id: String = "WsManagerMember",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_ws_manage_member,
        @StringRes override val nameRes: Int
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return WorkspaceMembersDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class WsAddMember(
        override val id: String = "WsAddMember",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_ws_add_member,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_ws_add_member
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return WorkspaceAddMemberDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Friend(
        override val id: String = "Friend",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_friend,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_friend,
        override val badgeCount: Int = 0
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            val tabIndex = if (badgeCount > 0) {
                FriendZoneMyFriendsDeepLink.TabIndex.FRIEND
            } else {
                FriendZoneMyFriendsDeepLink.TabIndex.INVITATION
            }
            return FriendZoneMyFriendsDeepLink(
                GapoDeepLink.Options(
                    bundle = bundleOf(FriendZoneMyFriendsDeepLink.TAB_INDEX_EXTRA to tabIndex.index)
                )
            )
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Meeting(
        override val id: String = "Meeting",
        @DrawableRes override val iconRes: Int = GapoDrawables.ic28_shared_meeting,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_meeting
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return MeetingCreateDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Survey(
        override val id: String = "Survey",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_survey,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_survey
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return SurveyListDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Task(
        override val id: String = "Task",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_task,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_task
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return MiniTaskListDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Approval(
        override val id: String = "Approval",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_approval,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_approval
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return ApprovalListDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Settings(
        override val id: String = "Settings",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_setting,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_setting
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return SettingsDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Privacy(
        override val id: String = "Privacy",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_privacy,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_privacy
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return SettingsTermPolicyDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Support(
        override val id: String = "Support",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_support,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_support
    ) : MenuViewData {
        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Posts(
        override val id: String = "SavedPost",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_menu_saved_post,
        @StringRes override val nameRes: Int = GapoStrings.setting_menu_saved_post
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return SavedPostDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Ticket(
        override val id: String = "Ticket",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_ticket,
        @StringRes override val nameRes: Int = GapoStrings.menu_setting_ticket
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return TicketDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Endorsement(
        override val id: String = "Endorsement",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_endorsement,
        @StringRes override val nameRes: Int = GapoStrings.feeds_achievement_header_title
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return GroupAchievementDeepLink(postId = "", ignoreDisplayMode = true)
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Coin(
        override val id: String = "Coin",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_gcoin,
        @StringRes override val nameRes: Int = GapoStrings.menu_setting_gcoin
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return CoinDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    data class Portal(
        override val id: String = "Portal",
        @DrawableRes override val iconRes: Int = R.drawable.setting_ic_portal,
        @StringRes override val nameRes: Int = GapoStrings.menu_setting_portal
    ) : MenuViewData {

        override fun getGapoDeepLink(): GapoDeepLink {
            return PortalDeepLink()
        }

        override fun shallowCopy(): MenuViewData {
            return copy()
        }
    }

    object DiffCallback : DiffUtil.ItemCallback<MenuViewData>() {
        override fun areItemsTheSame(oldItem: MenuViewData, newItem: MenuViewData): Boolean {
            return oldItem.areItemsTheSame(newItem)
        }

        override fun areContentsTheSame(oldItem: MenuViewData, newItem: MenuViewData): Boolean {
            return oldItem.areContentsTheSame(newItem)
        }

        override fun getChangePayload(oldItem: MenuViewData, newItem: MenuViewData): Any {
            return oldItem.getChangePayload(newItem)
        }
    }

    companion object {
        const val BADGE_COUNT_CHANGED_EXTRA = "BADGE_COUNT_CHANGED_EXTRA"
        const val NAME_RES_CHANGED_EXTRA = "NAME_RES_CHANGED_EXTRA"
        const val ICON_RES_CHANGED_EXTRA = "ICON_RES_CHANGED_EXTRA"
    }
}
