package com.gg.gapo.feature.setting.data.session

import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.feature.setting.data.session.remote.SessionRemote
import com.gg.gapo.feature.setting.data.session.remote.response.mapToDomainModel
import com.gg.gapo.feature.setting.data.session.remote.response.request.SessionLogoutDeviceRequestDto
import com.gg.gapo.feature.setting.data.session.remote.response.request.mapToDto
import com.gg.gapo.feature.setting.domain.model.SessionDeviceModel
import com.gg.gapo.feature.setting.domain.model.SessionLogoutAllModel
import com.gg.gapo.feature.setting.domain.model.request.SessionLogoutAllRequestModel
import com.gg.gapo.feature.setting.domain.session.SessionRepository
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @since 11/17/22
 */
internal class SessionRepositoryImpl(
    private val sessionRemote: SessionRemote,
    private val appDispatchers: CoroutineDispatchers
) : SessionRepository {
    override suspend fun fetchListDevices(): List<SessionDeviceModel> {
        return withContext(appDispatchers.io) {
            sessionRemote.fetchListDevices().mapNotNull {
                it.mapToDomainModel()
            }
        }
    }

    override suspend fun logoutAllDevices(request: SessionLogoutAllRequestModel): SessionLogoutAllModel? {
        return withContext(appDispatchers.io) {
            sessionRemote.logoutAllDevices(request.mapToDto())?.mapToDomainModel()
        }
    }

    override suspend fun logoutAssignDevice(deviceId: String): String? {
        return withContext(appDispatchers.io) {
            sessionRemote.logoutAssignDevice(SessionLogoutDeviceRequestDto(deviceId))
        }
    }
}
