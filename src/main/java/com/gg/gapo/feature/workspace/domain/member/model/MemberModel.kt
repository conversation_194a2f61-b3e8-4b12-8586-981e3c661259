package com.gg.gapo.feature.workspace.domain.member.model

/**
 * <AUTHOR>
 */
internal data class MemberModel(
    val id: String,
    val displayName: String,
    val email: String,
    val phoneNumber: String,
    val birth: BirthModel,
    val gender: MemberGender,
    val avatar: String,
    val avatarThumb: String,
    val role: MemberRole,
    val company: String,
    val status: MemberStatus,
    val state: MemberState,
    val loginType: LoginType,
    val companyName: String,
    val identifierCode: String,
    val listDepartment: List<PositionModel>,
    val customInfoList: List<CustomInfoModel>,
    val employeeCode: String,
    val lastBan: Long
)
