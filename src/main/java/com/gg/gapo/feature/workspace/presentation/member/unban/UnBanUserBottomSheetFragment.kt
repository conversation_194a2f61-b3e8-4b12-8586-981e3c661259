package com.gg.gapo.feature.workspace.presentation.member.unban

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.workspace.databinding.FragmentUnbanUserBinding
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.MemberContentViewData
import com.gg.gapo.feature.workspace.utils.DateUtils
import com.gg.gapo.feature.workspace.utils.DateUtils.convertToDateByPattern

/**
 * <AUTHOR>
 */
internal class UnBanUserBottomSheetFragment internal constructor() : GapoBottomSheetFragment() {

    private var binding by autoCleared<FragmentUnbanUserBinding>()

    private var listener: UnBanUserCallback? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentUnbanUserBinding.inflate(inflater, container, false)
        val member = requireArguments().parcelable<MemberContentViewData>(MEMBER_ARG)!!

        if (System.currentTimeMillis() - member.lastBan > 3600000) {
            binding.tvDescription.isVisible = true
            binding.tvContent1.isVisible = false
            binding.tvUnderstood.isVisible = false
            binding.tvContent2.isVisible = false
            binding.tvAsk.text = context?.getString(
                GapoStrings.workspace_dialog_unban_title,
                member.name
            ).orEmpty()
        } else {
            binding.lnConfirm.isVisible = false
            binding.tvUnderstood.isVisible = true
            binding.tvDescription.isVisible = false
            binding.tvContent1.isVisible = true
            binding.tvContent2.isVisible = true
            binding.icUnban.setColorFilter(
                ContextCompat.getColor(
                    requireActivity(),
                    GapoColors.criticalPrimary
                ),
                android.graphics.PorterDuff.Mode.SRC_IN
            )

            val lastBanHour = member.lastBan.convertToDateByPattern(DateUtils.HOUR_FORMAT)
            val lastBanDate = member.lastBan.convertToDateByPattern(DateUtils.DEFAULT_FORMAT_DATE)

            val reactiveTime = member.lastBan + 3600000
            val reactiveHour = reactiveTime.convertToDateByPattern(DateUtils.HOUR_FORMAT)
            val reactiveDate = reactiveTime.convertToDateByPattern(DateUtils.DEFAULT_FORMAT_DATE)

            binding.tvContent1.setText(
                context?.getString(
                    GapoStrings.workspace_dialog_reactive_member_content_1,
                    lastBanHour,
                    lastBanDate
                ).orEmpty()
            )
            binding.tvContent2.setText(
                context?.getString(
                    GapoStrings.workspace_dialog_reactive_member_content_2,
                    reactiveHour,
                    reactiveDate
                ).orEmpty()
            )

            binding.tvAsk.text = context?.getString(
                GapoStrings.workspace_dialog_cannot_unban_title,
                member.name
            ).orEmpty()
        }
        binding.tvUnderstood.setDebouncedClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvCancel.setDebouncedClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvConfirm.setDebouncedClickListener {
            listener?.onConfirm(member)
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
    }

    companion object {
        private const val MEMBER_ARG = "MEMBER_ARG"

        fun createInstance(
            member: MemberContentViewData,
            listener: UnBanUserCallback
        ) = UnBanUserBottomSheetFragment()
            .apply {
                arguments = bundleOf(
                    MEMBER_ARG to member
                )
                this.listener = listener
            }
    }

    interface UnBanUserCallback {
        fun onConfirm(member: MemberContentViewData)
    }
}
