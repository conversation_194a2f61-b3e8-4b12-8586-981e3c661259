package com.gg.gapo.feature.workspace.presentation.member.list.search

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.core.os.postDelayed
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerMessageDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileDeepLink
import com.gg.gapo.core.ui.GapoIntegers
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.ui.snackbar.makePositiveSnackbar
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.feature.workspace.databinding.FragmentSearchMemberBinding
import com.gg.gapo.feature.workspace.domain.member.model.MemberRole
import com.gg.gapo.feature.workspace.eventbus.*
import com.gg.gapo.feature.workspace.presentation.member.adapter.ListMemberAdapter
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment.Companion.TYPE_OTHER_DOMAIN
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment.Companion.TYPE_SAME_DOMAIN
import com.gg.gapo.feature.workspace.presentation.member.bottomsheet.reset.ResetPasswordForStaffBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.common.option_menu.MemberOptionsBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.common.option_menu.generateMemberOptions
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.MemberContentViewData
import com.gg.gapo.feature.workspace.presentation.member.common.viewmodel.MemberOptionViewModel
import com.gg.gapo.feature.workspace.presentation.member.list.search.viewmodel.SearchMemberViewModel
import com.gg.gapo.feature.workspace.presentation.member.list.total.ListMemberViewModel
import com.gg.gapo.feature.workspace.presentation.member.unban.UnBanUserBottomSheetFragment
import com.gg.gapo.feature.workspace.utils.*
import com.gg.gapo.feature.workspace.utils.DialogUtils.showDialogCancelInvitation
import com.gg.gapo.feature.workspace.utils.DialogUtils.showDialogDeleteUser
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 */
internal class SearchMemberFragment :
    Fragment(),
    MemberOptionsBottomSheetFragment.Listener {

    private var binding by autoCleared<FragmentSearchMemberBinding>()
    private var memberAdapter by autoCleared<ListMemberAdapter>()
    private val searchViewModel by viewModel<SearchMemberViewModel>()
    private val memberOptionViewModel by viewModel<MemberOptionViewModel>()

    private val memberShareViewModel by sharedViewModel<ListMemberViewModel>()

    private val args by navArgs<SearchMemberFragmentArgs>()

    private val currentTab by lazy { args.currentTab }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onPause() {
        super.onPause()
        binding.searchView.hideKeyboard()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSearchMemberBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        searchViewModel.currentTab = currentTab
        initRcvSearch()
        initSearchView()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.toolbar.setNavigationOnClickListener {
            binding.searchView.hideKeyboard()
            findNavController().popBackStack()
        }

        subscribeObserver()
    }

    private fun subscribeObserver() {
        searchViewModel.memberItemsLiveData.observe(viewLifecycleOwner) {
            memberAdapter.submitList(it)
        }
    }

    private fun initSearchView() {
        binding.searchView.requestFocus()
        Handler(Looper.getMainLooper()).postDelayed(200) {
            view?.showKeyboard()
        }

        binding.searchView.imeOptions = EditorInfo.IME_ACTION_DONE
        binding.searchView.setOnEditorActionListener { _, _, _ ->
            // do nothing
            view?.hideKeyboard()
            return@setOnEditorActionListener true
        }

        binding.searchView.addTextChangedListener(
            DebounceQueryTextListener(
                requireContext().resources.getInteger(GapoIntegers.search_debounce_value).toLong(),
                lifecycleScope
            ) { text ->
                if (text.isEmpty()) {
                    searchViewModel.clearData(true)
                } else {
                    searchViewModel.searchMembers(text, memberShareViewModel.getTreeSelectedId())
                }
            }
        )
    }

    private fun initRcvSearch() {
        binding.listMembers.run {
            itemAnimator = null
            val layoutManager = LinearLayoutManager(requireContext())
                .also { layoutManager = it }
            adapter =
                ListMemberAdapter(
                    listMemberViewModel = memberShareViewModel,
                    userId = searchViewModel.userId,
                    requestManager = GapoGlide.with(this),
                    isAdminAndHasPermission = searchViewModel.isAdminCurrentWorkspace,
                    onClickOption = { position, item ->
                        hideKeyboard()
                        onMemberOptionSelect(position, item)
                    },
                    onClickChat = { _, item ->
                        navByDeepLink(
                            MessengerMessageDeepLink(
                                userId = item.id
                            )
                        )
                    },
                    onViewProfile = { _, item ->
                        navByDeepLink(
                            UserProfileDeepLink(
                                item.id
                            )
                        )
                    }
                ).also {
                    memberAdapter = it
                }

            addOnScrollListener(object : InfiniteScrollListener(layoutManager, 15) {
                override fun onLoadMore() {
                    searchViewModel.fetchNextSearchResult(memberShareViewModel.getTreeSelectedId())
                }

                override fun isDataLoading(): Boolean {
                    return searchViewModel.isDataFetching()
                }
            })
        }
    }

    private fun onMemberOptionSelect(position: Int, item: MemberContentViewData) {
        MemberOptionsBottomSheetFragment.createInstance(
            generateMemberOptions(
                searchViewModel.userId,
                searchViewModel.isAdminCurrentWorkspace,
                item,
                searchViewModel.currentWorkspaceDomains,
                searchViewModel.currentWorkspace
            ),
            position
        ).show(childFragmentManager, null)
    }

    override fun onMessage(member: MemberContentViewData, adapterPosition: Int) {
        navByDeepLink(
            MessengerMessageDeepLink(userId = member.id)
        )
    }

    override fun onSeeProfile(member: MemberContentViewData, adapterPosition: Int) {
        navByDeepLink(
            UserProfileDeepLink(
                member.id
            )
        )
    }

    override fun onEditUser(member: MemberContentViewData, adapterPosition: Int) {
        memberShareViewModel.getTreeSelected()?.let {
            context?.navToEditMember(
                member,
                it
            )
        }
    }

    override fun onAddAdmin(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.updateMemberRole(
            member,
            searchViewModel.currentWorkspaceId,
            MemberRole.ADMIN
        )
    }

    override fun onRemoveAdmin(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.updateMemberRole(
            member,
            searchViewModel.currentWorkspaceId,
            MemberRole.MEMBER
        )
    }

    override fun onDisableAccount(member: MemberContentViewData, adapterPosition: Int) {
        BanUserBottomSheetFragment.createInstance(
            member,
            searchViewModel.currentWorkspaceName,
            if (member.isSameDomain) TYPE_SAME_DOMAIN else TYPE_OTHER_DOMAIN,
            object : BanUserBottomSheetFragment.BanUserCallback {
                override fun onConfirm(member: MemberContentViewData) {
                    memberOptionViewModel.banMember(member)
                }
            }
        ).show(childFragmentManager, null)
    }

    override fun onEnableAccount(member: MemberContentViewData, adapterPosition: Int) {
        UnBanUserBottomSheetFragment.createInstance(
            member,
            object : UnBanUserBottomSheetFragment.UnBanUserCallback {
                override fun onConfirm(member: MemberContentViewData) {
                    memberOptionViewModel.unBanMember(member)
                }
            }
        ).show(childFragmentManager, null)
    }

    override fun onRemoveAccount(member: MemberContentViewData, adapterPosition: Int) {
        context?.showDialogDeleteUser({
            memberOptionViewModel.deleteUser(member.id)
        }, viewLifecycleOwner)
    }

    override fun onAddFriend(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.addFriend(member)
    }

    override fun onCancelFriendRequest(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.cancelFriendRequestUseCase(member)
    }

    override fun onAcceptFriendRequest(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.acceptFriendRequest(member)
    }

    override fun onResendEmail(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.resendEmail(member.id) {
            showSnackbar(getString(GapoStrings.workspace_email_resent_message))
        }
    }

    override fun onCancelInvitation(member: MemberContentViewData, adapterPosition: Int) {
        context?.showDialogCancelInvitation(member.name, {
            memberOptionViewModel.cancelInvitation(member.id) {
                showSnackbar(getString(GapoStrings.workspace_cancel_invitation_success))
            }
        }, viewLifecycleOwner)
    }

    override fun onResetPasswordForStaff(member: MemberContentViewData, adapterPosition: Int) {
        ResetPasswordForStaffBottomSheetFragment.newInstance(
            member
        ).show(childFragmentManager, null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onAddAdminSuccess(event: WsAddAdminBusEvent) {
        searchViewModel.updateMemberRole(event.user.id, MemberRole.ADMIN)
        GapoToast.makePositive(
            requireContext(),
            getString(GapoStrings.workspace_admin_added_message),
            Toast.LENGTH_LONG
        ).show()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onRemoveAdminSuccess(event: WsRemoveAdminBusEvent) {
        searchViewModel.updateMemberRole(event.user.id, MemberRole.MEMBER)
        GapoToast.makePositive(
            requireContext(),
            getString(GapoStrings.workspace_admin_cancelled_message),
            Toast.LENGTH_LONG
        ).show()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onBanUser(data: WsBanUserBusEvent) {
        if (data.isSuccess) {
            searchViewModel.banUser(data.member.id)
            makePositiveSnackbar(data.message)?.showOnTop()
        } else {
            makeNegativeSnackbar(data.message)?.showOnTop()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onUnBanUserSuccess(data: WsUnbanUserBusEvent) {
        if (data.isSuccess && data.member.isSameDomain) {
            searchViewModel.unBanUser(data.member)
            makePositiveSnackbar(data.message)?.showOnTop()
        } else {
            makeNegativeSnackbar(data.message)?.showOnTop()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onDeleteUser(event: WsDeleteUserBusEvent) {
        searchViewModel.removeMember(event.userId)
        makePositiveSnackbar(getString(GapoStrings.shared_remove_user_success_message))?.showOnTop()
    }

    /**
     * event đc call từ [com.gg.gapo.friendzone.feature.member.presentation.edit.EditMemberFragment]
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEditUserSuccess(event: WsEditUserBusEvent) {
        searchViewModel.updateUser(event.user)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onCancelInvitation(event: WsCancelInviteBusEvent) {
        searchViewModel.removeMember(event.invitationId)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}
