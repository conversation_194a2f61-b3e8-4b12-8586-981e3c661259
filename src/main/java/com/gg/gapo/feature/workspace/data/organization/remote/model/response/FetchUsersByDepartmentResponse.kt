package com.gg.gapo.feature.workspace.data.organization.remote.model.response

import com.gg.gapo.feature.workspace.data.organization.remote.model.UserByDepartmentDto
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 19/08/2021
 */
internal data class FetchUsersByDepartmentResponse(
    @SerializedName("data") val data: List<UserByDepartmentDto>?,
    @SerializedName("links") val links: Links?,
    @SerializedName("staff_total") val totalStaff: Int?
) : OrganizationBaseResponse() {

    data class Links(@SerializedName("next") val next: String?) {

        val nextQueries: Map<String, String>
            get() {
                return try {
                    val listQuery = next?.split("&") ?: return emptyMap()
                    val queries = mutableMapOf<String, String>()
                    listQuery.forEach { child ->
                        val (key, value) = child.split("=")
                        queries[key] = value
                    }
                    queries
                } catch (e: Exception) {
                    emptyMap()
                }
            }
    }
}
