package com.gg.gapo.feature.workspace.presentation.member.common.viewdata

import android.os.Parcelable
import com.gg.gapo.feature.workspace.R
import com.gg.gapo.feature.workspace.domain.member.model.LoginType
import com.gg.gapo.feature.workspace.domain.member.model.MemberRole
import com.gg.gapo.feature.workspace.domain.member.model.MemberState
import com.gg.gapo.feature.workspace.domain.member.model.MemberStatus
import com.gg.gapo.feature.workspace.domain.organization.model.UserByDepartmentModel
import com.gg.gapo.feature.workspace.presentation.model.edit.PositionViewData
import com.gg.gapo.feature.workspace.utils.ThumbPatternSize
import com.gg.gapo.feature.workspace.utils.parseThumbPattern
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 */
@Parcelize
internal data class MemberContentViewData(
    override val itemId: String,
    val id: String,
    var name: String,
    var email: String,
    val avatar: String,
    val company: String,
    var phoneNumber: String,
    var role: MemberRole,
    var status: MemberStatus,
    var lastStatus: MemberStatus, // use for unban
    var state: MemberState,
    val loginType: LoginType,
    val isSameDomain: Boolean, // có chung domain với current workspace k
    var companyName: String,
    var identifierCode: String,
    var listPosition: List<PositionViewData>,
    var customInfoList: List<CustomInfoViewData>,
    var employeeCode: String,
    var lastBan: Long
) : MemberViewData, Parcelable {
    val isInvitation: Boolean
        get() = state == MemberState.PENDING

    val valueIdentifier: String
        get() = when (loginType) {
            LoginType.EMAIL -> email
            LoginType.PHONE -> phoneNumber
            LoginType.IDENTIFIER -> identifierCode
        }

    /** use for mapping from [UserByDepartmentModel] to [MemberContentViewData] */
    constructor(
        itemId: String,
        id: String,
        name: String,
        avatar: String,
        companyName: String,
        identifierCode: String
    ) : this(
        itemId = itemId,
        id = id,
        name = name,
        email = "",
        avatar = avatar,
        company = "",
        phoneNumber = "",
        role = MemberRole.MEMBER,
        status = MemberStatus.ACTIVE,
        state = MemberState.ACTIVE,
        lastStatus = MemberStatus.ACTIVE,
        loginType = LoginType.EMAIL, // k cần care
        isSameDomain = false, // k cần care,
        companyName = companyName,
        identifierCode = identifierCode,
        listPosition = emptyList(),
        customInfoList = emptyList(),
        employeeCode = "",
        lastBan = 0L
    )

    override val layoutRes: Int
        get() = R.layout.workspace_member_item

    override fun areItemsTheSame(item: MemberViewData): Boolean {
        return if (item !is MemberContentViewData) false
        else item.itemId == itemId
    }

    override fun areContentsTheSame(item: MemberViewData): Boolean {
        return if (item !is MemberContentViewData) false
        else {
            item.name == name && item.avatar == avatar && item.role == role &&
                item.status == status && item.state == state && item.email == email &&
                item.phoneNumber == phoneNumber && item.loginType == loginType &&
                item.isSameDomain == isSameDomain && item.identifierCode == identifierCode &&
                item.listPosition == listPosition && item.customInfoList == customInfoList &&
                item.employeeCode == employeeCode && item.lastBan == lastBan
        }
    }

    override fun shallowCopy(): MemberViewData {
        return copy()
    }

    fun copyFrom(item: MemberContentViewData): MemberContentViewData {
        this.name = item.name
        this.phoneNumber = item.phoneNumber
        this.companyName = item.companyName
        this.email = item.email
        this.identifierCode = item.identifierCode
        this.listPosition = item.listPosition.map { it.copy() }
        this.customInfoList = item.customInfoList.map { it.copy() }
        this.employeeCode = item.employeeCode
        return copy()
    }
}

internal fun UserByDepartmentModel.mapToViewData(): MemberContentViewData {
    return MemberContentViewData(
        itemId = "item_id_$id",
        id = id,
        name = userName,
        avatar = parseThumbPattern(
            avatar,
            avatarThumbPattern,
            ThumbPatternSize.AVATAR_MEDIUM_SIZE
        ).orEmpty(),
        companyName = "",
        identifierCode = ""
    )
}

internal fun List<UserByDepartmentModel>.mapToDomain() = map { it.mapToViewData() }
