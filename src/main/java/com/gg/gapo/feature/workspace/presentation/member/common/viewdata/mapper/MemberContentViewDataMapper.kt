package com.gg.gapo.feature.workspace.presentation.member.common.viewdata.mapper

import com.gg.gapo.feature.workspace.domain.member.model.*
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.MemberContentViewData
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.mapToViewData
import com.gg.gapo.feature.workspace.presentation.model.edit.mapToViewData
import com.gg.gapo.feature.workspace.utils.ThumbPatternSize
import com.gg.gapo.feature.workspace.utils.parseThumbPattern

/**
 * <AUTHOR>
 */

internal fun MemberModel.mapToViewData(
    currentWsDomains: List<String>
): MemberContentViewData {
    val isSameDomain = when (loginType) {
        LoginType.PHONE -> {
            false
        }
        LoginType.IDENTIFIER -> {
            true
        }
        else -> {
            val memberDomain = try {
                email.substring(email.lastIndexOf("@") + 1)
            } catch (e: Exception) {
                ""
            }
            currentWsDomains.contains(memberDomain)
        }
    }

    return MemberContentViewData(
        itemId = "mc_$id",
        id = id,
        name = displayName,
        email = email,
        avatar = parseThumbPattern(
            avatar,
            avatarThumb,
            ThumbPatternSize.AVATAR_MEDIUM_SIZE
        ).orEmpty(),
        company = company,
        phoneNumber = phoneNumber,
        role = role,
        status = status,
        lastStatus = status,
        state = state,
        loginType = loginType,
        isSameDomain = isSameDomain,
        companyName = companyName,
        identifierCode = identifierCode,
        listPosition = listDepartment.map {
            it.mapToViewData()
        },
        customInfoList = customInfoList.map {
            it.mapToViewData()
        },
        employeeCode = employeeCode,
        lastBan = lastBan
    )
}

internal fun MemberModel.mapToViewData(
    currentWsDomains: List<String>,
    requestModel: MemberRequestModel
): MemberContentViewData {
    val isSameDomain = when (loginType) {
        LoginType.PHONE -> {
            false
        }
        LoginType.IDENTIFIER -> {
            true
        }
        else -> {
            val memberDomain = try {
                email.substring(email.lastIndexOf("@") + 1)
            } catch (e: Exception) {
                ""
            }
            currentWsDomains.contains(memberDomain)
        }
    }

    return MemberContentViewData(
        itemId = "mc_$id",
        id = id,
        name = requestModel.displayName,
        email = requestModel.email.orEmpty(),
        avatar = parseThumbPattern(
            avatar,
            avatarThumb,
            ThumbPatternSize.AVATAR_MEDIUM_SIZE
        ).orEmpty(),
        company = company,
        phoneNumber = requestModel.phoneNumber.orEmpty(),
        role = role,
        status = status,
        lastStatus = status,
        state = state,
        loginType = loginType,
        isSameDomain = isSameDomain,
        companyName = companyName,
        identifierCode = identifierCode,
        listPosition = requestModel.listDepartments?.map {
            it.mapToViewData()
        }.orEmpty(),
        customInfoList = requestModel.customInfoList?.map {
            it.mapToViewData()
        }.orEmpty(),
        employeeCode = requestModel.employeeCode.orEmpty(),
        lastBan = lastBan
    )
}

internal fun List<MemberModel>.mapToViewData(currentWsDomains: List<String>): List<MemberContentViewData> {
    return map { it.mapToViewData(currentWsDomains) }
}
