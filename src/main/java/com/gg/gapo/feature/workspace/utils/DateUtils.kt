package com.gg.gapo.feature.workspace.utils

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @since 9/27/22
 */
internal object DateUtils {
    const val DEFAULT_FORMAT_DATE = "dd/MM/yyyy"
    const val HOUR_FORMAT = "HH:mm"
    const val DEFAULT_FORMAT_DATE_LINE_REVERSE_PATTERN = "yyyy-MM-dd"

    fun String.convertToMillisecondTime(pattern: String): Long {
        return kotlin.runCatching {
            val dateFormat = SimpleDateFormat(pattern)
            dateFormat.parse(this)?.time ?: 0
        }.getOrElse {
            0
        }
    }

    fun Long.convertToDateByPattern(pattern: String): String {
        val date = Date(this)
        val format: DateFormat = SimpleDateFormat(pattern)
        format.timeZone = TimeZone.getDefault()
        return format.format(date)
    }

    fun String.changeFormatDateByPattern(oldPattern: String, newPattern: String): String {
        return kotlin.runCatching {
            this.convertToMillisecondTime(oldPattern).convertToDateByPattern(newPattern)
        }.getOrElse {
            ""
        }
    }
}
