package com.gg.gapo.feature.workspace.data.member.remote.model

import com.gg.gapo.feature.workspace.domain.member.model.*
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since 18/11/2021
 */
internal data class InvitationDto(
    @SerializedName("invitations") val invitations: List<InvitationDetailDto>?,
    @SerializedName("total") val total: Int
) {

    /**
     "_id": "string",
     "workspace_id": "string",
     "user_id": 0,
     "email": "string",
     "phone_number": "string",
     "data": {
     "email": "string",
     "phone_number": "string",
     "display_name": "string",
     "department_id": "string",
     "department": "string",
     "title": "string",
     "role_id": "string"
     },
     "status": 1,
     "expire_at": 0,
     "created_at": 0,
     "created_by": 0
     */
    data class InvitationDetailDto(
        @SerializedName("_id") val id: String?,
        @SerializedName("email") val email: String?,
        @SerializedName("phone_number") val phoneNumber: String?,
        @SerializedName("data") val data: Data?
    ) {
        data class Data(@SerializedName("display_name") val displayName: String)
    }
}

private fun InvitationDto.InvitationDetailDto.mapToDomain(): MemberModel? {
    if ((email == null && phoneNumber == null) || id == null) return null
    return MemberModel(
        id = id,
        displayName = data?.displayName.orEmpty(),
        email = email.orEmpty(),
        phoneNumber = phoneNumber.orEmpty(),
        birth = BirthModel.DEFAULT,
        gender = MemberGender.NA,
        avatar = "",
        avatarThumb = "",
        role = MemberRole.MEMBER,
        company = "",
        status = MemberStatus.ACTIVE,
        state = MemberState.PENDING,
        loginType = LoginType.EMAIL,
        companyName = "",
        identifierCode = "",
        listDepartment = listOf(),
        customInfoList = listOf(),
        employeeCode = "",
        lastBan = 0L
    )
}

private fun List<InvitationDto.InvitationDetailDto>.mapToDomain() = mapNotNull { it.mapToDomain() }

internal fun InvitationDto.mapToDomain(): MemberWrapperModel {
    return MemberWrapperModel(invitations?.mapToDomain().orEmpty(), total)
}
