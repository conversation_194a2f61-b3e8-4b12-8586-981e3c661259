package com.gg.gapo.feature.workspace.data.member.remote.model

import com.gg.gapo.core.user.data.remote.model.UserProfileInfoDto
import com.gg.gapo.feature.workspace.domain.member.model.*
import com.google.gson.annotations.SerializedName
import java.time.Instant

/**
 * <AUTHOR>
 */
internal data class MemberDto(
    @SerializedName("id") val id: Long?,
    @SerializedName("display_name") val displayName: String?,
    @SerializedName("email") val email: String?,
    @SerializedName("phone_number") val phoneNumber: String?,
    @SerializedName("birth") val birth: BirthDto?,
    @SerializedName("gender") val gender: MemberGender?,
    @SerializedName("avatar") val avatar: String?,
    @SerializedName("avatar_thumb_pattern") val avatarThumb: String?,
    @SerializedName("role") val role: MemberRole?,
    @SerializedName("company") val company: String?,
    @SerializedName("status") val status: MemberStatus?,
    @SerializedName("state") val state: MemberState?,
    @SerializedName("login_type") val loginType: LoginType?,
    @SerializedName("company_name") val companyName: String?,
    @SerializedName("identifier_code") val identifierCode: String?,
    @SerializedName("list_departments") val listDepartment: List<UserProfileInfoDto.WorkDataDto.PositionDto>?,
    @SerializedName("custom_info") val customInfo: List<CustomInfoDto>?,
    @SerializedName("employee_code") val employeeCode: String?,
    @SerializedName("last_ban") val lastBan: String?,

    @Deprecated("Không dùng field workspaceId này nữa") @SerializedName("workspace_id")
    val mainWorkSpaceId: String?
)

internal fun MemberDto.mapToDomain(): MemberModel? {
    if (id == null || displayName == null) return null
    return MemberModel(
        id = id.toString(),
        displayName = displayName,
        email = email.orEmpty(),
        phoneNumber = phoneNumber.orEmpty(),
        birth = birth?.mapToDomain() ?: BirthModel.DEFAULT,
        gender = gender ?: MemberGender.NA,
        avatar = avatar.orEmpty(),
        avatarThumb = avatarThumb.orEmpty(),
        role = role ?: MemberRole.MEMBER,
        company = company.orEmpty(),
        status = status ?: MemberStatus.ACTIVE,
        state = state ?: MemberState.ACTIVE,
        loginType = loginType ?: LoginType.EMAIL,
        companyName = companyName.orEmpty(),
        identifierCode = identifierCode.orEmpty(),
        listDepartment = listDepartment?.map {
            it.mapToDomainModel()
        }.orEmpty(),
        customInfoList = customInfo?.mapNotNull {
            it.mapToDomain()
        }.orEmpty(),
        employeeCode = employeeCode.orEmpty(),
        lastBan = if (lastBan.isNullOrEmpty()) 0L else Instant.parse(lastBan).toEpochMilli()
    )
}

internal fun List<MemberDto>.mapToDomain(): List<MemberModel> {
    return mapNotNull { it.mapToDomain() }
}

internal fun PositionByMemberDto.mapToDomainModel(): PositionModel {
    return PositionModel(
        treeId.orEmpty(),
        departmentId.orEmpty(),
        department.orEmpty(),
        roleId.orEmpty(),
        title.orEmpty()
    )
}

internal fun UserProfileInfoDto.WorkDataDto.PositionDto.mapToDomainModel(): PositionModel {
    return PositionModel(
        treeId.orEmpty(),
        departmentId.orEmpty(),
        department.orEmpty(),
        roleId.orEmpty(),
        title.orEmpty()
    )
}
