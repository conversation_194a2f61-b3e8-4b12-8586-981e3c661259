package com.gg.gapo.feature.workspace.presentation.member.ban

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.workspace.databinding.FragmentBanUserBinding
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.MemberContentViewData
import com.gg.gapo.feature.workspace.utils.DateUtils
import com.gg.gapo.feature.workspace.utils.DateUtils.convertToDateByPattern

/**
 * <AUTHOR>
 */
internal class BanUserBottomSheetFragment internal constructor() : GapoBottomSheetFragment() {

    private var binding by autoCleared<FragmentBanUserBinding>()

    private var listener: BanUserCallback? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBanUserBinding.inflate(inflater, container, false)
        val member = requireArguments().parcelable<MemberContentViewData>(MEMBER_ARG)!!
        val workspaceName = requireArguments().getString(WORK_SPACE_NAME_ARG).orEmpty()
        when (requireArguments().getString(TYPE_ARG)) {
            TYPE_OTHER_DOMAIN -> {
                binding.tvContentWsSameDomain.isVisible = false
                binding.tvAsk.text =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        Html.fromHtml(
                            getString(
                                GapoStrings.workspace_dialog_ban_account_title_not_same_domain,
                                member.valueIdentifier,
                                workspaceName
                            ),
                            HtmlCompat.FROM_HTML_MODE_LEGACY
                        )
                    } else {
                        Html.fromHtml(
                            getString(
                                GapoStrings.workspace_dialog_ban_account_title_not_same_domain,
                                member.valueIdentifier,
                                workspaceName
                            )
                        )
                    }

                binding.tvContent1.setText(
                    context?.getString(
                        GapoStrings.workspace_dialog_ban_description_1
                    ).orEmpty()
                )
            }
            TYPE_SAME_DOMAIN -> {
                binding.tvContentOtherDomain.isVisible = false
                if (System.currentTimeMillis() - member.lastBan > 3600000) {
                    binding.tvUnderstood.isVisible = false
                    binding.lnConfirm.isVisible = true
                    binding.tvAsk.text =
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            Html.fromHtml(
                                getString(GapoStrings.workspace_dialog_ban_account_title, member.name),
                                HtmlCompat.FROM_HTML_MODE_LEGACY
                            )
                        } else {
                            Html.fromHtml(
                                getString(
                                    GapoStrings.workspace_dialog_ban_account_title,
                                    member.name
                                )
                            )
                        }
                    binding.tvContent1.setText(
                        context?.getString(
                            GapoStrings.workspace_dialog_ban_description_1
                        ).orEmpty()
                    )
                } else {
                    binding.tvUnderstood.isVisible = true
                    binding.lnConfirm.isVisible = false
                    val lastBanHour = member.lastBan.convertToDateByPattern(DateUtils.HOUR_FORMAT)
                    val lastBanDate = member.lastBan.convertToDateByPattern(DateUtils.DEFAULT_FORMAT_DATE)

                    val reactiveTime = member.lastBan + 3600000
                    val reactiveHour = reactiveTime.convertToDateByPattern(DateUtils.HOUR_FORMAT)
                    val reactiveDate = reactiveTime.convertToDateByPattern(DateUtils.DEFAULT_FORMAT_DATE)
                    binding.tvAsk.text =
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            Html.fromHtml(
                                getString(GapoStrings.workspace_dialog_cannot_deactivate_title, member.name),
                                HtmlCompat.FROM_HTML_MODE_LEGACY
                            )
                        } else {
                            Html.fromHtml(
                                getString(
                                    GapoStrings.workspace_dialog_cannot_deactivate_title,
                                    member.name
                                )
                            )
                        }
                    binding.tvContent1.setText(
                        context?.getString(
                            GapoStrings.workspace_dialog_ban_member_content_1,
                            lastBanHour,
                            lastBanDate
                        ).orEmpty()
                    )
                    binding.tvContentWsSameDomain.setText(
                        context?.getString(
                            GapoStrings.workspace_dialog_ban_member_content_2,
                            reactiveHour,
                            reactiveDate
                        ).orEmpty()
                    )
                }
            }
            else -> throw IllegalArgumentException()
        }

        binding.tvUnderstood.setDebouncedClickListener {
            dismissAllowingStateLoss()
        }

        binding.tvCancel.setDebouncedClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvConfirm.setDebouncedClickListener {
            listener?.onConfirm(member)
            dismissAllowingStateLoss()
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
    }

    companion object {
        private const val MEMBER_ARG = "MEMBER_ARG"

        private const val TYPE_ARG = "TYPE_ARG"
        private const val WORK_SPACE_NAME_ARG = "WORK_SPACE_NAME_ARG"
        const val TYPE_SAME_DOMAIN = "TYPE_SAME_DOMAIN"
        const val TYPE_OTHER_DOMAIN = "TYPE_OTHER_DOMAIN"

        fun createInstance(
            member: MemberContentViewData,
            workSpaceName: String,
            type: String,
            listener: BanUserCallback
        ) = BanUserBottomSheetFragment()
            .apply {
                arguments = bundleOf(
                    MEMBER_ARG to member,
                    WORK_SPACE_NAME_ARG to workSpaceName,
                    TYPE_ARG to type
                )
                this.listener = listener
            }
    }

    interface BanUserCallback {
        fun onConfirm(member: MemberContentViewData)
    }
}
