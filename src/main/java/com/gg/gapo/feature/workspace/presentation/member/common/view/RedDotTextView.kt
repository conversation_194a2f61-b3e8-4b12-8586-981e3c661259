package com.gg.gapo.feature.workspace.presentation.member.common.view

import android.content.Context
import android.os.Build
import android.text.Html
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import com.gg.gapo.feature.workspace.R

/**
 * <AUTHOR>
 */
internal class RedDotTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val tvContent: AppCompatTextView
        get() = findViewById(R.id.tvContent)

    init {
        init(context, attrs, defStyleAttr)
    }

    private fun init(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) {
        LayoutInflater.from(context).inflate(R.layout.red_dot_text_view, this)

        val typedArray =
            context.theme.obtainStyledAttributes(
                attrs,
                R.styleable.WorkspaceRedDotText,
                defStyleAttr,
                defStyleAttr
            )

        val text = typedArray.getString(R.styleable.WorkspaceRedDotText_textContent).orEmpty()
        val message = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(
                text,
                Html.FROM_HTML_MODE_LEGACY
            )
        } else {
            Html.fromHtml(text)
        }
        tvContent.text = message
    }

    fun setText(s: String) {
        val message = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(
                s,
                Html.FROM_HTML_MODE_LEGACY
            )
        } else {
            Html.fromHtml(s)
        }
        tvContent.text = message
    }
}
