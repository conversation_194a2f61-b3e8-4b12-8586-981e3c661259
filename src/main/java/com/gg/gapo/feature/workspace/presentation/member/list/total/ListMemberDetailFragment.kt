package com.gg.gapo.feature.workspace.presentation.member.list.total

import android.app.Activity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerMessageDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.user.UserProfileDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceDepartmentsDeepLink
import com.gg.gapo.core.navigation.deeplink.workspace.WorkspaceMembersDeepLink
import com.gg.gapo.core.ui.GapoIntegers
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.ui.snackbar.makePositiveSnackbar
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.ui.toast.GapoToast.showOnTop
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.databinding.isVisible
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.view.hideKeyboard
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.workspace.databinding.WorkspaceMemberListDetailFragmentBinding
import com.gg.gapo.feature.workspace.domain.member.model.MemberRole
import com.gg.gapo.feature.workspace.eventbus.*
import com.gg.gapo.feature.workspace.presentation.member.adapter.ListMemberAdapter
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment.Companion.TYPE_OTHER_DOMAIN
import com.gg.gapo.feature.workspace.presentation.member.ban.BanUserBottomSheetFragment.Companion.TYPE_SAME_DOMAIN
import com.gg.gapo.feature.workspace.presentation.member.bottomsheet.reset.ResetPasswordForStaffBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.common.ListMemberItemDecoration
import com.gg.gapo.feature.workspace.presentation.member.common.option_menu.MemberOptionsBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.common.option_menu.generateMemberOptions
import com.gg.gapo.feature.workspace.presentation.member.common.viewdata.MemberContentViewData
import com.gg.gapo.feature.workspace.presentation.member.common.viewmodel.MemberOptionViewModel
import com.gg.gapo.feature.workspace.presentation.member.list.filter.StateFilterBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.member.list.filter.StateFilterBottomSheetFragment.*
import com.gg.gapo.feature.workspace.presentation.member.list.search.DebounceQueryTextListener
import com.gg.gapo.feature.workspace.presentation.member.unban.UnBanUserBottomSheetFragment
import com.gg.gapo.feature.workspace.presentation.organization.department.select.DepartmentBottomSheetFragment.*
import com.gg.gapo.feature.workspace.utils.DialogUtils.showDialogCancelInvitation
import com.gg.gapo.feature.workspace.utils.DialogUtils.showDialogDeleteUser
import com.gg.gapo.feature.workspace.utils.KeyUtils.DEFAULT_DEPARTMENT_ALL_ITEM_ID
import com.gg.gapo.feature.workspace.utils.navToEditMember
import com.gg.gapo.feature.workspace.utils.showOnTop
import com.gg.gapo.feature.workspace.utils.showSnackbar
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * <AUTHOR>
 */
internal class ListMemberDetailFragment :
    Fragment(),
    MemberOptionsBottomSheetFragment.Listener,
    StateFilterCallback {

    private var binding by autoCleared<WorkspaceMemberListDetailFragmentBinding>()

    private val viewModel by sharedViewModel<ListMemberViewModel>()

    private val memberOptionViewModel by viewModel<MemberOptionViewModel>()

    private var memberAdapter by autoCleared<ListMemberAdapter>()

    private val selectDepartmentLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                kotlin.runCatching {
                    val departmentId =
                        result.data?.getStringExtra(WorkspaceDepartmentsDeepLink.DEPARTMENT_ID_NAV_RESULT)

                    val departmentName =
                        result.data?.getStringExtra(WorkspaceDepartmentsDeepLink.DEPARTMENT_NAME_NAV_RESULT)

                    binding.tvSelectDepartment.text = departmentName
                    binding.tvSelectDepartmentUser.text = departmentName

                    if ((departmentId != DEFAULT_DEPARTMENT_ALL_ITEM_ID && viewModel.currentUserStateFilter == USER_FILTER_BANNED)) {
                        viewModel.currentUserStateFilter = USER_FILTER_ALL
                        binding.tvSelectMemberState.text =
                            getString(GapoStrings.workspace_all_members)
                    }

                    viewModel.getFirstData(departmentIdParam = departmentId)
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.currentUserStateFilter =
            arguments?.getString(USER_STATE_FILTER) ?: USER_FILTER_ALL

        loadDataInit()
    }

    private fun loadDataInit() {
        var dataJustAdded: MemberContentViewData? = null
        activity?.intent?.getStringExtra(WorkspaceMembersDeepLink.ADD_USER_EXTRA)?.let {
            dataJustAdded =
                activity?.intent?.parcelable(WorkspaceMembersDeepLink.USER_DATA_ARG) as? MemberContentViewData
        }
        if (dataJustAdded != null) {
            viewModel.currentUserStateFilter = USER_FILTER_PENDING
        }
        viewModel.fetchDataInit(dataJustAdded)
    }

    private fun refreshData() {
        viewModel.clearData(false)
        // chỉ fetch admins nếu state là all

        viewModel.fetchMembers()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = WorkspaceMemberListDetailFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        updateUIHeader()
        initRcvMembers()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState) // must have
        registerEventBus()
        viewModel.memberItemsLiveData.observe(viewLifecycleOwner) {
            memberAdapter.submitList(it)
            binding.swipeRefreshLayout.isRefreshing = false
            binding.groupEmpty.isVisible = it.isNullOrEmpty()
        }

        binding.tvSelectMemberState.setDebouncedClickListener {
            binding.textSearch.clearSearchFocus()
            showStateFilterBottomSheet()
        }

        binding.tvSelectDepartment.setDebouncedClickListener {
            binding.textSearch.clearSearchFocus()
            showDepartmentBottomSheet()
        }

        binding.tvSelectDepartmentUser.setDebouncedClickListener {
            binding.textSearch.clearSearchFocus()
            showDepartmentBottomSheet()
        }

        binding.swipeRefreshLayout.setOnRefreshListener {
            binding.textSearch.clearSearchFocus()
            refreshData()
        }

        binding.textSearch.addTextChangedListener(
            DebounceQueryTextListener(
                requireContext().resources.getInteger(GapoIntegers.search_debounce_value).toLong(),
                viewLifecycleOwner.lifecycleScope
            ) {
                kotlin.runCatching {
                    viewModel.textSearch = it
                    viewModel.clearData(false)
                    viewModel.fetchMembers()
                }
            }
        )

        binding.textSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                binding.textSearch.clearFocus()
            }
            false
        }

        updateUIFilterMember()
        updateUIHeader()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unregisterEventBus()
    }

    private fun updateUIFilterMember() {
        when (viewModel.currentUserStateFilter) {
            USER_FILTER_ALL -> {
                binding.tvSelectMemberState.text = getString(GapoStrings.workspace_all_members)
            }
            USER_FILTER_ACTIVE -> {
                binding.tvSelectMemberState.text = getString(GapoStrings.workspace_active_members)
            }
            USER_FILTER_PENDING -> {
                binding.tvSelectMemberState.text = getString(GapoStrings.workspace_tab_inactive)
            }
            USER_FILTER_BANNED -> {
                binding.tvSelectMemberState.text = getString(GapoStrings.workspace_banned)
            }
        }
    }

    private fun showStateFilterBottomSheet() {
        StateFilterBottomSheetFragment.newInstance(viewModel.currentUserStateFilter)
            .show(childFragmentManager, null)
    }

    private fun showDepartmentBottomSheet() {
        selectDepartmentLauncher.launch(
            WorkspaceDepartmentsDeepLink(
                true,
                GapoDeepLink.Options(
                    bundleOf(
                        WorkspaceDepartmentsDeepLink.TREE_ID_NAV_RESULT to viewModel.getTreeSelectedId(),
                        WorkspaceDepartmentsDeepLink.DEPARTMENT_ID_NAV_RESULT to viewModel.departmentIdSelected
                    )
                )
            ).getIntent(requireContext())
        )
    }

    private fun updateUIHeader() {
        if (viewModel.isAdminCurrentWorkspaceAndHasPermission) {
            binding.clAdminHasOrganizationHeader.isVisible = true
            binding.tvSelectDepartmentUser.isVisible = false
        } else {
            binding.clAdminHasOrganizationHeader.isVisible = false
            binding.tvSelectDepartmentUser.isVisible = true
        }
    }

    private fun initRcvMembers() {
        binding.listMembers.run {
            val layoutManager = LinearLayoutManager(requireContext())
                .also { layoutManager = it }
            adapter = ListMemberAdapter(
                listMemberViewModel = viewModel,
                userId = memberOptionViewModel.userId,
                isAdminAndHasPermission = viewModel.isAdminCurrentWorkspaceAndHasPermission,
                requestManager = GapoGlide.with(this),
                onClickOption = { position, item ->
                    onMemberOptionSelect(position, item)
                },
                onClickChat = { _, item ->
                    navByDeepLink(
                        MessengerMessageDeepLink(userId = item.id)
                    )
                },
                onViewProfile = { _, item ->
                    navByDeepLink(
                        UserProfileDeepLink(
                            item.id
                        )
                    )
                }
            ).also {
                memberAdapter = it
            }
            addItemDecoration(ListMemberItemDecoration(context))

            addOnScrollListener(object : InfiniteScrollListener(layoutManager, 15) {
                override fun onLoadMore() {
                    viewModel.fetchNextMembers()
                }

                override fun isDataLoading(): Boolean {
                    return viewModel.isDataFetching()
                }
            })
        }
    }

    private fun onMemberOptionSelect(position: Int, item: MemberContentViewData) {
        MemberOptionsBottomSheetFragment.createInstance(
            generateMemberOptions(
                memberOptionViewModel.userId,
                viewModel.isAdminCurrentWorkspaceAndHasPermission,
                item,
                viewModel.currentWorkspaceDomains,
                viewModel.currentWorkspace
            ),
            position
        ).show(childFragmentManager, null)
    }

    override fun onViewAllUsers() {
        binding.tvSelectMemberState.text = getString(GapoStrings.workspace_all_members)
        viewModel.currentUserStateFilter = USER_FILTER_ALL
        viewModel.getFirstData(viewModel.departmentIdSelected)
    }

    override fun onViewActiveUsers() {
        binding.tvSelectMemberState.text = getString(GapoStrings.workspace_active_members)
        viewModel.currentUserStateFilter = USER_FILTER_ACTIVE
        viewModel.getFirstData(viewModel.departmentIdSelected)
    }

    override fun onViewPendingUsers() {
        binding.tvSelectMemberState.text = getString(GapoStrings.workspace_tab_inactive)
        viewModel.currentUserStateFilter = USER_FILTER_PENDING
        viewModel.getFirstData(viewModel.departmentIdSelected)
    }

    override fun onViewBannedUsers() {
        binding.tvSelectMemberState.text = getString(GapoStrings.workspace_banned)
        viewModel.currentUserStateFilter = USER_FILTER_BANNED

        val departmentId = DEFAULT_DEPARTMENT_ALL_ITEM_ID
        val departmentName = getString(GapoStrings.workspace_filter_by_department)
        binding.tvSelectDepartment.text = departmentName
        binding.tvSelectDepartmentUser.text = departmentName
        viewModel.getFirstData(departmentIdParam = departmentId)
    }

    override fun onMessage(member: MemberContentViewData, adapterPosition: Int) {
        navByDeepLink(
            MessengerMessageDeepLink(userId = member.id)
        )
    }

    override fun onSeeProfile(member: MemberContentViewData, adapterPosition: Int) {
        navByDeepLink(
            UserProfileDeepLink(
                member.id
            )
        )
    }

    override fun onEditUser(member: MemberContentViewData, adapterPosition: Int) {
        viewModel.getTreeSelected()?.let {
            context?.navToEditMember(member, it)
        }
    }

    override fun onAddAdmin(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.updateMemberRole(
            member,
            viewModel.currentWorkspaceId,
            MemberRole.ADMIN
        )
    }

    override fun onRemoveAdmin(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.updateMemberRole(
            member,
            viewModel.currentWorkspaceId,
            MemberRole.MEMBER
        )
    }

    override fun onDisableAccount(member: MemberContentViewData, adapterPosition: Int) {
        BanUserBottomSheetFragment.createInstance(
            member,
            viewModel.currentWorkspaceName,
            if (member.isSameDomain) TYPE_SAME_DOMAIN else TYPE_OTHER_DOMAIN,
            object : BanUserBottomSheetFragment.BanUserCallback {
                override fun onConfirm(member: MemberContentViewData) {
                    memberOptionViewModel.banMember(member)
                }
            }
        ).show(childFragmentManager, null)
    }

    override fun onEnableAccount(member: MemberContentViewData, adapterPosition: Int) {
        UnBanUserBottomSheetFragment.createInstance(
            member,
            object : UnBanUserBottomSheetFragment.UnBanUserCallback {
                override fun onConfirm(member: MemberContentViewData) {
                    memberOptionViewModel.unBanMember(member)
                }
            }
        ).show(childFragmentManager, null)
    }

    override fun onRemoveAccount(member: MemberContentViewData, adapterPosition: Int) {
        Timber.e("akaizz2 onRemoveAccount")
        context?.showDialogDeleteUser({
            memberOptionViewModel.deleteUser(member.id)
        }, viewLifecycleOwner)
    }

    override fun onAddFriend(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.addFriend(member)
    }

    override fun onCancelFriendRequest(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.cancelFriendRequestUseCase(member)
    }

    override fun onAcceptFriendRequest(member: MemberContentViewData, adapterPosition: Int) {
        memberOptionViewModel.acceptFriendRequest(member)
    }

    override fun onResendEmail(member: MemberContentViewData, adapterPosition: Int) {
        // member id ở đây là invitation id
        memberOptionViewModel.resendEmail(member.id) {
            showSnackbar(getString(GapoStrings.workspace_email_resent_message))
        }
    }

    override fun onCancelInvitation(member: MemberContentViewData, adapterPosition: Int) {
        context?.showDialogCancelInvitation(member.name, {
            memberOptionViewModel.cancelInvitation(member.id) {
                showSnackbar(getString(GapoStrings.workspace_cancel_invitation_success))
            }
        }, viewLifecycleOwner)
    }

    override fun onResetPasswordForStaff(member: MemberContentViewData, adapterPosition: Int) {
        ResetPasswordForStaffBottomSheetFragment.newInstance(
            member
        ).show(childFragmentManager, null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onAddAdminSuccess(event: WsAddAdminBusEvent) {
        viewModel.updateMemberRole(event.user, MemberRole.ADMIN)
        GapoToast.makePositive(
            requireContext(),
            getString(GapoStrings.workspace_admin_added_message),
            Toast.LENGTH_LONG
        ).show()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onRemoveAdminSuccess(event: WsRemoveAdminBusEvent) {
        viewModel.updateMemberRole(event.user, MemberRole.MEMBER)
        GapoToast.makePositive(
            requireContext(),
            getString(GapoStrings.workspace_admin_cancelled_message),
            Toast.LENGTH_LONG
        ).show()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onBanUser(data: WsBanUserBusEvent) {
        if (data.isSuccess) {
            viewModel.banUser(data.member)
            makePositiveSnackbar(data.message)?.showOnTop()
        } else {
            makeNegativeSnackbar(data.message)?.showOnTop()
        }
    }

    // data.member.isSameDomain dang ko can thiet bo di de chay dung luong
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onUnBanUserSuccess(data: WsUnbanUserBusEvent) {
        if (data.isSuccess) {
            viewModel.unBanUser(data.member)
            makePositiveSnackbar(data.message)?.showOnTop()
        } else {
            makeNegativeSnackbar(data.message)?.showOnTop()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onDeleteUser(event: WsDeleteUserBusEvent) {
        viewModel.removeMember(event.userId)
        makePositiveSnackbar(getString(GapoStrings.shared_remove_user_success_message))?.showOnTop()
    }

    /**
     * event đc call từ [com.gg.gapo.feature.workspace.presentation.edit.EditMemberFragment]
     * finish để [com.gg.gapo.features.menu.MenuFragment] gọi, load lại từ đầu
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onAddUserEvent(event: WsReloadWhenAddUserEvent) {
        activity?.finish()
    }

    /**
     * event đc call từ [com.gg.gapo.feature.workspace.presentation.edit.EditMemberFragment]
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEditUserSuccess(event: WsEditUserBusEvent) {
        viewModel.updateUser(event.user)
        kotlin.runCatching {
            Handler(Looper.getMainLooper()).postDelayed({
                GapoToast.makeNormal(
                    requireContext(),
                    String.format(
                        getString(GapoStrings.workspace_edit_info_member_success_message),
                        event.user.name
                    ),
                    Toast.LENGTH_LONG
                ).showOnTop()
            }, 100)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onCancelInvitation(event: WsCancelInviteBusEvent) {
        viewModel.removeMember(event.invitationId)
    }

    private fun EditText.clearSearchFocus() {
        hideKeyboard()
        clearFocus()
    }

    companion object {

        private const val USER_STATE_FILTER = "UserStateFilter"

        const val USER_FILTER_ALL = "USER_FILTER_ALL"
        const val USER_FILTER_ACTIVE = "USER_FILTER_ACTIVE"
        const val USER_FILTER_PENDING = "USER_FILTER_PENDING"
        const val USER_FILTER_BANNED = "USER_FILTER_BANNED"

        fun newInstance(tab: String) = ListMemberDetailFragment()
            .apply {
                arguments = bundleOf(USER_STATE_FILTER to tab)
            }
    }
}
