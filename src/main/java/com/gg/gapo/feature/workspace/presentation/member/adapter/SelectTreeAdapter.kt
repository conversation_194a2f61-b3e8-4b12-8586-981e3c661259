package com.gg.gapo.feature.workspace.presentation.member.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.workspace.databinding.WorkspaceTreeSelectStateItemBinding
import com.gg.gapo.feature.workspace.presentation.common.BaseBindingViewHolder
import com.gg.gapo.feature.workspace.presentation.common.BaseDiffAdapter
import com.gg.gapo.feature.workspace.presentation.model.SelectItemViewData

/**
 * <AUTHOR>
 * @since 9/8/22
 */
internal class SelectTreeAdapter constructor(
    private val context: Context,
    private val valueSelected: String?,
    private val selectItemListener: (item: SelectItemViewData) -> Unit
) :
    BaseDiffAdapter<WorkspaceTreeSelectStateItemBinding, SelectItemViewData>(
        callBack = SelectItemViewData.WorkspaceSelectedDiffUtil
    ) {

    override fun createViewBinding(
        parent: ViewGroup,
        viewType: Int?
    ): WorkspaceTreeSelectStateItemBinding {
        return WorkspaceTreeSelectStateItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun getItemCount(): Int {
        return currentList.size
    }

    override fun bind(
        holder: BaseBindingViewHolder<WorkspaceTreeSelectStateItemBinding>,
        viewBinding: WorkspaceTreeSelectStateItemBinding,
        viewType: Int,
        position: Int,
        item: SelectItemViewData
    ) {
        if (!item.isTextInt) {
            viewBinding.textName.text = item.text
        } else {
            item.textInt?.run {
                viewBinding.textName.text = context.getString(this)
            }
        }

        viewBinding.imageChecked.isChecked = item.value == valueSelected

        viewBinding.root.setDebouncedClickListener {
            selectItemListener.invoke(item)
        }
        viewBinding.imageChecked.setDebouncedClickListener {
            selectItemListener.invoke(item)
        }
    }
}
