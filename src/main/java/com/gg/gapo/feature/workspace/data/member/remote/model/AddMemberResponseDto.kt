package com.gg.gapo.feature.workspace.data.member.remote.model

import com.gg.gapo.feature.workspace.domain.member.model.*
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 */

internal data class AddMemberTotalResponseDto(
    @SerializedName("user") val user: AddMemberResponseDto?,
    @SerializedName("invitation") val invitation: InvitationDataResponseDto?
) {
    fun mapToDomain(): MemberTotalModel {
        return MemberTotalModel(
            if (invitation?.id == null) null else InvitationModel(invitation.id),
            user?.mapToDomain()
        )
    }
}

internal data class InvitationDataResponseDto(
    @SerializedName("id") val id: String?
)

internal data class AddMemberResponseDto(
    @SerializedName("id") val id: String?,
    @SerializedName("display_name") val displayName: String?,
    @SerializedName("email") val email: String?,
    @SerializedName("phone_number") val phoneNumber: String?,
    @SerializedName("birth") val birth: BirthDto?,
    @SerializedName("gender") val gender: MemberGender?,
    @SerializedName("avatar_thumb_pattern") val avatarThumb: String?,
    @SerializedName("status") val status: MemberStatus?,
    @SerializedName("state") val state: MemberState?,
    @SerializedName("company") val company: String?,
    @SerializedName("department_id") val departmentId: String?,
    @SerializedName("department") val department: String?,
    @SerializedName("role_id") val titleId: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("login_type") val loginType: LoginType?,
    @SerializedName("company_name") val companyName: String?,
    @SerializedName("identifier_code") val identifierCode: String?,
    @SerializedName("employee_code") val employeeCode: String?
) {

    fun mapToDomain(): MemberModel? {
        if (id == null || displayName == null) return null
        return MemberModel(
            id = id.toString(),
            displayName = displayName,
            email = email.orEmpty(),
            phoneNumber = phoneNumber.orEmpty(),
            birth = birth?.mapToDomain() ?: BirthModel.DEFAULT,
            gender = gender ?: MemberGender.NA,
            avatar = "",
            avatarThumb = avatarThumb.orEmpty(),
            role = MemberRole.MEMBER,
            status = status ?: MemberStatus.ACTIVE,
            state = state ?: MemberState.ACTIVE,
            company = company.orEmpty(),
            loginType = loginType ?: LoginType.EMAIL,
            companyName = companyName.orEmpty(),
            identifierCode = identifierCode.orEmpty(),
            listDepartment = emptyList(),
            customInfoList = emptyList(),
            employeeCode = employeeCode.orEmpty(),
            lastBan = 0L
        )
    }

    fun List<AddMemberResponseDto>.mapToDomain(): List<MemberModel> {
        return mapNotNull { it.mapToDomain() }
    }
}
