package com.gg.gapo.feature.workspace.presentation.invitation.adapter

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.core.ui.GapoAttributes
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.glide.GlideRequests
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.workspace.databinding.ItemAddWorkspaceInvitationBinding
import com.gg.gapo.feature.workspace.databinding.WorkspaceInvitationItemBinding
import com.gg.gapo.feature.workspace.presentation.model.WorkspaceInvitationViewData
import com.gg.gapo.feature.workspace.presentation.model.WorkspaceStateViewEnum

/**
 * <AUTHOR>
 * @since 05/01/2022
 */
internal class WorkspaceInvitationAdapter(
    private val context: Context,
    private val interaction: Interaction,
    private val glideRequest: GlideRequests
) : ListAdapter<WorkspaceInvitationViewData, RecyclerView.ViewHolder>(WorkspaceInvitationViewData.DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val holder = if (viewType == TYPE_DEFAULT) {
            WorkspaceInvitationHolder(
                context,
                WorkspaceInvitationItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                glideRequest,
                interaction
            )
        } else {
            WorkspaceInvitationAddHolder(
                ItemAddWorkspaceInvitationBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                interaction
            )
        }

        return holder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is WorkspaceInvitationHolder -> {
                holder.bind(getItem(position), null)
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        when (holder) {
            is WorkspaceInvitationHolder -> {
                val bundle = payloads.firstOrNull() as? Bundle
                if (bundle == null) {
                    super.onBindViewHolder(holder, position, payloads)
                } else {
                    val item = getItem(position)
                    holder.bind(item, bundle)
                }
            }

            is WorkspaceInvitationAddHolder -> {
                holder.bind()
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (currentList[position].type == WorkspaceInvitationViewData.WorkspaceType.DEFAULT_ITEM) {
            TYPE_DEFAULT
        } else TYPE_ADD
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)
        when (holder) {
            is WorkspaceInvitationHolder -> {
                holder.onViewRecycle()
            }
        }
    }

    private class WorkspaceInvitationHolder(
        private val context: Context,
        private val binding: WorkspaceInvitationItemBinding,
        private val glideRequest: GlideRequests,
        private val interaction: Interaction
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bindAvatar(avatar: String) = with(binding) {
            glideRequest.load(avatar)
                .fallback(GapoDrawables.shared_ic_workspace_logo_default)
                .placeholder(GapoDrawables.shared_ic_workspace_logo_default)
                .error(GapoDrawables.shared_ic_workspace_logo_default)
                .into(imageWorkspace)
        }

        fun bindStateAndRole(
            state: WorkspaceStateViewEnum,
            roleName: String
        ) =
            with(binding) {
                when (state) {
                    WorkspaceStateViewEnum.DISABLED -> {
                        textWorkspaceRole.text =
                            context.getString(GapoStrings.workspace_invitation_organization_has_blocked)
                        textWorkspaceRole.setTextColor(
                            ContextCompat.getColor(
                                context,
                                GapoColors.negativePrimary
                            )
                        )
                    }

                    WorkspaceStateViewEnum.PENDING -> {
                        textWorkspaceRole.text =
                            context.getString(GapoStrings.workspace_invitation_to_join)
                        textWorkspaceRole.setTextColor(
                            GapoGlobalResources.getColor(
                                context,
                                GapoAttributes.accentWorkSecondary
                            )
                        )
                    }

                    WorkspaceStateViewEnum.REQUESTING -> {
                        textWorkspaceRole.text =
                            context.getString(GapoStrings.workspace_pending_approval)
                        textWorkspaceRole.setTextColor(
                            ContextCompat.getColor(
                                context,
                                GapoColors.criticalPrimary
                            )
                        )
                    }

                    WorkspaceStateViewEnum.REJECTED -> {
                        textWorkspaceRole.text =
                            context.getString(GapoStrings.workspace_admin_rejected)
                        textWorkspaceRole.setTextColor(
                            ContextCompat.getColor(
                                context,
                                GapoColors.negativePrimary
                            )
                        )
                    }

                    WorkspaceStateViewEnum.IS_ADMIN -> {
                        textWorkspaceRole.text =
                            roleName.ifBlank { context.getString(GapoStrings.shared_admin) }
                        textWorkspaceRole.setTextColor(
                            ContextCompat.getColor(
                                context,
                                GapoColors.contentSecondary
                            )
                        )
                    }

                    else -> {
                        textWorkspaceRole.text =
                            roleName.ifBlank { context.getString(GapoStrings.shared_member) }
                        textWorkspaceRole.setTextColor(
                            ContextCompat.getColor(
                                context,
                                GapoColors.contentSecondary
                            )
                        )
                    }
                }
            }

        private fun bindName(name: String) = with(binding) {
            textWorkspaceName.text = name
        }

        fun bind(item: WorkspaceInvitationViewData, bundle: Bundle?) = with(binding) {
            itemView.setDebouncedClickListener {
                interaction.onItemSelected(absoluteAdapterPosition, item)
            }

            textRemove.setDebouncedClickListener {
                interaction.onWorkspaceDeleted(item)
            }

            if (bundle == null) {
                bindAvatar(item.workspaceAvatar)
                bindStateAndRole(item.workspaceState, item.roleName)
                bindName(item.workspaceName)
            } else {
                if (bundle.containsKey(WorkspaceInvitationViewData.WORKSPACE_AVATAR_CHANGED_EXTRA)) {
                    bindAvatar(item.workspaceAvatar)
                }
                if (bundle.containsKey(WorkspaceInvitationViewData.WORKSPACE_NAME_CHANGED_EXTRA)) {
                    bindName(item.workspaceName)
                }
                if (bundle.containsKey(WorkspaceInvitationViewData.WORKSPACE_STATE_CHANGED_EXTRA)) {
                    bindStateAndRole(item.workspaceState, item.roleName)
                }
            }
        }

        fun onViewRecycle() {
            glideRequest.clear(binding.imageWorkspace)
        }
    }

    private class WorkspaceInvitationAddHolder(
        private val binding: ItemAddWorkspaceInvitationBinding,
        private val interaction: Interaction
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.root.setDebouncedClickListener {
                interaction.onWorkspaceCreate()
            }
        }
    }

    interface Interaction {
        fun onItemSelected(position: Int, item: WorkspaceInvitationViewData)

        fun onWorkspaceDeleted(item: WorkspaceInvitationViewData)

        fun onWorkspaceCreate()
    }

    companion object {
        const val TYPE_DEFAULT = 0
        const val TYPE_ADD = 1
    }
}
