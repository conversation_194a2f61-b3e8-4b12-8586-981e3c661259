package com.gg.gapo.feature.invitation.common

import android.view.View
import androidx.annotation.CallSuper
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.airbnb.epoxy.EpoxyHolder

internal class DataBindingEpoxyHolder : EpoxyHolder() {

    var binding: ViewDataBinding? = null

    @CallSuper
    override fun bindView(itemView: View) {
        binding = DataBindingUtil.bind(itemView)
    }
}
