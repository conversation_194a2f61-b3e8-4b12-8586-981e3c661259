package com.gg.gapo.feature.user.timeline.domain.follow.usecase

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.user.timeline.data.follow.exception.UserFollowExceptionHandler
import com.gg.gapo.feature.user.timeline.domain.follow.UserFollowRepository

/**
 * <AUTHOR>
 * @since 15/05/2024
 */
internal class FetchFollowingCountUseCase(
    private val repository: UserFollowRepository,
    private val exceptionHandler: UserFollowExceptionHandler
) {
    suspend operator fun invoke(userId: String): Result<Int> {
        return try {
            Result.Success(repository.fetchFollowingCount(userId))
        } catch (e: Exception) {
            Result.Error(exceptionHandler(e))
        }
    }
}
