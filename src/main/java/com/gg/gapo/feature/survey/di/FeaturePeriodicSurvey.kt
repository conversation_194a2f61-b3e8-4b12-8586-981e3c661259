package com.gg.gapo.feature.survey.di

import com.gg.gapo.core.utilities.di.*
import com.gg.gapo.core.utilities.retrofit.RetrofitFactory
import com.gg.gapo.feature.survey.data.SurveyRepositoryImpl
import com.gg.gapo.feature.survey.data.remote.SurveyRemote
import com.gg.gapo.feature.survey.data.remote.SurveyRemoteImpl
import com.gg.gapo.feature.survey.data.remote.api.MessengerService
import com.gg.gapo.feature.survey.data.remote.api.SurveyService
import com.gg.gapo.feature.survey.domain.SurveyRepository
import com.gg.gapo.feature.survey.domain.usecase.*
import com.gg.gapo.feature.survey.presentation.compose.privacy.viewmodel.AddParticipantViewModel
import com.gg.gapo.feature.survey.presentation.compose.privacy.viewmodel.ComposeParticipantViewModel
import com.gg.gapo.feature.survey.presentation.compose.question.viewmodel.AddQuestionViewModel
import com.gg.gapo.feature.survey.presentation.compose.question.viewmodel.ComposeQuestionViewModel
import com.gg.gapo.feature.survey.presentation.compose.setting.viewmodel.ComposeSettingViewModel
import com.gg.gapo.feature.survey.presentation.list.sheet.info.SurveyInfoViewModel
import com.gg.gapo.feature.survey.presentation.list.sheet.periods.SurveyPeriodsViewModel
import com.gg.gapo.feature.survey.presentation.list.viewmodel.*
import com.gg.gapo.feature.survey.presentation.worker.SurveyAttachmentDownloadWorker
import com.gg.gapo.feature.survey.presentation.worker.SurveyAttachmentUploadWorker
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.androidx.workmanager.dsl.worker
import org.koin.dsl.module

val featureSurvey = module {

    factory {
        RetrofitFactory.create<SurveyService>(apiBaseUrl, commonOkHttp)
    }

    factory {
        RetrofitFactory.create<MessengerService>(messengerApiBaseUrl, commonOkHttp)
    }

    viewModel { AddParticipantViewModel(get(), get()) }
    viewModel { ComposeSettingViewModel() }
    viewModel {
        SurveyListViewModel(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    viewModel {
        QuestionSurveyDetailViewModel(
            get()
        )
    }
    viewModel { RecipientsViewModel(get(), get(), get(), get()) }
    viewModel { SurveyInfoViewModel(get(), get()) }
    viewModel { SurveyPeriodsViewModel(get()) }
    viewModel { SurveyItemActionViewModel(get(), get(), get(), get()) }
    viewModel { AnswerSurveyViewModel(get(), get(), get(), get()) }
    viewModel { FilterAnswererViewModel(get(), get()) }
    viewModel { AddQuestionViewModel() }
    viewModel { ComposeQuestionViewModel(get(), get(), get()) }
    viewModel { ComposeParticipantViewModel(get(), get(), get(), get(), get(), get(), get(), get()) }
    factory<SurveyRemote> { SurveyRemoteImpl(get(), get()) }
    factory<SurveyRepository> { SurveyRepositoryImpl(get(), get()) }
    factory { CreatePeriodicSurveyUseCase(get()) }
    factory { GetCreatedSurveysUseCase(get()) }
    factory { GetRequiredSurveysUseCase(get()) }
    factory { UpdatePeriodicSurveyUseCase(get()) }
    factory { DeletePeriodicSurveyUseCase(get()) }
    factory { RemindUnAnsweredUseCase(get()) }
    factory { AnswerSurveyUseCase(get()) }
    factory { GetQuestionContainAnswersUseCase(get()) }
    factory { GetQuestionContainAnswersForCreatorUseCase(get()) }
    factory { GetAnswersOfQuestionUseCase(get()) }
    factory { GetDetailSurveyUseCase(get()) }
    factory { GetDraftSurveyByIdUseCase(get()) }
    factory { GetDetailCreatedSurveyUseCase(get()) }
    factory { GetListAnswererUseCase(get()) }
    factory { GetSurveyPeriodsUseCase(get()) }
    factory { SearchUserInviteWorkSpaceUseCase(get()) }
    factory { CreateDraftSurveyUseCase(get()) }
    factory { UpdateDraftSurveyUseCase(get()) }
    factory { GetDraftSurveysUseCase(get()) }
    factory { DeleteDraftSurveyUseCase(get()) }
    factory { GetUserByIdsUseCase(get()) }
    factory { GetThreadByIdsUseCase(get()) }
    factory { GetRoleByIdsUseCase(get()) }
    factory { GetDepartmentByIdsUseCase(get()) }
    factory { GetRandomBannerUseCase(get()) }

    worker { params ->
        SurveyAttachmentDownloadWorker(
            androidContext(),
            params.get(),
            downloadOkHttp,
            noAuthenticatorDownloadOkHttp,
            get()
        )
    }

    worker { params ->
        SurveyAttachmentUploadWorker(
            androidContext(),
            params.get(),
            uploadApiBaseUrl,
            uploadOkHttp,
            get()
        )
    }
}
