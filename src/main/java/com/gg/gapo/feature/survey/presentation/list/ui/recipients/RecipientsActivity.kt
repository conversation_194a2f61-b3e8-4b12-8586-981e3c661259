package com.gg.gapo.feature.survey.presentation.list.ui.recipients

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import com.gg.gapo.core.ui.GapoPlurals
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.ui.snackbar.makePositiveSnackbar
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.survey.R
import com.gg.gapo.feature.survey.databinding.RecipentsActivityBinding
import com.gg.gapo.feature.survey.domain.model.PrivacyType
import com.gg.gapo.feature.survey.domain.model.SurveyStatus
import com.gg.gapo.feature.survey.presentation.list.viewmodel.RecipientsViewModel
import com.google.android.material.tabs.TabLayoutMediator
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 20/10/2021
 */
internal class RecipientsActivity : GapoThemeBaseActivity() {

    private lateinit var binding: RecipentsActivityBinding
    private val recipientsViewModel by viewModel<RecipientsViewModel>()
    private var surveyId = ""
    private var surveyStatus = SurveyStatus.RUNNING.title

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.recipents_activity)
        surveyId = intent?.getStringExtra(SURVEY_ID_ARG) ?: return
        surveyStatus = intent?.getStringExtra(SURVEY_STATUS_ARG) ?: return
        val eventTime = intent?.getStringExtra(EVENT_TIME_ARG) ?: return
        val surveyType = intent?.getIntExtra(SURVEY_TYPE_ARG, PrivacyType.PRIVATE.value) ?: return
        val incognitoMode = intent?.getBooleanExtra(INCOGNITO_MODE_ARG, false) ?: false
        recipientsViewModel.fetchSurveyInfoForAdmin(surveyId, eventTime)
        initViews(surveyType, incognitoMode)

        recipientsViewModel.snackBarErrorMessage.observe(
            this,
            EventObserver {
                makeNegativeSnackbar(it)?.show()
            }
        )
        recipientsViewModel.snackBarRemindSuccessLiveData.observe(
            this,
            EventObserver {
                if (it) {
                    makePositiveSnackbar(GapoStrings.survey_v2_send_reminder_success)?.showOnTop()
                } else {
                    makeNegativeSnackbar(GapoStrings.survey_v2_send_reminder_failed)?.showOnTop()
                }
            }
        )
    }

    private fun initViews(surveyType: Int, incognitoMode: Boolean) {
        binding.btnBack.setDebouncedClickListener {
            finish()
        }
        recipientsViewModel.surveyModelLiveData.observe(this) {
            val recipientsPagerAdapter = RecipientsPagerAdapter(
                this,
                surveyId,
                surveyStatus,
                surveyType,
                incognitoMode,
                (it.to?.size ?: 0) > 0
            )
            val countAnswer = it.answerIdsAfterQuesChanged?.size ?: 0
            val toUserIds = it.toUserIds.size
            binding.viewPager.adapter = recipientsPagerAdapter
            val tabsTitle = arrayOf(
                getString(GapoStrings.shared_all) + " ($toUserIds)",
                getString(GapoStrings.periodic_survey_answered) + " ($countAnswer)",
                getString(GapoStrings.periodic_survey_not_answer_yet) + " (${toUserIds - countAnswer})"
            )
            TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
                tab.text = tabsTitle[position]
            }.attach()

            if (surveyType == PrivacyType.PUBLIC.value) {
                if (incognitoMode) {
                    if (it.toUser.isEmpty()) {
                        binding.viewPager.visibility = View.GONE
                        binding.tabLayout.visibility = View.GONE
                        binding.lineHeader.visibility = View.GONE
                        if (countAnswer == 0) {
                            binding.tvCount.text =
                                resources.getString(GapoStrings.survey_v2_empty_response)
                        } else {
                            binding.tvCount.text = resources.getQuantityString(
                                GapoPlurals.survey_v2_plurals_people_answered,
                                countAnswer,
                                countAnswer
                            )
                        }
                        binding.title.text = getString(GapoStrings.survey_v2_participants)
                    }
                } else {
                    if (it.to.isNullOrEmpty()) {
                        binding.tabLayout.visibility = View.GONE
                        binding.title.text =
                            getString(GapoStrings.periodic_survey_answer_people_public)
                    }
                }
            }
        }
    }

    companion object {
        private const val SURVEY_ID_ARG = "SURVEY_ID_ARG"
        private const val SURVEY_STATUS_ARG = "SURVEY_STATUS_ARG"
        private const val SURVEY_TYPE_ARG = "SURVEY_TYPE_ARG"
        private const val EVENT_TIME_ARG = "EVENT_TIME_ARG"
        private const val INCOGNITO_MODE_ARG = "INCOGNITO_MODE_ARG"

        fun getIntent(
            context: Context,
            surveyId: String,
            surveyStatus: String,
            eventTime: String,
            surveyType: Int,
            incognitoMode: Boolean
        ) =
            Intent(context, RecipientsActivity::class.java).apply {
                putExtra(SURVEY_ID_ARG, surveyId)
                putExtra(SURVEY_STATUS_ARG, surveyStatus)
                putExtra(SURVEY_TYPE_ARG, surveyType)
                putExtra(EVENT_TIME_ARG, eventTime)
                putExtra(INCOGNITO_MODE_ARG, incognitoMode)
            }
    }
}
