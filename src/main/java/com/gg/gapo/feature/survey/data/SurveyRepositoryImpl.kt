package com.gg.gapo.feature.survey.data

import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.survey.data.remote.SurveyRemote
import com.gg.gapo.feature.survey.data.remote.model.SubmitSurveyRequest
import com.gg.gapo.feature.survey.data.remote.model.SurveyDto
import com.gg.gapo.feature.survey.data.remote.model.mapToDomain
import com.gg.gapo.feature.survey.data.remote.response.BaseDataResponse
import com.gg.gapo.feature.survey.data.remote.response.BaseResponse
import com.gg.gapo.feature.survey.data.remote.response.onSuccess
import com.gg.gapo.feature.survey.domain.SurveyRepository
import com.gg.gapo.feature.survey.domain.model.*
import com.gg.gapo.feature.survey.domain.model.organization.DepartmentModel
import com.gg.gapo.feature.survey.domain.model.organization.RoleModel
import com.gg.gapo.feature.survey.domain.model.organization.ThreadModel
import com.google.gson.JsonParser
import kotlinx.coroutines.withContext
import retrofit2.HttpException

internal class SurveyRepositoryImpl(
    private val surveyRemote: SurveyRemote,
    private val appDispatchers: CoroutineDispatchers
) : SurveyRepository {

    override suspend fun createSurvey(data: SurveyDto): Result<BaseResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.createSurvey(data)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                getError(e)?.second ?: Result.Error(e)
            }
        }
    }

    override suspend fun updateSurvey(
        id: String,
        data: SurveyDto
    ): Result<BaseResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.updateSurvey(id, data)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                getError(e)?.second ?: Result.Error(e)
            }
        }
    }

    override suspend fun getCreatedSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getCreatedSurveys(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun createDraftSurvey(data: SurveyDto): Result<BaseResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.createDraftSurvey(data)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                getError(e)?.second ?: Result.Error(e)
            }
        }
    }

    override suspend fun updateDraftSurvey(
        id: String,
        data: SurveyDto
    ): Result<BaseResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.updateDraftSurvey(id, data)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                getError(e)?.second ?: Result.Error(e)
            }
        }
    }

    override suspend fun getDraftSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getDraftSurveys(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getRequiredSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getRequiredSurveys(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun deleteSurvey(id: String): Result<String> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.deleteSurvey(id)
                Result.Success(
                    response.message.orEmpty()
                )
            } catch (e: Exception) {
                getErrorString(e) ?: Result.Error(e)
            }
        }
    }

    override suspend fun deleteDraftSurvey(id: String): Result<String> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.deleteDraftSurvey(id)
                Result.Success(
                    response.message.orEmpty()
                )
            } catch (e: Exception) {
                getErrorString(e) ?: Result.Error(e)
            }
        }
    }

    override suspend fun remindUnAnswered(id: String): Result<Unit> {
        return withContext(appDispatchers.io) {
            try {
                surveyRemote.remindUnAnswered(id)
                Result.Success(Unit)
            } catch (e: Exception) {
                getErrorString(e) ?: Result.Error(e)
            }
        }
    }

    override suspend fun answerSurvey(data: SubmitSurveyRequest): Result<String> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.answerSurvey(data)
                Result.Success(
                    response.message.orEmpty()
                )
            } catch (e: Exception) {
                getErrorString(e) ?: Result.Error(e)
            }
        }
    }

    override suspend fun getDetailCreatedSurvey(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<BaseDataResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getDetailSurvey(surveyId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseDataResponse(
                            code = "200",
                            data = response.data!!.mapToDomainModel(),
                            links = response.links,
                            message = ""
                        )
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getQuestionContainsAnswers(
        questionId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<QuestionContainAnswerModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getQuestionContainsAnswers(questionId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getQuestionContainsAnswersForCreator(
        questionId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<QuestionContainAnswerModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response =
                    surveyRemote.getQuestionContainsAnswersForCreator(questionId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getAnswersOfQuestion(
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<AnswerModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getAnswersOfQuestion(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getDetailSurvey(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Pair<Int, Result<BaseResponse<SurveyModel>>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getDetailSurveyForUser(surveyId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Pair(
                        200,
                        Result.Success(
                            BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                        )
                    )
                } else {
                    Pair(
                        -1,
                        Result.Error(Exception(response.message.orEmpty()))
                    )
                }
            } catch (e: Exception) {
                getError(e) ?: Pair(-1, Result.Error(e))
            }
        }
    }

    override suspend fun getDraftSurveyById(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<BaseResponse<SurveyModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getDraftSurveyById(surveyId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                getError(e)?.second ?: Result.Error(e)
            }
        }
    }

    override suspend fun getListAnswerer(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<UserProfileDataModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getListAnswerer(surveyId, queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.mapNotNull {
                                it.mapToDomainModel()
                            }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun getSurveyPeriods(
        surveyId: String,
        isCreator: Boolean,
        queryMap: Map<String, String>
    ): Pair<List<SurveyPeriodModel>, String> = withContext(appDispatchers.io) {
        val response = surveyRemote.getSurveyPeriods(surveyId, isCreator, queryMap)
        response.data?.mapToDomain().orEmpty() to response.links?.next.orEmpty()
    }

    override suspend fun getRandomBanner(): Result<BaseResponse<WrapBannerModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.getRandomBanner()
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        BaseResponse(response.data!!.mapToDomainModel(), 200, "")
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun searchUserInviteWorkSpace(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<UserProfileDataModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.searchUserInviteWorkSpace(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.mapNotNull { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun fetchUsersWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<UserProfileDataModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.fetchUsersWithIds(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.mapNotNull { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun fetchThreadsWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<ThreadModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.fetchThreadsWithIds(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun fetchRolesWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<RoleModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.fetchRolesWithIds(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    override suspend fun fetchDepartmentsWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<DepartmentModel>> {
        return withContext(appDispatchers.io) {
            try {
                val response = surveyRemote.fetchDepartmentsWithIds(queryMap)
                if (response.onSuccess() || response.data != null) {
                    Result.Success(
                        response.mapToDomainModel {
                            response.data?.map { it.mapToDomainModel() }.orEmpty()
                        }
                    )
                } else {
                    Result.Error(Exception(response.message.orEmpty()))
                }
            } catch (e: Exception) {
                Result.Error(e)
            }
        }
    }

    private fun getError(e: Exception): Pair<Int, Result<BaseResponse<SurveyModel>>>? {
        return try {
            if (e is HttpException && e.response()?.code() == 400 && e.response()
                ?.errorBody() != null
            ) {
                val temp = JsonParser().parse(e.response()?.errorBody()?.string())
                Pair(400, Result.Error(Exception(temp.asJsonObject.get("message").asString)))
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun getErrorString(e: Exception): Result.Error? {
        return try {
            if (e is HttpException && e.response()?.code() == 400 && e.response()
                ?.errorBody() != null
            ) {
                val temp = JsonParser().parse(e.response()?.errorBody()?.string())
                Result.Error(Exception(temp.asJsonObject.get("message").asString))
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
}
