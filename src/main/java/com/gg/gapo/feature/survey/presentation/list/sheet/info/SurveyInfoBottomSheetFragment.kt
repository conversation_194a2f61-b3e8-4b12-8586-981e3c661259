package com.gg.gapo.feature.survey.presentation.list.sheet.info

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoDrawables
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbar
import com.gg.gapo.core.ui.snackbar.showOnTop
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.survey.common.Utils
import com.gg.gapo.feature.survey.common.copyToClipboard
import com.gg.gapo.feature.survey.common.copyToClipboardWithoutToast
import com.gg.gapo.feature.survey.databinding.SurveyInfoBottomSheetFragmentBinding
import com.gg.gapo.feature.survey.domain.model.PrivacyType
import com.gg.gapo.feature.survey.domain.model.SurveyType
import com.gg.gapo.feature.survey.presentation.list.listener.SpinnerPeriodListener
import com.gg.gapo.feature.survey.presentation.list.sheet.periods.SurveyPeriodsBottomSheetFragment
import com.gg.gapo.feature.survey.presentation.list.ui.recipients.RecipientsActivity
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 19/10/2021
 */
internal class SurveyInfoBottomSheetFragment :
    GapoBottomSheetFragment(),
    SpinnerPeriodListener {

    private var binding by autoCleared<SurveyInfoBottomSheetFragmentBinding>()
    private val surveyInfoViewModel by viewModel<SurveyInfoViewModel>()
    private var isFromCreator: Boolean = false
    private var surveyId: String? = null
    private val requestManager by lazy { GapoGlide.with(this) }
    private var listener: Listener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = parentFragment as? Listener ?: activity as? Listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val surveyId = arguments?.getString(SURVEY_ID_ARG).also { this.surveyId = it }
        isFromCreator = arguments?.getBoolean(IS_FROM_CREATOR_ARG) ?: false
        if (surveyId == null) {
            dismissAllowingStateLoss()
            return
        }
        surveyInfoViewModel.fetchSurveyInfo(surveyId, isFromCreator)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = SurveyInfoBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.surveyInfoViewModel = surveyInfoViewModel
        initViews()
        return binding.root
    }

    private fun initViews() {
        surveyInfoViewModel.surveyLiveData.observe(viewLifecycleOwner) { surveyModel ->
            binding.tvTitle.text = surveyModel.title
            binding.tvBody.text = surveyModel.des
            binding.tvBody.setOnLongClickListener {
                requireContext().copyToClipboard(surveyModel.des)
                return@setOnLongClickListener true
            }
            if (surveyModel.des.isEmpty()) {
                binding.tvBody.visibility = View.GONE
            }
            if (isFromCreator && surveyModel.type == PrivacyType.PUBLIC.value) {
                binding.btnCopy.visibility = View.VISIBLE
            }
            binding.tvDueDate.text = if (surveyModel.eventTime == 0L) {
                requireContext().getString(GapoStrings.survey_v2_no_expiration_date)
            } else {
                Utils.formatTimeAndDate(surveyModel.eventTime)
            }

            when (surveyModel.schedule.type) {
                SurveyType.ONCE.type -> {
                    binding.tvSurveyHistory.isVisible = false
                }
                else -> {
                    binding.tvSurveyHistory.isVisible = true
                }
            }
            if (isFromCreator) {
                binding.listParticipants.setData(requestManager, surveyModel.toUser)
                binding.tvAnswerPeople.text = getString(
                    GapoStrings.periodic_survey_answer_people,
                    surveyModel.toUser.size.toString()
                )
                binding.tvCreatorName.isVisible = false
                binding.tvAnswerPeople.isVisible = true
            } else {
                binding.listParticipants.setData(requestManager, listOf(surveyModel.creator))
                binding.tvCreatorName.text = surveyModel.creator.displayName
                binding.tvCreatorName.isVisible = true
                binding.tvAnswerPeople.isVisible = false
            }

            if (surveyModel.incognitoMode) {
                binding.lnAnonymous.visibility = View.VISIBLE
            } else {
                binding.lnAnonymous.visibility = View.GONE
            }

            binding.tvAnswerPeople.setDebouncedClickListener {
                val recipientsActivity = surveyId?.let { surveyId ->
                    RecipientsActivity.getIntent(
                        requireContext(),
                        surveyId,
                        surveyModel.statusTitle,
                        arguments?.getString(EVENT_TIME_ARG).orEmpty(),
                        surveyModel.type,
                        surveyModel.incognitoMode
                    )
                }

                recipientsActivity?.let {
                    startActivity(it)
                }

//                RecipientsBottomSheetFragment.getInstance(
//                    surveyId.orEmpty(),
//                    arguments?.getString(EVENT_TIME_ARG).orEmpty(),
//                    surveyModel.type,
//                    surveyModel.incognitoMode
//                ).show(childFragmentManager, null)
            }
        }

        binding.btnCopy.setDebouncedClickListener {
            val checkmarkCircleDrawable = ContextCompat.getDrawable(
                requireContext(),
                GapoDrawables.ic24_fill_checkmark_circle
            )
            checkmarkCircleDrawable?.setTint(
                ContextCompat.getColor(
                    requireContext(),
                    GapoColors.accentPrimary
                )
            )
            requireActivity().makeNormalSnackbar(
                GapoStrings.shared_copied_link,
                leftDrawable = checkmarkCircleDrawable
            )?.showOnTop()
            surveyId?.let {
                requireContext().copyToClipboardWithoutToast(Utils.genPublicLink(it))
            }
            dismissAllowingStateLoss()
        }

        binding.tvSurveyHistory.setDebouncedClickListener {
            surveyId?.let { surveyId ->
                SurveyPeriodsBottomSheetFragment.newInstance(surveyId, isFromCreator)
                    .show(childFragmentManager, null)
            }
        }

        surveyInfoViewModel.snackBarErrorMessage.observe(
            this,
            EventObserver {
                GapoToast.makeNegative(requireContext(), it, Toast.LENGTH_LONG).show()
            }
        )
    }

    companion object {
        private const val SURVEY_ID_ARG = "SURVEY_ID_ARG"
        private const val EVENT_TIME_ARG = "EVENT_TIME_ARG"
        private const val IS_FROM_CREATOR_ARG = "IS_FROM_CREATOR_ARG"

        fun newInstance(surveyId: String, eventTime: String = "", isFromCreator: Boolean) =
            SurveyInfoBottomSheetFragment().apply {
                arguments = bundleOf(
                    SURVEY_ID_ARG to surveyId,
                    EVENT_TIME_ARG to eventTime,
                    IS_FROM_CREATOR_ARG to isFromCreator
                )
            }
    }

    override fun onPeriodSelected(eventTime: Long, submitted: Boolean, questionVersion: String) {
        listener?.onPeriodSelectedFromInfoBottomSheet(eventTime, submitted, questionVersion)
        dismissAllowingStateLoss()
    }

    interface Listener {
        fun onPeriodSelectedFromInfoBottomSheet(eventTime: Long, submitted: Boolean, questionVersion: String)
    }
}
