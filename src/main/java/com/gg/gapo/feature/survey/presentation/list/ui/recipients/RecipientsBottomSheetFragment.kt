package com.gg.gapo.feature.survey.presentation.list.ui.recipients

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import com.gg.gapo.core.ui.GapoPlurals
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.bottomsheet.GapoExpandedBottomSheetFragment
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.feature.survey.databinding.RecipentsBottomSheetFragmentBinding
import com.gg.gapo.feature.survey.domain.model.PrivacyType
import com.gg.gapo.feature.survey.presentation.list.viewmodel.RecipientsViewModel
import com.google.android.material.tabs.TabLayoutMediator
import org.koin.androidx.viewmodel.ext.android.viewModel

internal class RecipientsBottomSheetFragment : GapoExpandedBottomSheetFragment() {

    private lateinit var binding: RecipentsBottomSheetFragmentBinding
    private val recipientsViewModel by viewModel<RecipientsViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = RecipentsBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        val surveyId = arguments?.getString(SURVEY_ID_ARG) ?: ""
        val surveyStatus = arguments?.getString(SURVEY_STATUS_ARG) ?: ""
        val eventTime = arguments?.getString(EVENT_TIME_ARG) ?: ""
        val surveyType = arguments?.getInt(SURVEY_TYPE_ARG, PrivacyType.PRIVATE.value)
            ?: PrivacyType.PRIVATE.value
        val incognitoMode = arguments?.getBoolean(INCOGNITO_MODE_ARG, false) ?: false
        recipientsViewModel.fetchSurveyInfoForAdmin(surveyId, eventTime)
        initViews(surveyType, surveyId, surveyStatus, incognitoMode)
        recipientsViewModel.snackBarErrorMessage.observe(
            this,
            EventObserver {
                makeNegativeSnackbar(it)?.show()
            }
        )
        initViews(surveyType, surveyId, surveyStatus, incognitoMode)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    private fun initViews(
        surveyType: Int,
        surveyId: String,
        surveyStatus: String,
        incognitoMode: Boolean
    ) {
        val recipientsPagerAdapter =
            RecipientsPagerAdapter(
                requireActivity(),
                surveyId,
                surveyStatus,
                surveyType,
                incognitoMode,
                false
            )
        binding.viewPager.adapter = recipientsPagerAdapter
        val tabsTitle = arrayOf(
            getString(GapoStrings.shared_all),
            getString(GapoStrings.periodic_survey_answered),
            getString(GapoStrings.periodic_survey_not_answer_yet)
        )
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = tabsTitle[position]
        }.attach()

        if (surveyType == PrivacyType.PUBLIC.value) {
            if (incognitoMode) {
                recipientsViewModel.surveyModelLiveData.observe(this) {
                    if (it.incognitoMode && it.to.isNullOrEmpty()) {
                        binding.viewPager.visibility = View.GONE
                        binding.tabLayout.visibility = View.GONE
                        binding.lineHeader.visibility = View.GONE
                        val countAnswer = it.answerIds?.size ?: 0
                        if (countAnswer == 0) {
                            binding.tvCount.text =
                                resources.getString(GapoStrings.survey_v2_empty_response)
                        } else {
                            binding.tvCount.text = resources.getQuantityString(
                                GapoPlurals.survey_v2_plurals_people_answered,
                                countAnswer,
                                countAnswer
                            )
                        }
                    } else {
                        binding.lnIncognito.visibility = View.GONE
                        binding.viewPager.visibility = View.VISIBLE
                        binding.tabLayout.visibility = View.VISIBLE
                    }
                }
            } else {
                binding.tabLayout.visibility = View.GONE
                binding.title.text = getString(GapoStrings.periodic_survey_answer_people_public)
            }
        }
    }

    companion object {
        private const val SURVEY_ID_ARG = "SURVEY_ID_ARG"
        private const val SURVEY_STATUS_ARG = "SURVEY_STATUS_ARG"
        private const val SURVEY_TYPE_ARG = "SURVEY_TYPE_ARG"
        private const val EVENT_TIME_ARG = "EVENT_TIME_ARG"
        private const val INCOGNITO_MODE_ARG = "INCOGNITO_MODE_ARG"

        fun getInstance(
            surveyId: String,
            surveyStatus: String,
            eventTime: String,
            surveyType: Int,
            incognitoMode: Boolean
        ) = RecipientsBottomSheetFragment().apply {
            arguments = bundleOf(
                SURVEY_ID_ARG to surveyId,
                SURVEY_STATUS_ARG to surveyStatus,
                SURVEY_TYPE_ARG to surveyType,
                EVENT_TIME_ARG to eventTime,
                INCOGNITO_MODE_ARG to incognitoMode
            )
        }
    }
}
