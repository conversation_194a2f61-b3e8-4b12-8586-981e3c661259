package com.gg.gapo.feature.survey.presentation.list.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.survey.common.Constants.MAX_ANSWER_OF_PAGE
import com.gg.gapo.feature.survey.domain.model.SurveyModel
import com.gg.gapo.feature.survey.domain.model.UserProfileDataModel
import com.gg.gapo.feature.survey.domain.usecase.GetDetailCreatedSurveyUseCase
import com.gg.gapo.feature.survey.domain.usecase.GetUserByIdsUseCase
import com.gg.gapo.feature.survey.domain.usecase.RemindUnAnsweredUseCase
import com.gg.gapo.feature.survey.presentation.list.ui.recipients.RecipientsPagerAdapter
import kotlinx.coroutines.launch
import java.util.TreeMap

/**
 * <AUTHOR>
 * @since 20/10/2021
 */
internal class RecipientsViewModel(
    private val getDetailCreatedSurveyUseCase: GetDetailCreatedSurveyUseCase,
    private val getUserByIdsUseCase: GetUserByIdsUseCase,
    private val remindUnAnsweredUseCase: RemindUnAnsweredUseCase,
    private val appDispatchers: CoroutineDispatchers
) : ViewModel() {

    private val _allUsersLiveData = MutableLiveData<List<UserProfileDataModel>>()
    val allUsersLiveData: LiveData<List<UserProfileDataModel>>
        get() = _allUsersLiveData

    private val _answeredUsersLiveData = MutableLiveData<List<UserProfileDataModel>>()
    val answeredUsersLiveData: LiveData<List<UserProfileDataModel>>
        get() = _answeredUsersLiveData

    private val _notAnsweredUsersLiveData = MutableLiveData<List<UserProfileDataModel>>()
    val notAnsweredUsersLiveData: LiveData<List<UserProfileDataModel>>
        get() = _notAnsweredUsersLiveData

    private val _surveyModelLiveData = MutableLiveData<SurveyModel>()
    val surveyModelLiveData: LiveData<SurveyModel>
        get() = _surveyModelLiveData

    private val _snackBarErrorMessage = MutableLiveData<Event<String>>()
    val snackBarErrorMessage: LiveData<Event<String>>
        get() = _snackBarErrorMessage

    private val _snackBarRemindSuccessLiveData = MutableLiveData<Event<Boolean>>()
    val snackBarRemindSuccessLiveData: LiveData<Event<Boolean>>
        get() = _snackBarRemindSuccessLiveData

    private val allUserIds = TreeMap<String, UserProfileDataModel?>()
    private val answeredUserIds = TreeMap<String, UserProfileDataModel?>()
    private val notAnsweredUserIds = TreeMap<String, UserProfileDataModel?>()

    @Volatile var isAllUserLoading = false

    @Volatile var isAnsweredUserLoading = false

    @Volatile var isNotAnsweredUserLoading = false

    fun fetchSurveyInfoForAdmin(surveyId: String, eventTime: String) =
        viewModelScope.launch {
            val queryMap = mutableMapOf<String, String>()
            queryMap["fields"] =
                "_id,title,to_users,created_at,questions,schedule,des,event_time,status,answer_ids,answer_ids_after_ques_changed,incognito_mode,to_user_ids"
            if (eventTime.isNotEmpty()) {
                queryMap["event_time"] = eventTime
            }
            when (val result = getDetailCreatedSurveyUseCase.invoke(surveyId, queryMap)) {
                is Result.Success -> {
                    result.data.data?.let {
                        handleSurvey(it)
                    }
                }
                is Result.Error -> {
                    val message = result.exception.message.orEmpty()
                    _snackBarErrorMessage.postValue(Event(message))
                }
            }
        }

    fun remindUnAnswered(surveyId: String) =
        viewModelScope.launch {
            when (val result = remindUnAnsweredUseCase.invoke(surveyId)) {
                is Result.Success -> {
                    _snackBarRemindSuccessLiveData.postValue(Event(true))
                }
                is Result.Error -> {
                    _snackBarRemindSuccessLiveData.postValue(Event(false))
                }
            }
        }

    private fun handleSurvey(survey: SurveyModel) {
        val allUserIds = survey.toUserIds.toMutableList()
        allUserIds.forEach {
            this.allUserIds[it] = null
        }

        val answeredUserIds = survey.answerIdsAfterQuesChanged.orEmpty().toMutableList()
        answeredUserIds.forEach {
            this.answeredUserIds[it] = null
        }
        val notAnsweredUserIds =
            allUserIds.toMutableList().apply { removeAll { answeredUserIds.contains(it) } }
        notAnsweredUserIds.forEach {
            this.notAnsweredUserIds[it] = null
        }
        _surveyModelLiveData.value = survey
    }

    fun fetchParticipants(tabValue: String) {
        viewModelScope.launch(appDispatchers.io) {
            val tab = RecipientsPagerAdapter.TAB.fromString(tabValue)
            val currentParticipantIds =
                when (tab) {
                    RecipientsPagerAdapter.TAB.TAB_ALL -> allUserIds.filter { it.value == null }
                    RecipientsPagerAdapter.TAB.TAB_ANSWERED -> answeredUserIds.filter { it.value == null }
                    RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED -> notAnsweredUserIds.filter { it.value == null }
                }.map { it.key }.take(MAX_ANSWER_OF_PAGE)
            if (currentParticipantIds.isEmpty()) return@launch
            val queryMap = mutableMapOf<String, String>()
            queryMap["user_ids"] = currentParticipantIds.joinToString(",")
            when (tab) {
                RecipientsPagerAdapter.TAB.TAB_ALL -> {
                    isAllUserLoading = true
                }
                RecipientsPagerAdapter.TAB.TAB_ANSWERED -> {
                    isAnsweredUserLoading = true
                }
                RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED -> {
                    isNotAnsweredUserLoading = true
                }
            }
            when (val result = getUserByIdsUseCase.invoke(queryMap)) {
                is Result.Success -> {
                    result.data.data.forEach {
                        if (allUserIds.containsKey(it.id)) {
                            allUserIds[it.id] = it
                        }
                        if (answeredUserIds.containsKey(it.id)) {
                            answeredUserIds[it.id] = it
                        }
                        if (notAnsweredUserIds.containsKey(it.id)) {
                            notAnsweredUserIds[it.id] = it
                        }
                    }
                    _allUsersLiveData.postValue(allUserIds.filter { it.value != null }.map { it.value!! })
                    _answeredUsersLiveData.postValue(answeredUserIds.filter { it.value != null }.map { it.value!! })
                    _notAnsweredUsersLiveData.postValue(notAnsweredUserIds.filter { it.value != null }.map { it.value!! })
                }
                is Result.Error -> {
                    val message = result.exception.message.orEmpty()
                    _snackBarErrorMessage.postValue(Event(message))
                }
            }
            when (tab) {
                RecipientsPagerAdapter.TAB.TAB_ALL -> {
                    isAllUserLoading = false
                }
                RecipientsPagerAdapter.TAB.TAB_ANSWERED -> {
                    isAnsweredUserLoading = false
                }
                RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED -> {
                    isNotAnsweredUserLoading = false
                }
            }
        }
    }

    fun isUserLoading(tab: String): Boolean {
        return when (RecipientsPagerAdapter.TAB.fromString(tab)) {
            RecipientsPagerAdapter.TAB.TAB_ALL -> isAllUserLoading
            RecipientsPagerAdapter.TAB.TAB_ANSWERED -> isAnsweredUserLoading
            RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED -> isNotAnsweredUserLoading
        }
    }
}
