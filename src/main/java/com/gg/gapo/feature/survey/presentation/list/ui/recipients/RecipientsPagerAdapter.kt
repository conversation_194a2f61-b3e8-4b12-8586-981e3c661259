package com.gg.gapo.feature.survey.presentation.list.ui.recipients

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.gg.gapo.feature.survey.domain.model.PrivacyType

internal class RecipientsPagerAdapter(
    fa: FragmentActivity,
    private val surveyId: String,
    private val surveyStatus: String,
    private val surveyType: Int,
    private val incognitoMode: Boolean,
    private val isSendToUser: <PERSON>olean
) : FragmentStateAdapter(fa) {

    override fun getItemCount(): Int {
        return if (surveyType == PrivacyType.PUBLIC.value && !incognitoMode && !isSendToUser) {
            1
        } else {
            3
        }
    }

    override fun createFragment(position: Int): Fragment {
        return if (surveyType == PrivacyType.PUBLIC.value && !incognitoMode && !isSendToUser) {
            RecipientsFragment.newInstance(
                TAB.TAB_ANSWERED.type,
                incognitoMode,
                surveyId,
                surveyStatus
            )
        } else {
            when (position) {
                0 -> {
                    RecipientsFragment.newInstance(
                        TAB.TAB_ALL.type,
                        incognitoMode,
                        surveyId,
                        surveyStatus
                    )
                }
                1 -> {
                    RecipientsFragment.newInstance(
                        TAB.TAB_ANSWERED.type,
                        incognitoMode,
                        surveyId,
                        surveyStatus
                    )
                }
                else -> {
                    RecipientsFragment.newInstance(
                        TAB.TAB_NOT_ANSWERED.type,
                        incognitoMode,
                        surveyId,
                        surveyStatus
                    )
                }
            }
        }
    }

    enum class TAB(val type: String) {
        TAB_ALL("TAB_ALL"), TAB_ANSWERED("TAB_ANSWERED"), TAB_NOT_ANSWERED("TAB_NOT_ANSWERED");

        companion object {
            @JvmStatic
            fun fromString(value: String): TAB =
                values().find { it.type == value } ?: TAB_ALL
        }
    }
}
