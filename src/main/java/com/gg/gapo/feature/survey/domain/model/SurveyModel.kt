package com.gg.gapo.feature.survey.domain.model

import android.os.Parcelable
import com.gg.gapo.feature.survey.data.remote.model.SurveyDto
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SurveyModel(
    @SerializedName("_id")
    var id: String = "",
    @SerializedName("question_draft_id")
    var draftId: String? = "",
    @SerializedName("title")
    var title: String = "",
    @SerializedName("des")
    var des: String = "",
    @SerializedName("type")
    var type: Int = PrivacyType.PRIVATE.value,
    @SerializedName("to")
    var to: MutableList<String>? = mutableListOf(),
    @SerializedName("to_user_ids")
    var toUserIds: MutableList<String> = mutableListOf(),
    @SerializedName("answer_ids")
    var answerIds: MutableList<String>? = mutableListOf(),
    @SerializedName("answer_ids_after_ques_changed")
    var answerIdsAfterQuesChanged: MutableList<String>? = mutableListOf(),
    @SerializedName("workspace_id")
    var workspaceId: String = "",
    @SerializedName("status")
    var status: Int = QuestionStatus.ENABLE.value,
    @SerializedName("status_title")
    var statusTitle: String = SurveyStatus.RUNNING.title,
    @SerializedName("questions")
    var questions: MutableList<QuestionModel>? = mutableListOf(),
    @SerializedName("start_date")
    var startDate: Long = 0,
    @SerializedName("schedule")
    var schedule: ScheduleModel = ScheduleModel(),
    @SerializedName("to_users")
    var toUser: List<UserProfileDataModel> = listOf(),
    @SerializedName("created_at")
    var createdAt: Long = 0,
    @SerializedName("updated_at")
    var updatedAt: Long = 0,
    @SerializedName("event_time")
    var eventTime: Long = 0,
    @SerializedName("next_time")
    var nextTime: Long = 0,
    @SerializedName("creator")
    var creator: UserProfileDataModel = UserProfileDataModel("", "", null, null, questionVersion = null, answeredAt = null, eventTime = null),
    @SerializedName("count_answers")
    var countAnswers: Int? = null,
    @SerializedName("count_all_answers")
    var countAllAnswers: Int? = null,
    @SerializedName("count_questions")
    var countQuestions: Int = 0,
    @SerializedName("to_department_ids")
    var toDepartmentIds: MutableList<String>? = mutableListOf(),
    @SerializedName("to_thread_ids")
    var toThreadIds: MutableList<String>? = mutableListOf(),
    @SerializedName("to_role_ids")
    var toRoleIds: MutableList<String>? = mutableListOf(),
    @SerializedName("submitted")
    var submitted: Boolean = false,
    @SerializedName("to_remove")
    var toRemove: MutableList<String>? = mutableListOf(),
    @SerializedName("to")
    var toAdd: MutableList<String>? = mutableListOf(),
    @SerializedName("sharing_level")
    var sharingLevel: Int = SharingLevel.WORKSPACE.value,
    @SerializedName("incognito_mode")
    var incognitoMode: Boolean = false,
    @SerializedName("question_version")
    var questionVersion: String = "",
    @SerializedName("banner")
    var banner: BannerModel? = null,
    @SerializedName("owner_ids")
    var ownerIds: MutableList<String> = mutableListOf(),
    @SerializedName("editor_ids")
    var editorIds: MutableList<String> = mutableListOf(),
    @SerializedName("creator_id")
    var creatorId: String = ""
) : Parcelable {

    fun toRemoteDto(): SurveyDto {
        return SurveyDto(
            id,
            draftId,
            title,
            des,
            type,
            to,
            answerIds,
            answerIdsAfterQuesChanged,
            workspaceId,
            status,
            statusTitle,
            questions.orEmpty().map { it.toRemoteDto() }.toMutableList(),
            startDate, schedule.toRemoteDto(),
            toUser.map { it.toRemoteDto() },
            createdAt,
            updatedAt,
            eventTime,
            nextTime,
            creator.toRemoteDto(),
            countAnswers,
            countAllAnswers,
            countQuestions,
            toDepartmentIds,
            toThreadIds,
            toRoleIds,
            submitted,
            toRemove,
            toAdd,
            sharingLevel,
            incognitoMode,
            questionVersion,
            banner?.mapToRemoteDto(),
            ownerIds,
            editorIds,
            creatorId
        )
    }
}

internal enum class QuestionStatus(val value: Int) {
    ENABLE(1),
    DISABLE(-1)
}

internal enum class SurveyStatus(val title: String) {
    RUNNING("Running"),
    PAUSE("Pause"),
    EXPIRED("Expired"),
    REMOVED("Removed")
}

internal enum class PrivacyType(val value: Int) {
    PUBLIC(1),
    PRIVATE(2),
}

internal enum class SharingLevel(val value: Int) {
    WORKSPACE(1),
    ALL_USER(2),
}
