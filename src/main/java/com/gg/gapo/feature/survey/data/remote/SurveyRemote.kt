package com.gg.gapo.feature.survey.data.remote

import com.gg.gapo.feature.survey.data.remote.model.*
import com.gg.gapo.feature.survey.data.remote.response.BaseDataResponse
import com.gg.gapo.feature.survey.data.remote.response.PagingDataResponseDto
import com.gg.gapo.feature.survey.data.remote.response.SurveysPagingDataResponseDto

/**
 * <AUTHOR>
 * @since 08/06/2022
 */
internal interface SurveyRemote {

    suspend fun createSurvey(data: SurveyDto): BaseDataResponse<SurveyDto>

    suspend fun updateSurvey(id: String, data: SurveyDto): BaseDataResponse<SurveyDto>

    suspend fun getCreatedSurveys(queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    suspend fun deleteSurvey(id: String): BaseDataResponse<SurveyDto>

    suspend fun remindUnAnswered(id: String)

    suspend fun createDraftSurvey(data: SurveyDto): BaseDataResponse<SurveyDto>

    suspend fun updateDraftSurvey(id: String, data: SurveyDto): BaseDataResponse<SurveyDto>

    suspend fun getDraftSurveys(queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    suspend fun deleteDraftSurvey(id: String): BaseDataResponse<SurveyDto>

    suspend fun getDraftSurveyById(id: String, queryMap: MutableMap<String, String>): BaseDataResponse<SurveyDto>

    suspend fun getRequiredSurveys(queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    suspend fun answerSurvey(data: SubmitSurveyRequest): BaseDataResponse<SurveyDto>

    suspend fun getDetailSurveyForUser(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): BaseDataResponse<SurveyDto>

    suspend fun getDetailSurvey(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): BaseDataResponse<SurveyDto>

    suspend fun getQuestionContainsAnswers(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<QuestionContainAnswerDto>

    suspend fun getQuestionContainsAnswersForCreator(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<QuestionContainAnswerDto>

    suspend fun getAnswersOfQuestion(queryMap: MutableMap<String, String>): PagingDataResponseDto<AnswerDto>

    suspend fun getListAnswerer(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<UserProfileDataDto>

    suspend fun getSurveyPeriods(
        surveyId: String,
        isCreator: Boolean,
        queryMap: Map<String, String>
    ): BaseDataResponse<List<SurveyPeriodDto>>

    suspend fun getRandomBanner(): BaseDataResponse<WrapBannerDto>

    suspend fun searchUserInviteWorkSpace(queryMap: MutableMap<String, String>): PagingDataResponseDto<UserProfileDataDto>

    suspend fun fetchUsersWithIds(queryMap: MutableMap<String, String>): PagingDataResponseDto<UserProfileDataDto>
    suspend fun fetchThreadsWithIds(queryMap: MutableMap<String, String>): PagingDataResponseDto<ThreadDto>
    suspend fun fetchRolesWithIds(queryMap: MutableMap<String, String>): PagingDataResponseDto<RoleDto>
    suspend fun fetchDepartmentsWithIds(queryMap: MutableMap<String, String>): PagingDataResponseDto<DepartmentDto>
}
