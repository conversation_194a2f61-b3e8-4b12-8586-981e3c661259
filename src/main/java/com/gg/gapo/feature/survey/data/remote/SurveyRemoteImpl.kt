package com.gg.gapo.feature.survey.data.remote

import com.gg.gapo.feature.survey.data.remote.api.MessengerService
import com.gg.gapo.feature.survey.data.remote.api.SurveyService
import com.gg.gapo.feature.survey.data.remote.model.SubmitSurveyRequest
import com.gg.gapo.feature.survey.data.remote.model.SurveyDto
import com.gg.gapo.feature.survey.data.remote.model.UserProfileDataDto
import com.gg.gapo.feature.survey.data.remote.response.PagingDataResponseDto

internal class SurveyRemoteImpl(
    private val service: SurveyService,
    private val messenger: MessengerService
) : SurveyRemote {

    override suspend fun createSurvey(data: SurveyDto) = service.createSurvey(data)

    override suspend fun getCreatedSurveys(queryMap: MutableMap<String, String>) =
        service.getCreatedSurveys(queryMap)

    override suspend fun createDraftSurvey(data: SurveyDto) = service.createDraftSurvey(data)

    override suspend fun updateDraftSurvey(
        id: String,
        data: SurveyDto
    ) = service.updateDraftSurvey(id, data)

    override suspend fun getDraftSurveys(queryMap: MutableMap<String, String>) =
        service.getDraftSurveys(queryMap)

    override suspend fun deleteDraftSurvey(id: String) = service.deleteDraftSurvey(id)

    override suspend fun remindUnAnswered(id: String) = service.remindUnAnswered(id)

    override suspend fun getDraftSurveyById(id: String, queryMap: MutableMap<String, String>) = service.getDraftSurveyById(id)

    override suspend fun getRequiredSurveys(queryMap: MutableMap<String, String>) =
        service.getRequiredSurveys(queryMap)

    override suspend fun updateSurvey(id: String, data: SurveyDto) = service.updateSurvey(id, data)

    override suspend fun deleteSurvey(id: String) = service.deleteSurvey(id)

    override suspend fun answerSurvey(data: SubmitSurveyRequest) = service.answerSurvey(data)

    override suspend fun getDetailSurveyForUser(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ) =
        service.getDetailSurveyForUser(surveyId, queryMap)

    override suspend fun getDetailSurvey(surveyId: String, queryMap: MutableMap<String, String>) =
        service.getDetailSurvey(surveyId, queryMap)

    override suspend fun getQuestionContainsAnswers(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ) = service.getQuestionContainsAnswers(surveyId, queryMap)

    override suspend fun getQuestionContainsAnswersForCreator(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ) = service.getQuestionContainsAnswersForCreator(surveyId, queryMap)

    override suspend fun getAnswersOfQuestion(queryMap: MutableMap<String, String>) =
        service.getAnswersOfQuestion(queryMap)

    override suspend fun getListAnswerer(surveyId: String, queryMap: MutableMap<String, String>) =
        service.getListAnswerer(surveyId, queryMap)

    override suspend fun getSurveyPeriods(
        surveyId: String,
        isCreator: Boolean,
        queryMap: Map<String, String>
    ) = if (isCreator) {
        service.getSurveyPeriodForCreator(surveyId, queryMap)
    } else {
        service.getSurveyPeriodForUser(surveyId, queryMap)
    }

    override suspend fun getRandomBanner() = service.getRandomBanner()

    override suspend fun searchUserInviteWorkSpace(queryMap: MutableMap<String, String>): PagingDataResponseDto<UserProfileDataDto> {
        return service.searchUserInviteWorkSpace(queryMap)
    }

    override suspend fun fetchUsersWithIds(queryMap: MutableMap<String, String>) =
        service.fetchUsersWithIds(queryMap)

    override suspend fun fetchThreadsWithIds(queryMap: MutableMap<String, String>) =
        messenger.fetchThreadsWithIds(queryMap)

    override suspend fun fetchRolesWithIds(queryMap: MutableMap<String, String>) =
        service.fetchRolesWithIds(queryMap)

    override suspend fun fetchDepartmentsWithIds(queryMap: MutableMap<String, String>) =
        service.fetchDepartmentsWithIds(queryMap)
}
