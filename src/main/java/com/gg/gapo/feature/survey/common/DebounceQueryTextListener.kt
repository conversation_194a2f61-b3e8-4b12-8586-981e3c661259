package com.gg.gapo.feature.survey.common

import android.text.Editable
import android.text.TextWatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 */
internal class DebounceQueryTextListener(
    private val debouncePeriod: Long,
    private val coroutineScope: CoroutineScope,
    private val onDebounceQueryTextChange: (query: String) -> Unit
) : TextWatcher {

    private var searchJob: Job? = null

    override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
    }

    override fun afterTextChanged(p0: Editable?) {
        searchJob?.cancel()
        searchJob = coroutineScope.launch {
            p0?.toString()?.let { newText ->
                delay(debouncePeriod)
                onDebounceQueryTextChange(newText)
            }
        }
    }
}
