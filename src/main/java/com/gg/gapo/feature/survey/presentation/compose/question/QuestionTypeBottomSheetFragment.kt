package com.gg.gapo.feature.survey.presentation.compose.question

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.survey.databinding.SurveyQuestionTypeBottomSheetFragmentBinding
import com.gg.gapo.feature.survey.domain.model.QuestionType
import com.gg.gapo.feature.survey.presentation.compose.question.interfaces.QuestionTypeSelectListener

internal class QuestionTypeBottomSheetFragment : GapoBottomSheetFragment() {

    private var binding by autoCleared<SurveyQuestionTypeBottomSheetFragmentBinding>()
    private var questionType = QuestionType.TEXT
    private var questionIndex = -1
    private var listener: QuestionTypeSelectListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        questionType =
            arguments?.getSerializable(QUESTION_TYPE_ARG) as QuestionType? ?: QuestionType.TEXT
        questionIndex = arguments?.getInt(QUESTION_POSITION_ARG, -1) ?: -1
        listener =
            parentFragment as? QuestionTypeSelectListener ?: activity as? QuestionTypeSelectListener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = SurveyQuestionTypeBottomSheetFragmentBinding.inflate(inflater, container, false)
        binding.questionType = questionType
        initViews()
        return binding.root
    }

    private fun initViews() {
        binding.btnCheckbox.setDebouncedClickListener {
            listener?.onQuestionTypeSelect(QuestionType.MULTIPLE, questionIndex)
            dismissAllowingStateLoss()
        }
        binding.btnRadio.setDebouncedClickListener {
            listener?.onQuestionTypeSelect(QuestionType.SELECT, questionIndex)
            dismissAllowingStateLoss()
        }
        binding.btnShortAnswer.setDebouncedClickListener {
            listener?.onQuestionTypeSelect(QuestionType.TEXT, questionIndex)
            dismissAllowingStateLoss()
        }
        binding.btnAttachment.setDebouncedClickListener {
            listener?.onQuestionTypeSelect(QuestionType.ATTACHMENT, questionIndex)
            dismissAllowingStateLoss()
        }
        binding.btnPoint.setDebouncedClickListener {
            listener?.onQuestionTypeSelect(QuestionType.SCALE, questionIndex)
            dismissAllowingStateLoss()
        }
    }

    companion object {
        private const val QUESTION_TYPE_ARG = "QUESTION_TYPE_ARG"
        private const val QUESTION_POSITION_ARG = "QUESTION_POSITION_ARG"

        fun newInstance(type: QuestionType, questionPosition: Int) =
            QuestionTypeBottomSheetFragment().apply {
                arguments = bundleOf(
                    QUESTION_TYPE_ARG to type,
                    QUESTION_POSITION_ARG to questionPosition
                )
            }
    }
}
