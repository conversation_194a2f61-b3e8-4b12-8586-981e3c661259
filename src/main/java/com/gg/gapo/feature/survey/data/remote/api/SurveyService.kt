package com.gg.gapo.feature.survey.data.remote.api

import com.gg.gapo.core.utilities.api.GapoApiVersion
import com.gg.gapo.feature.survey.data.remote.model.*
import com.gg.gapo.feature.survey.data.remote.response.BaseDataResponse
import com.gg.gapo.feature.survey.data.remote.response.PagingDataResponseDto
import com.gg.gapo.feature.survey.data.remote.response.SurveysPagingDataResponseDto
import retrofit2.http.*

internal interface SurveyService {

    @POST("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection")
    suspend fun createSurvey(@Body data: SurveyDto): BaseDataResponse<SurveyDto>

    @PUT("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/{id}")
    suspend fun updateSurvey(
        @Path("id") id: String,
        @Body data: SurveyDto
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection")
    suspend fun getCreatedSurveys(@QueryMap queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    @DELETE("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/{id}")
    suspend fun deleteSurvey(
        @Path("id") id: String
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/{id}/unanswered/remind")
    suspend fun remindUnAnswered(
        @Path("id") id: String
    )

    @POST("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection-draft")
    suspend fun createDraftSurvey(@Body data: SurveyDto): BaseDataResponse<SurveyDto>

    @PUT("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection-draft/{id}")
    suspend fun updateDraftSurvey(
        @Path("id") id: String,
        @Body data: SurveyDto
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection-draft")
    suspend fun getDraftSurveys(@QueryMap queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    @DELETE("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection-draft/{id}")
    suspend fun deleteDraftSurvey(
        @Path("id") id: String
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection-draft/{id}")
    suspend fun getDraftSurveyById(
        @Path("id") id: String
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/question-collection")
    suspend fun getRequiredSurveys(@QueryMap queryMap: MutableMap<String, String>): SurveysPagingDataResponseDto<SurveyDto>

    @POST("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/answer")
    suspend fun answerSurvey(@Body data: SubmitSurveyRequest): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/question-collection/{id}")
    suspend fun getDetailSurveyForUser(
        @Path("id") id: String,
        @QueryMap queryMap: MutableMap<String, String>
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/{QUESTION_COLLECTION_ID}")
    suspend fun getDetailSurvey(
        @Path("QUESTION_COLLECTION_ID") surveyId: String,
        @QueryMap queryMap: MutableMap<String, String>
    ): BaseDataResponse<SurveyDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/question-collection/{QUESTION_COLLECTION_ID}/question-items")
    suspend fun getQuestionContainsAnswers(
        @Path("QUESTION_COLLECTION_ID") surveyId: String,
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<QuestionContainAnswerDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/{QUESTION_COLLECTION_ID}/question-items")
    suspend fun getQuestionContainsAnswersForCreator(
        @Path("QUESTION_COLLECTION_ID") surveyId: String,
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<QuestionContainAnswerDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/answer")
    suspend fun getAnswersOfQuestion(@QueryMap queryMap: MutableMap<String, String>): PagingDataResponseDto<AnswerDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/{QUESTION_COLLECTION_ID}/users")
    suspend fun getListAnswerer(
        @Path("QUESTION_COLLECTION_ID") surveyId: String,
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<UserProfileDataDto>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/admin/question-collection/periods")
    suspend fun getSurveyPeriodForCreator(
        @Query("question_collection_id") surveyId: String,
        @QueryMap queryMap: Map<String, String>
    ): BaseDataResponse<List<SurveyPeriodDto>>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/question-collection/periods")
    suspend fun getSurveyPeriodForUser(
        @Query("question_collection_id") surveyId: String,
        @QueryMap queryMap: Map<String, String>
    ): BaseDataResponse<List<SurveyPeriodDto>>

    @GET("${GapoApiVersion.PERIODIC_SURVEY_API_VERSION}/banners/random")
    suspend fun getRandomBanner(): BaseDataResponse<WrapBannerDto>

    @GET("${GapoApiVersion.SEARCH_API_VERSION}/search-user-invite-workspace")
    suspend fun searchUserInviteWorkSpace(@QueryMap queryMap: MutableMap<String, String>): PagingDataResponseDto<UserProfileDataDto>

    @GET("${GapoApiVersion.USER_INFO_API_VERSION}/listusers")
    suspend fun fetchUsersWithIds(
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<UserProfileDataDto>

    @GET("${GapoApiVersion.ORGANIZATION_CHART_WORKSPACE_API_VERSION}/role/multi")
    suspend fun fetchRolesWithIds(
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<RoleDto>

    @GET("${GapoApiVersion.ORGANIZATION_CHART_WORKSPACE_API_VERSION}/department/multi")
    suspend fun fetchDepartmentsWithIds(
        @QueryMap queryMap: MutableMap<String, String>
    ): PagingDataResponseDto<DepartmentDto>
}
