package com.gg.gapo.feature.survey.presentation.list.ui.recipients

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.R
import com.gg.gapo.core.ui.bottomsheet.GapoAlertBottomSheetFragment
import com.gg.gapo.core.ui.bottomsheet.lifecycleOwner
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.view.recyclerview.InfiniteScrollListener
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.survey.databinding.RecipientsFragmentBinding
import com.gg.gapo.feature.survey.domain.model.SurveyStatus
import com.gg.gapo.feature.survey.presentation.list.controller.RecipientsController
import com.gg.gapo.feature.survey.presentation.list.viewmodel.RecipientsViewModel

/**
 * <AUTHOR>
 * @since 20/10/2021
 */
internal class RecipientsFragment : Fragment() {

    private var binding by autoCleared<RecipientsFragmentBinding>()
    private var controller by autoCleared<RecipientsController>()
    private val recipientsViewModel by activityViewModels<RecipientsViewModel>()
    private var tab = ""
    private var surveyId = ""
    private var surveyStatus = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = RecipientsFragmentBinding.inflate(inflater, container, false)
        initViews()
        return binding.root
    }

    private fun initViews() {
        with(binding.listUser) {
            layoutManager = LinearLayoutManager(requireContext())
            setController(
                RecipientsController(
                    requireContext(),
                    GapoGlide.with(this@RecipientsFragment)
                ).also {
                    controller = it
                }
            )
            addOnScrollListener(object :
                    InfiniteScrollListener(layoutManager as LinearLayoutManager, 15) {
                    override fun onLoadMore() {
                        recipientsViewModel.fetchParticipants(tab)
                    }

                    override fun isDataLoading(): Boolean {
                        return recipientsViewModel.isUserLoading(tab)
                    }
                })
        }
        recipientsViewModel.fetchParticipants(tab)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val incognitoMode = arguments?.getBoolean(INCOGNITO_MODE_ARG, false) ?: false
        tab = arguments?.getString(TAB_NAME_ARG, RecipientsPagerAdapter.TAB.TAB_ALL.type)
            ?: RecipientsPagerAdapter.TAB.TAB_ALL.type
        surveyId = arguments?.getString(SURVEY_ID_ARG, "") ?: ""
        surveyStatus = arguments?.getString(SURVEY_STATUS_ARG, "") ?: ""

        when (tab) {
            RecipientsPagerAdapter.TAB.TAB_ALL.type -> {
                binding.lineReminder.visibility = View.GONE
                binding.lnReminder.visibility = View.GONE
                recipientsViewModel.allUsersLiveData.observe(viewLifecycleOwner) {
                    controller.submitList(it, incognitoMode, tab)
                    if (it.isEmpty()) {
                        binding.llEmpty.visibility = View.VISIBLE
                        binding.listUser.visibility = View.GONE
                    }
                }
            }
            RecipientsPagerAdapter.TAB.TAB_ANSWERED.type -> {
                binding.lineReminder.visibility = View.GONE
                binding.lnReminder.visibility = View.GONE
                recipientsViewModel.answeredUsersLiveData.observe(viewLifecycleOwner) {
                    controller.submitList(it, incognitoMode, tab)
                    if (!incognitoMode && it.isEmpty()) {
                        binding.llEmpty.visibility = View.VISIBLE
                        binding.listUser.visibility = View.GONE
                    }
                }
            }
            RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED.type -> {
                recipientsViewModel.notAnsweredUsersLiveData.observe(viewLifecycleOwner) {
                    controller.submitList(
                        it,
                        incognitoMode,
                        RecipientsPagerAdapter.TAB.TAB_NOT_ANSWERED.type
                    )
                    if (!incognitoMode && it.isEmpty()) {
                        binding.llEmpty.visibility = View.VISIBLE
                        binding.listUser.visibility = View.GONE
                    }
                    if (it.isNotEmpty() && surveyStatus == SurveyStatus.RUNNING.title) {
                        binding.lineReminder.visibility = View.VISIBLE
                        binding.lnReminder.visibility = View.VISIBLE
                    }

                    binding.lnReminder.setDebouncedClickListener {
                        showDialogConfirmSubmit(surveyId)
                    }
                }
            }
        }
    }

    private fun showDialogConfirmSubmit(surveyId: String) {
        GapoAlertBottomSheetFragment.Builder()
            .setIconRes(R.drawable.ic64_line15_bell)
            .setTintIconRes(GapoColors.criticalPrimary)
            .setTitleRes(GapoStrings.survey_v2_send_reminder)
            .setDescription(requireContext().getString(GapoStrings.survey_v2_send_reminder_description))
            .setFirstButtonStylesRes(GapoStyles.GapoButton_Large_BgSecondary)
            .setFirstButtonTextRes(GapoStrings.shared_cancel)
            .setSecondButtonStylesRes(GapoStyles.GapoButton_Large_AccentWorkPrimary)
            .setSecondButtonTextRes(GapoStrings.survey_v2_send_now)
            .setCompanionObject(69)
            .setListener(object : GapoAlertBottomSheetFragment.Listener {
                override fun onClickFirstButton(companionObject: Any?) {}

                override fun onClickSecondButton(companionObject: Any?) {
                    recipientsViewModel.remindUnAnswered(surveyId)
                }
            })
            .create()
            .lifecycleOwner(viewLifecycleOwner)
            .show(childFragmentManager, GapoAlertBottomSheetFragment.TAG)
    }

    companion object {
        private const val TAB_NAME_ARG = "TAB_NAME_ARG"
        private const val INCOGNITO_MODE_ARG = "INCOGNITO_MODE_ARG"
        private const val SURVEY_ID_ARG = "SURVEY_ID_ARG"
        private const val SURVEY_STATUS_ARG = "SURVEY_STATUS_ARG"

        fun newInstance(
            tab: String,
            incognitoMode: Boolean,
            surveyId: String,
            surveyStatus: String
        ) = RecipientsFragment().apply {
            arguments = bundleOf(
                TAB_NAME_ARG to tab,
                INCOGNITO_MODE_ARG to incognitoMode,
                SURVEY_ID_ARG to surveyId,
                SURVEY_STATUS_ARG to surveyStatus
            )
        }
    }
}
