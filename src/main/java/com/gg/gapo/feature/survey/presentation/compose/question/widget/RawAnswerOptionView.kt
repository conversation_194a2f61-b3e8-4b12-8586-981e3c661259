package com.gg.gapo.feature.survey.presentation.compose.question.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.feature.survey.R
import com.gg.gapo.feature.survey.domain.model.AnswerType
import com.gg.gapo.feature.survey.domain.model.QuestionModel
import com.gg.gapo.feature.survey.presentation.compose.question.controller.RawAnswerOptionController
import com.gg.gapo.feature.survey.presentation.compose.question.interfaces.OptionQuestionListener
import com.gg.gapo.feature.survey.presentation.event.ComposeQuestionAddOptionEvent
import com.gg.gapo.feature.survey.presentation.event.ComposeQuestionOptionTextChangedEvent
import com.gg.gapo.feature.survey.presentation.event.ComposeQuestionRemoveOptionEvent
import org.greenrobot.eventbus.EventBus

internal class RawAnswerOptionView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), OptionQuestionListener {

    private var rcvOptions: EpoxyRecyclerView? = null

    private var controller: RawAnswerOptionController? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.view_raw_answer_list, this)
        controller = RawAnswerOptionController(context, this)
        rcvOptions = findViewById(R.id.rcv_options)
        rcvOptions?.apply {
            layoutManager = object : LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false) {
                override fun canScrollVertically(): Boolean = false
            }
            controller?.let {
                setController(it)
            }
        }
    }

    /**
     * set data cho các comments
     * @param questionType loại câu hỏi, dùng để bind content
     * @param list list answer
     */
    fun setRawData(
        questionModel: QuestionModel,
        questionIndex: Int
    ) {
        controller?.setData(
            questionModel,
            questionIndex
        )
//        controller?.let {
//            EventBus.getDefault().post(
//                ComposeQuestionFetchOptionEvent(it.getOptions(), questionIndex)
//            )
//        }
    }

    override fun onClickedAddMoreOption(questionModel: QuestionModel, questionIndex: Int) {
        controller?.let { controller ->
            val options = controller.getOptions()
            val containEmptyOption = options.any { it.content.isEmpty() && it.type != AnswerType.OTHER.type }
            if (containEmptyOption) {
                GapoToast.makeNegative(context, context.getString(com.gg.gapo.core.ui.R.string.report_content_blank_message)).show()
                return
            }
            controller.addRawChoice()
            EventBus.getDefault().post(
                ComposeQuestionAddOptionEvent(options, questionIndex)
            )
        }
    }

    override fun onClickedAddOtherOption(questionModel: QuestionModel, questionIndex: Int) {
        controller?.addRawOtherChoice()
        controller?.let {
            EventBus.getDefault().post(
                ComposeQuestionAddOptionEvent(it.getOptions(), questionIndex)
            )
        }
    }

    override fun onClickedRemoveOption(
        questionModel: QuestionModel,
        questionIndex: Int,
        optionIndex: Int
    ) {
        controller?.removeChoice(optionIndex)
        controller?.let {
            EventBus.getDefault().post(
                ComposeQuestionRemoveOptionEvent(it.getOptions(), questionIndex)
            )
        }
    }

    override fun onOptionTextChanged(content: String, questionIndex: Int, optionIndex: Int) {
        controller?.let {
            EventBus.getDefault().post(
                ComposeQuestionOptionTextChangedEvent(content, questionIndex, optionIndex)
            )
        }
    }
}
