package com.gg.gapo.feature.survey.domain

import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.feature.survey.data.remote.model.SubmitSurveyRequest
import com.gg.gapo.feature.survey.data.remote.model.SurveyDto
import com.gg.gapo.feature.survey.data.remote.response.BaseDataResponse
import com.gg.gapo.feature.survey.data.remote.response.BaseResponse
import com.gg.gapo.feature.survey.domain.model.*
import com.gg.gapo.feature.survey.domain.model.organization.DepartmentModel
import com.gg.gapo.feature.survey.domain.model.organization.RoleModel
import com.gg.gapo.feature.survey.domain.model.organization.ThreadModel

internal interface SurveyRepository {

    suspend fun createSurvey(data: SurveyDto): Result<BaseResponse<SurveyModel>>

    suspend fun updateSurvey(id: String, data: SurveyDto): Result<BaseResponse<SurveyModel>>

    suspend fun getCreatedSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>>

    suspend fun createDraftSurvey(data: SurveyDto): Result<BaseResponse<SurveyModel>>

    suspend fun updateDraftSurvey(id: String, data: SurveyDto): Result<BaseResponse<SurveyModel>>

    suspend fun getDraftSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>>

    suspend fun deleteSurvey(id: String): Result<String>

    suspend fun remindUnAnswered(id: String): Result<Unit>

    suspend fun deleteDraftSurvey(id: String): Result<String>

    suspend fun getRequiredSurveys(queryMap: MutableMap<String, String>): Result<SurveysPagingDataResponseModel<SurveyModel>>

    suspend fun answerSurvey(data: SubmitSurveyRequest): Result<String>

    suspend fun getDetailSurvey(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Pair<Int, Result<BaseResponse<SurveyModel>>>

    suspend fun getDraftSurveyById(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<BaseResponse<SurveyModel>>

    suspend fun getDetailCreatedSurvey(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<BaseDataResponse<SurveyModel>>

    suspend fun getQuestionContainsAnswers(
        questionId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<QuestionContainAnswerModel>>

    suspend fun getQuestionContainsAnswersForCreator(
        questionId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<QuestionContainAnswerModel>>

    suspend fun getAnswersOfQuestion(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<AnswerModel>>

    suspend fun getListAnswerer(
        surveyId: String,
        queryMap: MutableMap<String, String>
    ): Result<PagingDataResponseModel<UserProfileDataModel>>

    suspend fun getSurveyPeriods(
        surveyId: String,
        isCreator: Boolean,
        queryMap: Map<String, String>
    ): Pair<List<SurveyPeriodModel>, String>

    suspend fun getRandomBanner(): Result<BaseResponse<WrapBannerModel>>

    suspend fun searchUserInviteWorkSpace(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<UserProfileDataModel>>

    suspend fun fetchUsersWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<UserProfileDataModel>>
    suspend fun fetchThreadsWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<ThreadModel>>
    suspend fun fetchRolesWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<RoleModel>>
    suspend fun fetchDepartmentsWithIds(queryMap: MutableMap<String, String>): Result<PagingDataResponseModel<DepartmentModel>>
}
