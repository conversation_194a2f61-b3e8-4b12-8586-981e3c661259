package com.gg.gapo.feature.survey.data.remote.model

import android.annotation.SuppressLint
import com.gg.gapo.feature.survey.domain.model.*
import com.gg.gapo.feature.survey.domain.model.UserProfileDataModel
import com.google.gson.annotations.SerializedName

@SuppressLint("ParcelCreator")
data class SurveyDto(
    @SerializedName("_id")
    var id: String? = null,
    @SerializedName("question_draft_id")
    var draftId: String? = null,
    @SerializedName("title")
    var title: String? = null,
    @SerializedName("des")
    var des: String? = null,
    @SerializedName("type")
    var type: Int? = null,
    @SerializedName("to")
    var to: MutableList<String>? = null,
    @SerializedName("answer_ids")
    var answerIds: MutableList<String>? = null,
    @SerializedName("answer_ids_after_ques_changed")
    var answerIdsAfterQuesChanged: MutableList<String>? = null,
    @SerializedName("workspace_id")
    var workspaceId: String? = null,
    @SerializedName("status")
    var status: Int? = null,
    @SerializedName("status_title")
    var statusTitle: String? = null,
    @SerializedName("questions")
    var questions: MutableList<QuestionDto>? = null,
    @SerializedName("start_date")
    var startDate: Long? = null,
    @SerializedName("schedule")
    var schedule: ScheduleDto? = null,
    @SerializedName("to_users")
    var toUser: List<UserProfileDataDto?>? = null,
    @SerializedName("created_at")
    var createdAt: Long? = null,
    @SerializedName("updated_at")
    var updatedAt: Long? = null,
    @SerializedName("event_time")
    var eventTime: Long? = null,
    @SerializedName("next_time")
    var nextTime: Long? = null,
    @SerializedName("creator")
    var creator: UserProfileDataDto? = null,
    @SerializedName("count_answers")
    var countAnswers: Int? = null,
    @SerializedName("count_all_answers")
    var countAllAnswers: Int? = null,
    @SerializedName("count_questions")
    var countQuestions: Int? = null,
    @SerializedName("to_department_ids")
    var toDepartmentIds: MutableList<String>? = null,
    @SerializedName("to_thread_ids")
    var toThreadIds: MutableList<String>? = null,
    @SerializedName("to_role_ids")
    var toRoleIds: MutableList<String>? = null,
    @SerializedName("submitted")
    var submitted: Boolean? = null,
    @SerializedName("to_remove")
    var toRemove: MutableList<String>? = null,
    @SerializedName("to_add")
    var toAdd: MutableList<String>? = null,
    @SerializedName("sharing_level")
    var sharingLevel: Int? = SharingLevel.WORKSPACE.value,
    @SerializedName("incognito_mode")
    var incognitoMode: Boolean? = false,
    @SerializedName("question_version")
    var questionVersion: String? = null,
    @SerializedName("banner")
    var banner: BannerDto? = null,
    @SerializedName("owner_ids")
    var ownerIds: MutableList<String>? = null,
    @SerializedName("editor_ids")
    var editorIds: MutableList<String>? = null,
    @SerializedName("creator_id")
    var creatorId: String? = null,
    @SerializedName("to_user_ids")
    var toUserIds: MutableList<String>? = null
) {
    fun mapToDomainModel(): SurveyModel {
        return SurveyModel(
            id = id.orEmpty(),
            draftId = draftId.orEmpty(),
            title = title.orEmpty(),
            des = des.orEmpty(),
            type = type ?: 2,
            to = to ?: mutableListOf(),
            answerIds = answerIds ?: mutableListOf(),
            answerIdsAfterQuesChanged = answerIdsAfterQuesChanged ?: mutableListOf(),
            workspaceId = workspaceId.orEmpty(),
            status = status ?: QuestionStatus.ENABLE.value,
            statusTitle = statusTitle ?: SurveyStatus.RUNNING.title,
            questions = questions?.map { it.mapToDomainModel() }?.toMutableList()
                ?: mutableListOf(),
            startDate = startDate ?: 0,
            schedule = (schedule?.mapToDomainModel() ?: ScheduleModel()),
            toUser = toUser?.mapNotNull { it?.mapToDomainModel() } ?: listOf(),
            createdAt = createdAt ?: 0L,
            updatedAt = updatedAt ?: 0L,
            eventTime = eventTime ?: 0L,
            nextTime = nextTime ?: 0L,
            creator = (
                creator?.mapToDomainModel() ?: UserProfileDataModel(
                    "",
                    "",
                    null,
                    null,
                    questionVersion = null,
                    answeredAt = null,
                    eventTime = null
                )
                ),
            countAnswers = countAnswers ?: 0,
            countAllAnswers = countAllAnswers ?: 0,
            countQuestions = countQuestions ?: 0,
            toDepartmentIds = toDepartmentIds ?: mutableListOf(),
            toThreadIds = toThreadIds ?: mutableListOf(),
            toRoleIds = toRoleIds ?: mutableListOf(),
            submitted = submitted ?: false,
            toRemove = toRemove ?: mutableListOf(),
            toAdd = toAdd ?: mutableListOf(),
            sharingLevel = sharingLevel ?: SharingLevel.WORKSPACE.value,
            incognitoMode = incognitoMode ?: false,
            questionVersion = questionVersion.orEmpty(),
            banner = banner?.mapToDomainModel() as BannerModel?,
            ownerIds = ownerIds ?: mutableListOf(),
            editorIds = editorIds ?: mutableListOf(),
            creatorId = creatorId.orEmpty(),
            toUserIds = toUserIds ?: mutableListOf()
        )
    }
}
