package com.gg.gapo.feature.post.presentation.post.media.horizontal.image

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.os.bundleOf
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.feature.post.databinding.PostMediaDetailsImageFragmentBinding
import com.gg.gapo.feature.post.presentation.post.media.horizontal.model.PostMediaDetailsViewData
import com.gg.gapo.feature.post.presentation.post.media.horizontal.viewmodel.PostMediaDetailsHorizontalViewModel
import com.github.penfeizhou.animation.glide.AnimationDecoderOption
import org.koin.androidx.viewmodel.ext.android.activityViewModel

/**
 * <AUTHOR>
 * @since 31/07/2021
 */
internal class PostMediaImageFragment : Fragment() {

    private val mediaDetailsViewModel by activityViewModel<PostMediaDetailsHorizontalViewModel>()

    private var binding by autoCleared<PostMediaDetailsImageFragmentBinding>()

    private var glideRequests by autoCleared<FeedImageLoader>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding =
            PostMediaDetailsImageFragmentBinding.inflate(inflater, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = mediaDetailsViewModel
        binding.imagePhoto.apply {
            scaleType = ImageView.ScaleType.FIT_CENTER
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        }

        initView()
        return binding.root
    }

    private fun initView() {
        val image =
            requireArguments().getParcelable<PostMediaDetailsViewData.Image>(MEDIA_IMAGE_ARG)
                ?: return

        glideRequests = GapoGlide.with(this)

        val thumbRequest = glideRequests.load(image.imageThumbSrc)
            .fitCenter()
            .priority(Priority.IMMEDIATE)
            .diskCacheStrategy(DiskCacheStrategy.ALL)

        glideRequests
            .load(image.imageSrc)
            .thumbnail(thumbRequest)
            .fitCenter()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .priority(Priority.IMMEDIATE)
            .set(AnimationDecoderOption.DISABLE_ANIMATION_GIF_DECODER, true)
            .into(binding.imagePhoto)
    }

    companion object {
        private const val MEDIA_IMAGE_ARG = "MEDIA_IMAGE_ARG"

        fun createInstance(image: PostMediaDetailsViewData.Image) =
            PostMediaImageFragment()
                .apply {
                    arguments = bundleOf(MEDIA_IMAGE_ARG to image)
                }
    }
}
