package com.gg.gapo.feature.livestream.data.remote

import com.gg.gapo.feature.livestream.data.model.confetti.remote.*

internal class ConfettiDataSource(private val service: ConfettiService, private val serviceLowTimeout: ConfettiServiceLowTimeout) {

    internal suspend fun getGameConfetti(postId: String) = service.getGameConfetti(postId)

    internal suspend fun createAnswer(createAnswerRequest: CreateAnswerRequest) =
        serviceLowTimeout.createAnswer(createAnswerRequest)

    internal suspend fun getGames(
        postId: String,
        status: String
    ) = service.getGames(
        HashMap<String, String>().apply {
            if (postId.isNotEmpty()) {
                this["post_id"] = postId
            }
            if (status.isNotEmpty()) {
                this["status"] = status
            }
        }
    )

    internal suspend fun getQuestionResult(questionId: String) = service.getQuestionResult(questionId)

    internal suspend fun addStreamCft(request: AddCftLiveStreamRequest) = service.addStreamCft(request)

    internal suspend fun fetchCallServerTime() = service.fetchCallServerTime()
}
