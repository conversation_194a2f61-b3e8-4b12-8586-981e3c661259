package com.gg.gapo.feature.livestream.presentation.base

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.ui.utils.EdgeToEdgeUtils
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import com.gg.gapo.core.ui.R.string as GPStrings

internal abstract class BaseLivestreamActivity : GapoThemeBaseActivity() {

    protected val shareLivestreamViewModel by viewModel<ShareLivestreamViewModel>()

    private lateinit var windowInsetsController: WindowInsetsControllerCompat

    abstract fun getBaseViewModel(): BaseLivestreamViewModel

    abstract fun getRootView(): View

    private var networkCallback: ConnectivityManager.NetworkCallback =
        object : ConnectivityManager.NetworkCallback() {

            override fun onAvailable(network: Network) {
                getBaseViewModel().onConnectedNetwork()
            }

            override fun onLost(network: Network) {
                Timber.d("onLost")
                getBaseViewModel().onLostNetwork()
            }
        }

    private lateinit var connectivityManager: ConnectivityManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // Use EdgeToEdgeUtils for Android 15 compatibility
        EdgeToEdgeUtils.setupCustomWindowInsets(window.decorView) { _, ime, _ ->
            updateBottomContent(ime.bottom)
            if (ime.bottom > 0 && !shareLivestreamViewModel.isKeyboardShowing) {
                shareLivestreamViewModel.showKeyboard()
            } else if (ime.bottom == 0 && shareLivestreamViewModel.isKeyboardShowing) {
                hideSystemUI()
                shareLivestreamViewModel.forceCloseKeyboard()
            }
        }
        supportFragmentManager.registerFragmentLifecycleCallbacks(
            object :
                FragmentManager.FragmentLifecycleCallbacks() {
                override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                    super.onFragmentDetached(fm, f)
                    if (f is DialogFragment) {
                        hideSystemUI()
                    }
                }
            },
            true
        )
        observeEvent()
        observeNetworkStatus()
    }

    protected open fun observeEvent() {
        shareLivestreamViewModel.hideSystemUI.observe(this) { event ->
            event.getContentIfNotHandled()?.let {
                if (it) hideSystemUI()
            }
        }
        getBaseViewModel().onLostNetworkEvent.observe(this) { event ->
            event.getContentIfNotHandled()?.let {
                if (it) {
                    GapoToast.makeNegative(
                        this,
                        GPStrings.livestream_message_internet_error,
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
        getBaseViewModel().onConnectedNetworkEvent.observe(this) { event ->
            event.getContentIfNotHandled()?.let {
                if (it && getBaseViewModel().lostNetworkFirstTime) {
                    GapoToast.makePositive(
                        applicationContext,
                        GPStrings.livestream_message_internet_success,
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    abstract fun updateBottomContent(margin: Int)

    private fun hideSystemUI() {
        if (this::windowInsetsController.isInitialized) {
            hideSystemUIDelayed()
        } else {
            windowInsetsController = WindowInsetsControllerCompat(window, getRootView())
            windowInsetsController.systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            hideSystemUIDelayed()
        }
        Timber.d("hideSystemUI $windowInsetsController")
    }

    private fun hideSystemUIDelayed() {
        getRootView().post {
            try {
                windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    private fun observeNetworkStatus() {
        connectivityManager =
            getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.registerDefaultNetworkCallback(networkCallback)
        } else {
            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET).build()
            connectivityManager.registerNetworkCallback(request, networkCallback)
        }
    }

    /**
     * Dispatch onResume() to fragments.  Note that for better inter-operation
     * with older versions of the platform, at the point of this call the
     * fragments attached to the activity are *not* resumed.
     */
    override fun onResume() {
        super.onResume()
        hideSystemUI()
    }

    /**
     * Called when the activity has detected the user's press of the back
     * key. The [OnBackPressedDispatcher][.getOnBackPressedDispatcher] will be given a
     * chance to handle the back button before the default behavior of
     * [android.app.Activity.onBackPressed] is invoked.
     *
     * @see .getOnBackPressedDispatcher
     */
    override fun onBackPressed() {
        if (shareLivestreamViewModel.isStickerShowing) {
            shareLivestreamViewModel.forceCloseSticker()
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
        super.onDestroy()
    }
}
