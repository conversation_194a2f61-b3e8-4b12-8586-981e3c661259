package com.gg.gapo.feature.livestream.data.remote.user

import com.gg.gapo.core.utilities.api.GapoApiVersion
import com.gg.gapo.feature.livestream.data.model.UserProfileResponseDto
import retrofit2.http.*

internal interface UserProfileService {

    @GET("${GapoApiVersion.USER_PROFILE_API_VERSION}/profile/{userId}")
    suspend fun getUserProfile(
        @Path("userId") userId: String
    ): UserProfileResponseDto
}
