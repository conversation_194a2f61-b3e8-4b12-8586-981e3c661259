package com.gg.gapo.feature.livestream.presentation.viewer

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import com.airbnb.deeplinkdispatch.DeepLink
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.feature.livestream.R
import com.gg.gapo.feature.livestream.databinding.ActivityViewerLivestreamBinding
import com.gg.gapo.feature.livestream.presentation.base.BaseLivestreamActivity
import com.gg.gapo.feature.livestream.presentation.base.BaseLivestreamViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

/*
**
* ViewerLiveStreamActivity
*
* Open: user click to video has title LIVE on news feed or time line, group, page
* Main purpose: The user watches live stream
* Component: Include 2 Fragment: 1. show video 2. show the comments list.
* Feature: Picture in picture, handel rotate, comments, video, tracking, deep link...
*/

@AppDeepLink("livestream/{post_id}")
internal class ViewerLiveStreamActivity : BaseLivestreamActivity() {

    private var trackingForVideoTime = 0L

    private lateinit var binding: ActivityViewerLivestreamBinding

    private val viewModel by viewModel<ViewerLiveStreamViewModel>()

    override fun getRootView(): View {
        return binding.root
    }

    override fun getBaseViewModel(): BaseLivestreamViewModel {
        return viewModel
    }

    override fun updateBottomContent(margin: Int) {
        binding.contentFrame.setPadding(0, 0, 0, margin)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_viewer_livestream)
        if (intent?.getBooleanExtra(DeepLink.IS_DEEP_LINK, false) == true) {
            val postId = intent.extras?.getString("post_id", "") ?: ""
            if (postId.isNotEmpty()) {
                setupNewIntent(postId)
            }
        }
        trackingForVideoTime = System.currentTimeMillis()
    }

    private fun setupNewIntent(postId: String) {
        shareLivestreamViewModel.postId = postId
        setUpFragment()
    }

    private fun setUpFragment() {
        supportFragmentManager.beginTransaction()
            .replace(R.id.contentFrame, ViewerLiveStreamFragment.newInstance())
            .commitNow()
    }

    // todo need check switch livestream
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
//        intent?.let {
//            if (it.getBooleanExtra(DeepLink.IS_DEEP_LINK, false)) {
//                ContextCompat.startForegroundService(
//                    this,
//                    Intent(this, ViewerLiveStreamService::class.java).apply {
//                        putExtra(
//                            ViewerLiveStreamService.TITLE_FOREGROUND_VIEWER_LIVESTREAM_SERVICE,
//                            getString(R.string.livestream_watching)
//                        )
//                    })
//                resetPreviousData(intent.extras?.getString("post_id", "") ?: "")
//                setupNewIntent(intent)
//            } else {
//                resetPreviousData(ViewerLiveStreamActivityArgs.deserializeFrom(it).post?.id.orEmpty())
//                setUpFragment(ViewerLiveStreamActivityArgs.deserializeFrom(it).post)
//            }
//        }
    }

//    private fun resetPreviousData(postId: String) {
//        retainedFragment =
//            supportFragmentManager.findFragmentByTag("Data") as? ViewerLiveStreamFragment
//        if (retainedFragment != null && postId.isNotEmpty() && args?.post?.id != null && postId != args?.post?.id) {
//            liveStreamCommentViewModel.waitToSwitch = true
//            liveStreamCommentViewModel.resetDataSet()
//        }
//    }

    /**
     * Dispatch onResume() to fragments.  Note that for better inter-operation
     * with older versions of the platform, at the point of this call the
     * fragments attached to the activity are *not* resumed.
     */

    override fun onPause() {
        super.onPause()
//        if (args.post?.id.orEmpty().isNotEmpty()) {
//            GAPOAnalytics.getInstance(this).logEventFinishVideo(
//                args.post?.id.orEmpty(),
//                System.currentTimeMillis() - trackingForVideoTime,
//                screenName = "viewer_live_stream",
//                videoId = args.post?.getVideoId()
//            )
//        }
    }
}
