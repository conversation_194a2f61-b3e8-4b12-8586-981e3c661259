package com.gg.gapo.feature.livestream.data.model.domain

import android.os.Parcelable
import com.gg.gapo.feature.livestream.data.model.Dto
import com.gg.gapo.feature.livestream.data.model.EducationDto
import com.gg.gapo.feature.livestream.data.model.Model
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
internal data class EducationDataModel(
    @SerializedName("school")
    var school: String = "",
    @SerializedName("title")
    var title: String = "",
    @SerializedName("privacy")
    var privacy: Int = DEFAULT_PRIVACY
) : Parcelable, Model {
    override fun toLocalDto(): Dto {
        TODO("Not yet implemented")
    }

    override fun toRemoteDto(): Dto {
        return EducationDto(school, title, privacy)
    }

    fun isNotEmpty(): Boolean {
        return school.isNotEmpty()
    }

    companion object {
        var TAG: String = EducationDataModel::class.java.name
    }
}
