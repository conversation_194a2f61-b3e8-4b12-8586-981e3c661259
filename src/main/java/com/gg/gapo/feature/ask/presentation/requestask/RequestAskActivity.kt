package com.gg.gapo.feature.ask.presentation.requestask

import android.os.Bundle
import androidx.core.os.bundleOf
import com.airbnb.deeplinkdispatch.DeepLink
import com.gg.gapo.core.navigation.WebDeepLink
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.ask.AskDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.deeplink.user.MyQuestionsDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.feature.ask.R
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <AUTHOR>
 * @since 02/05/2022
 */
@WebDeepLink(
    value = [
        "ask/{id}",
        "ask/{id}/"
    ]
)
internal class RequestAskActivity : GapoThemeBaseActivity() {

    private val requestAskViewModel: RequestAskViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.request_ask_activity)

        if (intent.getBooleanExtra(DeepLink.IS_DEEP_LINK, false)) {
            val idOrAlias = intent.extras?.getString("id", "")
            if (idOrAlias.isNullOrEmpty()) {
                finish()
                return
            }
            requestAskViewModel.fetchUserProfile(idOrAlias)
        } else {
            finish()
            return
        }

        requestAskViewModel.exceptionLiveData.observe(
            this,
            EventObserver {
                if (it.message.isNullOrEmpty()) {
                    makeNegativeSnackbar(application.getString(GapoStrings.shared_error_general))?.show()
                } else {
                    makeNegativeSnackbar(it.message.toString())?.show()
                }
            }
        )

        requestAskViewModel.navigateToQuestionsEventLiveData
            .observe(
                this,
                EventObserver {
                    navigateToQuestionFragment()
                }
            )

        requestAskViewModel.navigateToAskEventLiveData
            .observe(
                this,
                EventObserver {
                    navigateToAskFragment(it.first, it.second)
                }
            )
    }

    private fun navigateToQuestionFragment() {
        navByDeepLink(MyQuestionsDeepLink())
        finish()
    }

    private fun navigateToAskFragment(id: String, displayName: String) {
        navByDeepLink(
            AskDeepLink(
                options = GapoDeepLink.Options(
                    bundleOf(
                        AskDeepLink.ANSWER_USER_ID_EXTRA to id,
                        AskDeepLink.ANSWER_USER_DISPLAY_NAME_EXTRA to displayName
                    )
                )
            )
        )
        finish()
    }
}
