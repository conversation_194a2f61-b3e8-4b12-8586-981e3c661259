package com.gg.gapo.livekit.call.presentation.outgoing

import android.annotation.SuppressLint
import android.app.KeyguardManager
import android.app.PictureInPictureParams
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.graphics.PixelFormat
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.util.Rational
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import androidx.core.os.bundleOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.deeplink.call.CallOutGoingDeepLink
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.GapoStyles
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.snackbar.makePositiveSnackbar
import com.gg.gapo.core.ui.snackbar.makeWarningSnackbar
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.livedata.debounce
import com.gg.gapo.feature.call.R
import com.gg.gapo.feature.call.databinding.CallV2OutgoingActivityBinding
import com.gg.gapo.feature.call.presentation.utils.CallConstants
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_KEYGUARD_LOCK
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_WAKE_LOCK
import com.gg.gapo.feature.call.presentation.utils.acquireWakeLookAndDisableKeyguard
import com.gg.gapo.feature.call.presentation.utils.isPermissionsGranted
import com.gg.gapo.feature.call.presentation.utils.isStatusBarShown
import com.gg.gapo.feature.call.presentation.utils.releaseWakeLookAndReenableKeyguard
import com.gg.gapo.livekit.call.common.event.CallDestroySessionBusEventV2
import com.gg.gapo.livekit.call.common.event.CallForceCloseRateCallBusEventV2
import com.gg.gapo.livekit.call.common.proximitysensor.ProximitySensor
import com.gg.gapo.livekit.call.common.utilities.flatMapLatestOrNull
import com.gg.gapo.livekit.call.domain.model.EndCallReason
import com.gg.gapo.livekit.call.domain.model.LocalCallType
import com.gg.gapo.livekit.call.domain.model.ParticipantModel
import com.gg.gapo.livekit.call.logger.CallLoggerV2
import com.gg.gapo.livekit.call.presentation.dialog.GapoCallBottomSheetFragment
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingUIEvent
import com.gg.gapo.livekit.call.presentation.outgoing.viewmodel.CallV2OutgoingViewModel
import com.gg.gapo.livekit.call.presentation.outgoing.widget.setStateMachineTransitionDataBinding
import com.gg.gapo.livekit.call.service.CallReceiverV2
import com.gg.gapo.livekit.call.service.outgoing.CallV2OutgoingService
import com.gg.gapo.livekit.call.service.outgoing.CallV2OutgoingService.Companion.OUTGOING_TIMER_LIMIT_CALL_DELAY
import com.gg.gapo.livekit.call.service.outgoing.CallV2OutgoingServiceBinder
import com.gg.gapo.livekit.call.service.player.CallOutgoingEndedSoundPlayer
import com.google.android.material.snackbar.BaseTransientBottomBar
import io.livekit.android.events.ParticipantEvent
import io.livekit.android.events.RoomEvent
import io.livekit.android.events.collect
import io.livekit.android.room.participant.ConnectionQuality
import io.livekit.android.room.participant.LocalParticipant
import io.livekit.android.room.participant.RemoteParticipant
import io.livekit.android.room.track.Track
import io.livekit.android.room.track.VideoTrack
import io.livekit.android.util.flow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @since 09/09/2020
 */
@SuppressLint("InvalidWakeLockTag")
@AppDeepLink(value = ["call_v2/out_going/{callType}/{partnerId}"])
internal class CallV2OutgoingActivity :
    GapoThemeBaseActivity(), GapoCallBottomSheetFragment.GapoCallBottomSheetFragmentListener {

    private var bound = false
    private var participantCount = 0

    private val boundServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, iBinder: IBinder) {
            val service = (iBinder as? CallV2OutgoingServiceBinder)?.service?.get() ?: return
            callOutgoingService = service
            observeCallType(service)
            observeOutgoingCallState(service)
            observeMicroCameraSpeakerState(service)
            observerInternetConnectivityChanged(service)
            observeLiveKitToken(service)
            resumeVideoSource()
            bound = true
        }

        override fun onServiceDisconnected(name: ComponentName) {
            bound = false
        }
    }

    private val callLogger by inject<CallLoggerV2>()

    private val powerManager by lazy(LazyThreadSafetyMode.NONE) {
        getSystemService<PowerManager>()
    }

    private val wakeUpWakeLock by lazy(LazyThreadSafetyMode.NONE) {
        powerManager?.newWakeLock(
            PowerManager.FULL_WAKE_LOCK or PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            CALL_WAKE_LOCK
        )
    }

    private val proximitySensorWakeLock by lazy(LazyThreadSafetyMode.NONE) {
        powerManager?.newWakeLock(
            PowerManager.PROXIMITY_SCREEN_OFF_WAKE_LOCK,
            CALL_PROXIMITY_SENSOR_WAKE_LOCK
        )
    }

    private val keyguardManager by lazy(LazyThreadSafetyMode.NONE) {
        getSystemService<KeyguardManager>()
    }

    private val keyguardLock by lazy(LazyThreadSafetyMode.NONE) {
        keyguardManager?.newKeyguardLock(CALL_KEYGUARD_LOCK)
    }

    private val callOutgoingViewModel by viewModel<CallV2OutgoingViewModel>()

    private val mainHandler = Handler(Looper.getMainLooper())

    private val autoHideStatusBarControllerRunnable = Runnable {
        hideStatusBar()
        hideController()
    }

    private val endedSoundPlayer by lazy(LazyThreadSafetyMode.NONE) {
        CallOutgoingEndedSoundPlayer(this)
    }

    private lateinit var binding: CallV2OutgoingActivityBinding

    private var orientation = Configuration.ORIENTATION_PORTRAIT

    private var callOutgoingService: CallV2OutgoingService? = null

    private val gapoDialogShowing = AtomicBoolean(false)

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        callOutgoingViewModel.parseIntent(intent)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        window.setFormat(PixelFormat.RGBA_8888)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        acquireWakeLookAndDisableKeyguard(
            keyguardManager,
            powerManager,
            wakeUpWakeLock,
            keyguardLock,
            OUTGOING_TIMER_LIMIT_CALL_DELAY
        )
        showStatusBar()
        super.onCreate(savedInstanceState)
        Thread.setDefaultUncaughtExceptionHandler { _, e ->
            Timber.e(e)
        }
        binding = CallV2OutgoingActivityBinding.inflate(LayoutInflater.from(this))
            .apply {
                lifecycleOwner = this@CallV2OutgoingActivity
                callOutgoingViewModel = <EMAIL>
                glideRequests = GapoGlide.with(this@CallV2OutgoingActivity)
            }
        setContentView(binding.root)
        callOutgoingViewModel.parseIntent(intent)

        if (!isPermissionsGranted(*callOutgoingViewModel.callPermissions)) {
            finishAndRemoveTask()
            return
        }

        registerEventBus()
        bindOutgoingService()
        callOutgoingViewModel.onClickDraggableLayoutEventLiveData
            .observe(
                this,
                EventObserver {
                    removeAutoHideStatusBarController()
                    val isStatusBarShown = isStatusBarShown
                    if (isStatusBarShown) {
                        hideStatusBar()
                        hideController()
                    } else {
                        showStatusBar()
                        showController()
                        scheduleAutoHideStatusBarController()
                    }
                }
            )

        callOutgoingViewModel.onClickArrowDownButtonEventLiveData
            .debounce()
            .observe(
                this,
                EventObserver {
                    binding.layoutFullRender.offSwitchButton()
                    onBackPressed()
                }
            )

        callOutgoingViewModel.onClickMuteButtonEventLiveData
            .observe(
                this,
                EventObserver {
                    if (callOutgoingViewModel.callState.isEstablished) {
                        scheduleAutoHideStatusBarController()
                    }
                    callOutgoingViewModel.setMicrophoneMuted(it)
                    callOutgoingService?.setMicrophoneMute(it)
                }
            )

        callOutgoingViewModel.onClickCameraSourceButtonEventLiveData
            .debounce(500)
            .observe(
                this,
                EventObserver { disable ->
                    if (callOutgoingViewModel.callType.isAudioCall) {
                        Timber.d("CAll onClickCameraSourceButtonEventLiveData disable $disable")
                        binding.layoutFullRender.setVisiblePartnerAvatar(true)
                        binding.layoutFullRender.setVisibleRendererView(false)
                        callOutgoingViewModel.setCameraSourceStoppedButton(true)
                        if (callOutgoingViewModel.callState.isEstablished) {
                            scheduleAutoHideStatusBarController()
                        }
                        return@EventObserver
                    }
                    if (callOutgoingViewModel.callState.isEstablished) {
                        scheduleAutoHideStatusBarController()
                    } else {
                        binding.layoutFullRender.setPreviewCamera(!disable)
                    }
                    callOutgoingViewModel.setCameraSourceStoppedButton(disable)
                    lifecycleScope.launch {
                        callOutgoingViewModel.room.localParticipant.setCameraEnabled(!disable)
                    }
                }
            )

        callOutgoingViewModel.onClickSwitchCameraButtonEventLiveData
            .observe(
                this,
                EventObserver {
                    if (callOutgoingViewModel.callState.isEstablished) {
                        scheduleAutoHideStatusBarController()
                    }
                    callOutgoingViewModel.setSwitchCamera()
                }
            )

        callOutgoingViewModel.onClickExternalSpeakerButtonEventLiveData
            .observe(
                this,
                EventObserver { enable ->
                    if (callOutgoingViewModel.callState.isEstablished) {
                        scheduleAutoHideStatusBarController()
                    }
                    callOutgoingViewModel.setExternalSpeakerOpened(enable)
                    callOutgoingService?.setSpeakerphoneOn(enable)
                }
            )

        callOutgoingViewModel.onClickButtonHangupEventLiveData
            .observe(
                this,
                EventObserver {
                    sendBroadcast(CallReceiverV2.newHangupIntent(this))
                }
            )
        callOutgoingViewModel.stateMachineTransitionLiveData.observe(this) {
            binding.layoutDraggable.setStateMachineTransitionDataBinding(
                it,
                callOutgoingViewModel.brandingName
            )
        }

        callOutgoingViewModel.callTypeLiveData.observe(this) {
            callOutgoingService?.setCallType(it)
        }

        callOutgoingViewModel.room.initVideoRenderer(binding.layoutFullRender.fullViewRender())
        callOutgoingViewModel.room.initVideoRenderer(binding.layoutPipRender.pipViewRender())
        if (callOutgoingViewModel.callType.isVideoCall) {
            if (callOutgoingViewModel.callType.isOffer) {
                binding.layoutFullRender.setPreviewCamera(true)
                callOutgoingViewModel.setExternalSpeakerOpened(true)
            }
            callOutgoingViewModel.setCameraSourceStoppedButton(false)
            binding.layoutFullRender.setVisiblePartnerAvatar(false)
            binding.layoutFullRender.setVisibleRendererView(true)
        } else {
            callOutgoingViewModel.setCameraSourceStoppedButton(true)
            binding.layoutFullRender.setVisiblePartnerAvatar(true)
            binding.layoutFullRender.setVisibleRendererView(false)
        }

        callOutgoingViewModel.onClickTurnOnCameraButtonEventLiveData.observe(
            this,
            EventObserver {
                it.let {
                    GapoCallBottomSheetFragment.show(supportFragmentManager)
                }
            }
        )
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                callOutgoingViewModel.participants
                    .collect { participants ->
                        participantCount = participants.size
                        checkOnEntering()
                        participants.forEach { participant ->
                            val videoTrackPubFlow = participant::videoTrackPublications.flow
                                .map { participant to it }
                                .flatMapLatest { (participant, videoTracks) ->
                                    val trackPublication =
                                        participant.getTrackPublication(Track.Source.SCREEN_SHARE)
                                            ?: participant.getTrackPublication(Track.Source.CAMERA)
                                            ?: videoTracks.firstOrNull()?.first

                                    flowOf(trackPublication)
                                }

                            val videoTrackFlow =
                                videoTrackPubFlow.flatMapLatestOrNull { pub -> pub::track.flow }
                            launch {
                                videoTrackFlow.collect { videoTrack ->
                                    val track = videoTrack as? VideoTrack
                                    track?.let {
                                        Timber.d("CAll callOutgoingViewModel.participants videoTrackFlow")
                                        if (participant == callOutgoingViewModel.room.localParticipant) {
                                            binding.layoutFullRender.setVisiblePartnerAvatar(false)
                                            binding.layoutFullRender.setVisibleRendererView(true)
                                            binding.layoutPipRender.setVisibleRendererView(true)
                                            binding.layoutFullRender.addVideoTrackLocal(track)
                                            binding.layoutPipRender.addVideoTrack(track)
                                            if (participants.size == 2 && callOutgoingViewModel.isCameraSourceStoppedLiveData.value == false) {
                                                binding.layoutFullRender.renderVideoRemote()
                                                binding.layoutPipRender.renderVideo()
                                            } else {
                                                binding.layoutFullRender.renderVideoLocal()
                                            }
                                            Timber.d("CAll localParticipant / participants.size: ${participants.size} / isCameraSourceStoppedLiveData: ${callOutgoingViewModel.isCameraSourceStoppedLiveData.value}")
                                        } else {
                                            callOutgoingViewModel.switchToCamera()
                                            binding.layoutFullRender.setPreviewCamera(false)
                                            binding.layoutFullRender.setVisiblePartnerAvatar(false)
                                            binding.layoutFullRender.setVisibleRendererView(true)
                                            binding.layoutFullRender.addVideoTrackRemote(track)
                                            binding.layoutFullRender.renderVideoRemote()
                                            binding.layoutPipRender.renderVideo()
                                            Timber.d("CAll remoteParticipants / participants.size: ${participants.size} / isCameraSourceStoppedLiveData: ${callOutgoingViewModel.isCameraSourceStoppedLiveData.value}")
                                        }
                                        Timber.d("participant is Local ${participant is LocalParticipant}")
                                    }
                                }
                            }
                            callOutgoingViewModel.coroutineScope.launch {
                                videoTrackPubFlow
                                    .filter { trackPublication -> trackPublication?.participant?.get() is RemoteParticipant }
                                    .flatMapLatestOrNull { pub -> pub::muted.flow }
                                    .collectLatest { muted ->
                                        Timber.d("CAll callOutgoingViewModel.coroutineScope videoTrackPubFlow")
                                        checkCallType()
                                        binding.layoutFullRender.setMute(muted == true)
                                        if (muted == true) {
                                            binding.layoutFullRender.setVisiblePartnerAvatar(
                                                true
                                            )
                                            binding.layoutFullRender.setVisibleRendererView(
                                                false
                                            )
                                        } else {
                                            binding.layoutFullRender.setVisiblePartnerAvatar(
                                                false
                                            )
                                            binding.layoutFullRender.setVisibleRendererView(true)
                                        }
                                    }
                            }
                        }
                    }
            }
        }
//        callOutgoingViewModel.coroutineScope.launch {
//            delay(2000)
//            if (callOutgoingViewModel.callType.isAudioCall) {
//                binding.layoutPipRender.setVisibleRendererView(false)
//                Timber.d("CAll addVideoTrack layoutPipRender is GONE")
//                binding.layoutFullRender.setVisiblePartnerAvatar(true)
//                binding.layoutFullRender.setVisibleRendererView(false)
//            }
//        }
        callOutgoingViewModel.coroutineScope.launch {
            callOutgoingViewModel.room.localParticipant.events.collect { event ->
                Timber.d("room.events $event")
                when (event) {
                    is ParticipantEvent.LocalTrackUnpublished -> {
                        callLogger.captureMessage("LocalTrackUnpublished ${event.publication.kind}")
                    }

                    is ParticipantEvent.LocalTrackPublished -> {
                        callLogger.captureMessage("LocalTrackPublished ${event.publication.kind}")
                    }

                    else -> {}
                }
            }
        }

        callOutgoingViewModel.coroutineScope.launch {
            callOutgoingViewModel.room.events.collect { event ->
                when (event) {
                    is RoomEvent.TrackSubscribed -> {
                        callLogger.captureMessage("TrackSubscribed ${event.track}")
                    }

                    is RoomEvent.TrackUnsubscribed -> {
                        callLogger.captureMessage("TrackUnsubscribed ${event.track}")
                    }

                    is RoomEvent.Connected -> {
                        callLogger.captureMessage("Connected")
                        val localParticipant = callOutgoingViewModel.room.localParticipant
                        localParticipant.setMicrophoneEnabled(true)
                        localParticipant.setCameraEnabled(callOutgoingViewModel.callType.isVideoCall)
                        callOutgoingService?.stopDisconnectedTimeoutTimer()
                    }

                    is RoomEvent.Disconnected -> {
                        callLogger.captureMessage("Disconnected")
                        if (callOutgoingViewModel.callState.isEstablished) {
                            showPopupReconnect()
                        }
                    }

                    is RoomEvent.Reconnected -> {
                        callLogger.captureMessage("Reconnected")
                        makePositiveSnackbar(
                            message = getString(GapoStrings.call_group_reconnected),
                            duration = BaseTransientBottomBar.LENGTH_SHORT
                        )?.show()
                        callOutgoingService?.stopDisconnectedTimeoutTimer()
                    }

                    is RoomEvent.Reconnecting -> {
                        callLogger.captureMessage("Reconnecting")
                        makeWarningSnackbar(
                            message = getString(GapoStrings.call_group_reconnecting),
                            duration = BaseTransientBottomBar.LENGTH_LONG
                        )?.show()
                        callOutgoingService?.startDisconnectedTimeoutTimer()
                    }

                    is RoomEvent.FailedToConnect -> {
                        callLogger.captureFailedCall(event.error.message.orEmpty())
                        callLogger.captureEndCall(EndCallReason.CONNECT_FAILED.name)
                        callOutgoingViewModel.callCenter.endCall(EndCallReason.CONNECT_FAILED)
                    }

                    is RoomEvent.ActiveSpeakersChanged -> {
                        Timber.d("room ActiveSpeakersChanged ")
                    }

                    is RoomEvent.TrackMuted -> {
                        callLogger.captureMessage("TrackMuted ${event.participant}")
                    }

                    is RoomEvent.TrackUnmuted -> {
                        callLogger.captureMessage("TrackUnmuted ${event.participant}")
                    }

                    is RoomEvent.ParticipantConnected -> {
                        callLogger.captureMessage("ParticipantConnected Room  ${event.participant}")
                    }

                    is RoomEvent.ParticipantDisconnected -> {
                        callLogger.captureMessage("ParticipantDisconnected Room  ${event.participant}")
                    }

                    is RoomEvent.DataReceived -> {
                        try {
                            if (callOutgoingViewModel.isCameraSourceStoppedLiveData.value == false) {
                                callOutgoingViewModel.room.localParticipant.setCameraEnabled(true)
                            }
                            val data = event.data.toString(Charsets.UTF_8)
                            if (data.contains("ping", true)) {
                                callOutgoingService?.stopOutgoingTimeoutTimer()
                            }
                        } catch (e: Exception) {
                            Timber.e(e)
                        }
                    }

                    is RoomEvent.ConnectionQualityChanged -> {
                        if (event.participant is RemoteParticipant) {
                            when (event.quality) {
                                ConnectionQuality.LOST -> {
                                    callOutgoingService?.startDisconnectedTimeoutTimer()
                                }

                                ConnectionQuality.GOOD,
                                ConnectionQuality.EXCELLENT -> {
                                    callOutgoingService?.stopDisconnectedTimeoutTimer()
                                }

                                else -> {}
                            }
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun showPopupReconnect() {
        AlertDialog.Builder(this, GapoStyles.GapoDialog_Alert)
            .setTitle(getString(GapoStrings.call_group_connection_quality_4))
            .setMessage(GapoStrings.call_group_call_disconnected_please_retry)
            .setPositiveButton(getString(GapoStrings.call_group_reconnect)) { dialog, _ ->
                callOutgoingViewModel.callCenter.destroyLivekit()
                callOutgoingViewModel.callCenter.startConnect()
                dialog.dismiss()
            }
            .setNegativeButton(getString(GapoStrings.shared_exit)) { dialog, _ ->
                callOutgoingViewModel.onClickHangupButton()
                dialog.dismiss()
            }
            .show()
    }

    private fun checkOnEntering() {
        lifecycleScope.launch {
            // delay 5s để chắc chắn rằng các biến đã được khởi tạo
            delay(5000)
            Timber.e("checkOnEntering participantCount $participantCount")
            Timber.e("checkOnEntering bound $bound")
            Timber.e("checkOnEntering callOutgoingService $callOutgoingService")
            if (participantCount > 1 && bound) {
                callOutgoingService?.onEntering = false
                callOutgoingService?.stopEnteringTimeoutTimer()
                Timber.e("checkOnEntering stopEnteringTimeoutTimer sau 5s")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        resumeVideoSource()

//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
//            callOutgoingService?.removeOverlay()
//        }
//        callOutgoingService?.removeOverlay()
    }

    /**
     * Nếu đã call đã ended thì destroy activity và finish luôn task.
     * Nếu đang kết nối hoặc đang trong cuộc gọi thì check theo PIP Mode.
     * Nếu đang ở PIP Mode và dưới Android 10 thì sử dụng trick để restore lại activity.
     * Nếu đang ở PIP Mode và trên Android 10 thì turn off camera.
     */
    override fun onStop() {
        super.onStop()
        removeAutoHideStatusBarController()
        if (callOutgoingViewModel.callState.isEnded) {
            finishAndRemoveTask()
        } else if (callOutgoingViewModel.isInPIPMode) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                startActivity(intent)
            } else {
                pauseVideoSource()
            }
        }
    }

    override fun onBackPressed() {
        if (!minimizeToPIP()) {
            super.onBackPressed()
        }
    }

    override fun onUserLeaveHint() {
        if (!minimizeToPIP()) {
            super.onUserLeaveHint()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        callOutgoingViewModel.onClickHangupButton()
        endedSoundPlayer.stopEndedSound()
        unbindOutgoingService()
        unregisterEventBus()
        releaseWakeLookAndReenableKeyguard(
            keyguardManager,
            powerManager,
            wakeUpWakeLock,
            keyguardLock
        )
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean,
        newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        if (callOutgoingViewModel.callState.isEstablished) {
            if (isInPictureInPictureMode) {
                removeAutoHideStatusBarController()
            } else {
                showStatusBar()
                scheduleAutoHideStatusBarController()
            }
        }
        callOutgoingViewModel.onPictureInPictureModeChanged(isInPictureInPictureMode)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        orientation = newConfig.orientation
    }

    @SuppressLint("WakelockTimeout")
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onProximitySensor(distance: ProximitySensor.Distance) {
        // if (callOutgoingViewModel.callType.isAudioCall) {
        val wakeLock = proximitySensorWakeLock ?: return
        if (distance.isNear) {
            if (!wakeLock.isHeld) {
                wakeLock.acquire()
            }
        } else {
            if (wakeLock.isHeld) {
                wakeLock.release()
            }
        }
        // }
    }

    /**
     * Cuộc gọi đã kết thúc
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onDestroySession(event: CallDestroySessionBusEventV2) {
        unbindOutgoingService()
        if (callOutgoingViewModel.isInPIPMode) {
            finishAndRemoveTask()
        } else {
            val block: (() -> Unit) = {
                finishAndRemoveTask()
            }
            endedSoundPlayer.playEndedSound(block)
        }
    }

    /**
     * Cuộc gọi đã kết thúc và có cuộc gọi mới đến
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onForceCloseRateCall(event: CallForceCloseRateCallBusEventV2) {
        if (endedSoundPlayer.isPlaying) {
            finishAndRemoveTask()
        }
    }

    private fun bindOutgoingService() {
        val intent = Intent(this, CallV2OutgoingService::class.java)
            .apply {
                action = CallV2OutgoingService.START_OUTGOING_SERVICE_ACTION
                putExtras(callOutgoingViewModel.bundleToStartService)
            }
        ContextCompat.startForegroundService(this, intent)
        if (!bound) {
            bindService(intent, boundServiceConnection, BIND_AUTO_CREATE)
        }
    }

    private fun unbindOutgoingService() {
        pauseVideoSource()
        if (bound) {
            bound = false
            callOutgoingService?.let {
                it.partnerProfileLiveData.removeObservers(this)
                it.callStateLiveData.removeObservers(this)
                it.isInternetConnectedLiveData.removeObservers(this)
            }
            callOutgoingService = null
            unbindService(boundServiceConnection)
        }
    }

    private fun observeCallType(callOutgoingService: CallV2OutgoingService) {
        callOutgoingViewModel.callTypeLiveData.observe(this) {
            callOutgoingService.setCallType(it)
        }
    }

    private fun observeLiveKitToken(callOutgoingService: CallV2OutgoingService) {
        callOutgoingService.isLivekitConnectedLiveData.observe(this) {
            if (it) {
                Timber.e("akaizz isLivekitConnectedLiveData true")
                callOutgoingViewModel.cancelAllNotification()
                if (callOutgoingViewModel.callType == LocalCallType.Answer.Video || callOutgoingViewModel.callType == LocalCallType.Offer.Video) {
                    callOutgoingViewModel.setExternalSpeakerOpened(true)
                }
            }
        }
    }

    private fun observeOutgoingCallState(callOutgoingService: CallV2OutgoingService) {
        callOutgoingService.callStateLiveData
            .observe(this) { state ->
                callOutgoingViewModel.setOutgoingCallState(state)
                when {
                    state.isEstablishing -> {
                        showStatusBar()
                        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
                    }

                    state.isEstablished -> {
                        scheduleAutoHideStatusBarController()
                        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_USER
                    }

                    state.isEnded -> {
                        showStatusBar()
                        onProximitySensor(ProximitySensor.Distance.FAR)
                        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
                    }
                }
            }
    }

    private fun observeMicroCameraSpeakerState(callOutgoingService: CallV2OutgoingService) {
//        binding.layoutController.setInitialValue(
//            callOutgoingService.isVideoCapturerStoppedLiveData.value,
//            callOutgoingService.isMicrophoneMuteLiveData.value,
//            callOutgoingService.isExternalSpeakerOnLiveData.value
//        )

        binding.layoutController.setInitialValue(
            false,
            false,
            true
        )
    }

    private fun observerInternetConnectivityChanged(callOutgoingService: CallV2OutgoingService) {
        callOutgoingService.isInternetConnectedLiveData
            .observe(this) { isConnected ->
                callOutgoingViewModel.onConnectivityChanged(isConnected)
            }
    }

    private fun minimizeToPIP(): Boolean {
        return if (!callOutgoingViewModel.callState.isEnded) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val rational = if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                    Rational(
                        resources.getInteger(R.integer.call_pip_rational_numerator_portrait),
                        resources.getInteger(R.integer.call_pip_rational_denominator_portrait)
                    )
                } else {
                    Rational(
                        resources.getInteger(R.integer.call_pip_rational_numerator_landscape),
                        resources.getInteger(R.integer.call_pip_rational_denominator_landscape)
                    )
                }
                val params = PictureInPictureParams.Builder()
                    .setAspectRatio(rational)
                    .build()
                enterPictureInPictureMode(params)
                true
            } else {
                pauseVideoSource()
                false
//            callOutgoingService?.showOverlay()
            }
//        callOutgoingService?.showOverlay()}
        } else false
    }

    private fun resumeVideoSource() {
        if (callOutgoingViewModel.callType.isVideoCall && callOutgoingViewModel.callState.isEstablished) {
            callOutgoingService?.resumeVideoSource()
        }
    }

    private fun pauseVideoSource() {
        if (callOutgoingViewModel.callType.isVideoCall && callOutgoingViewModel.callState.isEstablished) {
            callOutgoingService?.pauseVideoSource()
        }
    }

    private fun scheduleAutoHideStatusBarController() {
        removeAutoHideStatusBarController()
        mainHandler.postDelayed(
            autoHideStatusBarControllerRunnable,
            AUTO_HIDE_STATUS_BAR_CONTROLLER_DELAY_TIME
        )
    }

    private fun removeAutoHideStatusBarController() {
        mainHandler.removeCallbacks(autoHideStatusBarControllerRunnable)
    }

    private fun showStatusBar() {
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
    }

    private fun hideStatusBar() {
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY or View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_FULLSCREEN
    }

    private fun showController() {
        callOutgoingViewModel.transition(CallOutgoingUIEvent.ShowController)
    }

    private fun hideController() {
        callOutgoingViewModel.transition(CallOutgoingUIEvent.HideController)
    }

    companion object {
        private const val AUTO_HIDE_STATUS_BAR_CONTROLLER_DELAY_TIME = 5_000L // 5 seconds
        private const val CALL_PROXIMITY_SENSOR_WAKE_LOCK = "CALL_PROXIMITY_SENSOR_WAKE_LOCK"

        internal fun Context.getCallOutgoingIntent(
            partner: ParticipantModel,
            callType: LocalCallType
        ) = Intent(this, CallV2OutgoingActivity::class.java)
            .apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
                val bundle = bundleOf(
                    CallConstants.CALL_PARTNER_ID_EXTRA to partner.userId.orEmpty(),
                    CallConstants.CALL_TYPE_EXTRA to callType,
                    CallOutGoingDeepLink.PARTNER_NAME_EXTRA to partner.name.orEmpty(),
                    CallOutGoingDeepLink.PARTNER_AVATAR_EXTRA to partner.avatar.orEmpty(),
                    CallOutGoingDeepLink.PARTNER_AVATAR_THUMB_EXTRA to partner.avatar.orEmpty()
                )
                putExtras(bundle)
            }
    }

    override fun onTurnOnCamera(isFromMe: Boolean) {
        callOutgoingViewModel.switchToCamera()
        callOutgoingViewModel.turnOnCameraSource()
        binding.layoutFullRender.setForceTurnOn()
        callOutgoingViewModel.setExternalSpeakerOpened(true)
    }

    override fun onDialogDismiss() {
        gapoDialogShowing.set(false)
    }

    private fun checkCallType() {
        if (callOutgoingViewModel.callType.isAudioCall && !gapoDialogShowing.get()) {
            gapoDialogShowing.set(true)
            GapoCallBottomSheetFragment.show(
                supportFragmentManager,
                callOutgoingViewModel.partnerNameLiveData.value.orEmpty()
            )
        }
    }
}
