package com.gg.gapo.livekit.call.presentation.outgoing.viewmodel

import android.app.Application
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.annotation.StringRes
import androidx.core.os.bundleOf
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airbnb.deeplinkdispatch.DeepLink
import com.gg.gapo.core.eventbus.call.CallStickyBusEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.deeplink.call.CallOutGoingDeepLink
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.image.thumbpattern.GapoThumbPattern
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.livedata.Event
import com.gg.gapo.feature.call.domain.usecase.SaveLastCallIdUseCase
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_PARTNER_EXTRA
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_PARTNER_ID_EXTRA
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_TYPE_EXTRA
import com.gg.gapo.feature.call.service.outgoing.state.CallOutgoingCallState
import com.gg.gapo.livekit.call.center.CallCenterV2
import com.gg.gapo.livekit.call.domain.model.LocalCallType
import com.gg.gapo.livekit.call.domain.model.ParticipantModel
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingStateMachineTransition
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingUIEvent
import com.gg.gapo.livekit.call.presentation.outgoing.state.StateMachineFactory
import com.twilio.audioswitch.AudioDevice
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import io.livekit.android.room.track.CameraPosition
import io.livekit.android.room.track.LocalVideoTrack
import io.livekit.android.room.track.Track
import io.livekit.android.room.track.VideoTrack
import io.livekit.android.util.flow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.webrtc.PeerConnection
import timber.log.Timber
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong

internal class CallV2OutgoingViewModel(
    private val context: Application,
    private val userManager: UserManager,
    val callCenter: CallCenterV2,
    private val onPremiseManager: OnPremiseManager,
    private val coroutineDispatchers: CoroutineDispatchers,
    private val saveLastCallIdUseCase: SaveLastCallIdUseCase
) : ViewModel() {

    private val audioHandler by lazy {
        callCenter.audioHandler
    }

    val room by lazy {
        callCenter.room
    }

    val brandingName = onPremiseManager.onPremiseConfigData?.brandName.orEmpty()

    val stateMachineTransitionLiveData: LiveData<CallOutgoingStateMachineTransition>
        get() = _stateMachineTransitionLiveData
    private val _stateMachineTransitionLiveData =
        MutableLiveData<CallOutgoingStateMachineTransition>()

    private val stateMachine = StateMachineFactory.create { transition ->
        _stateMachineTransitionLiveData.value = transition
    }

    val myAvatarLiveData: LiveData<String>
        get() = _myAvatarLiveData
    private val _myAvatarLiveData = MutableLiveData<String>()

    private val partnerName: String
        get() = _partnerNameLiveData.value.orEmpty()
    val partnerNameLiveData: MutableLiveData<String>
        get() = _partnerNameLiveData
    private val _partnerNameLiveData = MutableLiveData<String>()

    val textCallNoticeLiveData: LiveData<String>
        get() = _textCallNoticeLiveData
    private val _textCallNoticeLiveData = MutableLiveData<String>()

    val partnerAvatarLiveData: MutableLiveData<String>
        get() = _partnerAvatarLiveData
    private val _partnerAvatarLiveData = MutableLiveData<String>()

    val isClickableDraggableLayoutLiveData: LiveData<Boolean>
        get() = _isClickableDraggableLayoutLiveData
    private val _isClickableDraggableLayoutLiveData = MutableLiveData<Boolean>(false)

    val isClickableCameraAndMicLiveData: LiveData<Boolean>
        get() = _isClickableCameraAndMicLiveData
    private val _isClickableCameraAndMicLiveData = MutableLiveData(false)

    val isCameraSourceStoppedLiveData: LiveData<Boolean>
        get() = _isCameraSourceStoppedLiveData
    private val _isCameraSourceStoppedLiveData = MutableLiveData<Boolean>(false)

    val isMicrophoneMutedLiveData: LiveData<Boolean>
        get() = _isMicrophoneMutedLiveData
    private val _isMicrophoneMutedLiveData = MutableLiveData<Boolean>(false)

    val isSwitchCameraLiveData: LiveData<Boolean>
        get() = _isSwitchCameraLiveData
    private val _isSwitchCameraLiveData = MutableLiveData<Boolean>(false)

    val isExternalSpeakerOpenedLiveData: LiveData<Boolean>
        get() = _isExternalSpeakerOpenedLiveData
    private val _isExternalSpeakerOpenedLiveData = MutableLiveData<Boolean>(false)

    val onClickArrowDownButtonEventLiveData: LiveData<Event<Unit>>
        get() = _onClickArrowDownButtonEventLiveData
    private val _onClickArrowDownButtonEventLiveData = MutableLiveData<Event<Unit>>()

    val onLocalVideoTrackEventLiveData: LiveData<Event<VideoTrack?>>
        get() = _onLocalVideoTrackEventLiveData
    private val _onLocalVideoTrackEventLiveData = MutableLiveData<Event<VideoTrack?>>()

    val onClickExternalSpeakerButtonEventLiveData: LiveData<Event<Boolean>>
        get() = _onClickExternalSpeakerButtonEventLiveData
    private val _onClickExternalSpeakerButtonEventLiveData = MutableLiveData<Event<Boolean>>()

    val onClickDraggableLayoutEventLiveData: LiveData<Event<Unit>>
        get() = _onClickDraggableLayoutEventLiveData
    private val _onClickDraggableLayoutEventLiveData = MutableLiveData<Event<Unit>>()

    val onClickMuteButtonEventLiveData: LiveData<Event<Boolean>>
        get() = _onClickMuteButtonEventLiveData
    private val _onClickMuteButtonEventLiveData = MutableLiveData<Event<Boolean>>()

    val onClickCameraSourceButtonEventLiveData: LiveData<Event<Boolean>>
        get() = _onClickCameraSourceButtonEventLiveData
    private val _onClickCameraSourceButtonEventLiveData = MutableLiveData<Event<Boolean>>()

    val onClickButtonHangupEventLiveData: LiveData<Event<Unit>>
        get() = _onClickButtonHangupEventLiveData
    private val _onClickButtonHangupEventLiveData = MutableLiveData<Event<Unit>>()

    val onClickSwitchCameraButtonEventLiveData: LiveData<Event<Unit>>
        get() = _onClickSwitchCameraButtonEventLiveData
    private val _onClickSwitchCameraButtonEventLiveData = MutableLiveData<Event<Unit>>()

    val onClickTurnOnCameraButtonEventLiveData: LiveData<Event<Boolean>>
        get() = _onClickTurnOnCameraButtonEventLiveData
    private val _onClickTurnOnCameraButtonEventLiveData = MutableLiveData<Event<Boolean>>()

    val callState: CallOutgoingCallState
        get() = _callStateLiveData.value ?: CallOutgoingCallState.Establishing.Connecting
    val callStateLiveData: LiveData<CallOutgoingCallState>
        get() = _callStateLiveData
    private val _callStateLiveData =
        MutableLiveData<CallOutgoingCallState>(CallOutgoingCallState.Establishing.Connecting)

    val callPermissions: Array<String>
        get() = callType.callPermissions
    val callType: LocalCallType
        get() = _callTypeLiveData.value ?: throw IllegalStateException()

    val callTypeLiveData: LiveData<LocalCallType>
        get() = _callTypeLiveData
    private val _callTypeLiveData = MutableLiveData<LocalCallType>()

    val bundleToStartService: Bundle
        get() = bundleOf(
            CALL_PARTNER_EXTRA to partner,
            CALL_TYPE_EXTRA to callType
        )

    val isInPIPMode: Boolean
        get() = stateMachine.state.isInPIPMode

    val participants = room::remoteParticipants.flow
        .map { remoteParticipants ->
            listOf<Participant>(room.localParticipant) +
                remoteParticipants
                    .keys
                    .sortedBy { it.value }
                    .mapNotNull { remoteParticipants[it] }
        }

    private lateinit var partner: ParticipantModel

    val coroutineScope = CoroutineScope(coroutineDispatchers.main + SupervisorJob())

    private val count = AtomicLong(0L)

    init {
        val myUserProfile = userManager.userProfile
        _myAvatarLiveData.value = GapoThumbPattern.AVATAR_BIG_SIZE.parse(
            myUserProfile?.avatar,
            myUserProfile?.avatarThumbPattern
        )
        viewModelScope.launch {
            room::state.flow.collectLatest {
                when (it) {
                    Room.State.CONNECTED -> {
                        room.localParticipant.setMicrophoneEnabled(!(_isMicrophoneMutedLiveData.value ?: false))
                    }
                    else -> {
                    }
                }
            }
        }
//        initRoom()

//        val videoTrackPubFlow = room.localParticipant::videoTracks.flow
//            .map { room.localParticipant to it }
//            .flatMapLatest { (participant, videoTracks) ->
//                val trackPublication = participant.getTrackPublication(Track.Source.SCREEN_SHARE)
//                    ?: participant.getTrackPublication(Track.Source.CAMERA)
//                    ?: videoTracks.firstOrNull()?.first
//
//                flowOf(trackPublication)
//            }
//
//        coroutineScope.launch {
//            val videoTrackFlow = videoTrackPubFlow.flatMapLatestOrNull { pub -> pub::track.flow }
//            launch {
//                videoTrackFlow.collectLatest { videoTrack ->
//                    _onLocalVideoTrackEventLiveData.value = Event(videoTrack as? VideoTrack)
//                }
//            }
//        }
    }

    fun cancelAllNotification() {
        callCenter.cancelAllNotification()
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch(coroutineDispatchers.io + NonCancellable) {
            callCenter.destroyLivekit()
        }
        saveLastCallIdUseCase(userManager.userId, "")
        unregisterEventBus()
    }

    fun parseIntent(intent: Intent?) {
        val bundle = intent?.extras ?: return

        val partnerId = bundle.getString(CALL_PARTNER_ID_EXTRA) ?: throw IllegalStateException()
        val partnerName =
            bundle.getString(CallOutGoingDeepLink.PARTNER_NAME_EXTRA, "").orEmpty()
        val partnerAvatar = bundle.getString(CallOutGoingDeepLink.PARTNER_AVATAR_EXTRA)

        partner = ParticipantModel(
            userId = partnerId,
            name = partnerName,
            avatar = partnerAvatar,
            roomId = "",
            status = 0,
            reason = 0,
            createdAt = 0,
            updatedAt = 0
        )
        setPartnerProfile(partner)

        val isFromDeepLink = intent.getBooleanExtra(DeepLink.IS_DEEP_LINK, false)

        _callTypeLiveData.value = if (isFromDeepLink) {
            val type = bundle.getString(CALL_TYPE_EXTRA) ?: throw IllegalStateException()
            LocalCallType.getType(type)
        } else {
            bundle.parcelable(CALL_TYPE_EXTRA) ?: throw IllegalStateException()
        }

        if (callType.isVideoCall) {
            transition(CallOutgoingUIEvent.EstablishVideoCall)
        } else {
            transition(CallOutgoingUIEvent.EstablishAudioCall)
            _onClickCameraSourceButtonEventLiveData.value = Event(false)
        }
        registerEventBus()
    }

    fun switchToCamera() {
        if (callType !is LocalCallType.Offer.Video && callType !is LocalCallType.Answer.Video) {
            _callTypeLiveData.value = if (callType.isOffer) {
                LocalCallType.Offer.Video
            } else {
                LocalCallType.Answer.Video
            }
            transition(CallOutgoingUIEvent.ConvertFromAudioToVideo)
        }
    }
    fun transition(event: CallOutgoingUIEvent) {
        val nextTransition = stateMachine.transition(event)
        if (nextTransition is CallOutgoingStateMachineTransition) {
            Timber.e("state => ${nextTransition.fromState::class.java.simpleName} + ${nextTransition.event::class.java.simpleName} = ${nextTransition.toState::class.java.simpleName}")
        }
    }

    fun setPartnerProfile(partner: ParticipantModel) {
        _partnerNameLiveData.value = partner.name
        _partnerAvatarLiveData.value = partner.avatar
    }

    fun setOutgoingCallState(state: CallOutgoingCallState) {
        _callStateLiveData.value = state
        when {
            state.isEstablishing -> {
                setClickableDraggableLayout(false)
                setClickableCameraAndMic(false)
                when (state) {
                    is CallOutgoingCallState.Establishing.Connecting -> {
                        transition(CallOutgoingUIEvent.ConnectingServer)
                        setTextCallNotice(GapoStrings.call_connecting_all)
                    }

                    is CallOutgoingCallState.Establishing.Contacting -> {
                        transition(CallOutgoingUIEvent.Contacting)
                        setTextCallNotice(GapoStrings.call_contacting_all)
                    }

                    is CallOutgoingCallState.Establishing.PartnerRinging -> {
                        transition(CallOutgoingUIEvent.Ringing)
                        setTextCallNotice(GapoStrings.call_ringing_all)
                    }

                    else -> {
                    }
                }
            }

            state.isEstablished -> {
                setClickableDraggableLayout(true)
                setClickableCameraAndMic(true)
                transition(CallOutgoingUIEvent.CallEstablished)
            }

            state.isEnded -> {
                setClickableDraggableLayout(false)
                setClickableCameraAndMic(false)
                transition(CallOutgoingUIEvent.HangUpCall)
                when (state) {
                    is CallOutgoingCallState.Ended.PartnerDecline -> {
                        setTextCallNotice(GapoStrings.call_end_partner_decline_all)
                    }

                    is CallOutgoingCallState.Ended.PartnerIsInAnotherGapoCall -> {
                        setTextCallNotice(GapoStrings.call_end_partner_busy_all)
                    }

                    is CallOutgoingCallState.Ended.PartnerIsInAnotherVoIPCall -> {
                        setTextCallNotice(GapoStrings.call_end_hangup_all)
                    }

                    is CallOutgoingCallState.Ended.UnAuth -> {
                        setTextCallNotice(GapoStrings.call_end_un_auth_all)
                    }

                    is CallOutgoingCallState.Ended.UserNotFound -> {
                        setTextCallNotice(GapoStrings.call_end_user_not_found_all)
                    }

                    is CallOutgoingCallState.Ended.NoAnswer -> {
                        setTextCallNotice(GapoStrings.call_end_partner_no_answer_all)
                    }

                    is CallOutgoingCallState.Ended.ReachedLimitTime -> {
                        setTextCallNotice(GapoStrings.call_end_reach_limit_time_all)
                    }

                    is CallOutgoingCallState.Ended.YouAreInAnotherGapoCall -> {
                        setTextCallNotice(GapoStrings.call_end_you_are_on_another_gapo_call_all)
                    }

                    is CallOutgoingCallState.Ended.Hangup, CallOutgoingCallState.Ended.ForceHangup, CallOutgoingCallState.Ended.YouAreInAnotherVoIPCall -> {
                        setTextCallNotice(GapoStrings.call_end_hangup_all)
                    }

                    is CallOutgoingCallState.Ended.Disconnected -> {
                        setTextCallNotice(
                            context.getString(
                                GapoStrings.call_end_disconnected_all,
                                partnerName
                            )
                        )
                    }

                    is CallOutgoingCallState.Ended.PartnerIsNotFriend -> {
                        setTextCallNotice(GapoStrings.call_end_partner_is_not_friend_all)
                    }

                    else -> {
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    internal fun setChronometer(event: CallStickyBusEvent) {
        if (callType.isAudioCall && event.callDuration >= 0 && callState.isEstablished) {
            val hours = (TimeUnit.SECONDS.toHours(event.callDuration) % 60).toInt()
            val minutes = (TimeUnit.SECONDS.toMinutes(event.callDuration) % 60).toInt()
            val seconds = (TimeUnit.SECONDS.toSeconds(event.callDuration) % 60).toInt()

            val value = if (hours > 0) {
                String.format("%02d:%02d:%02d", hours, minutes, seconds)
            } else {
                String.format("%02d:%02d", minutes, seconds)
            }
            Timber.d("setChronometer Subscribe ${event.callDuration} = $value")
            setTextCallNotice(value)
        }
    }

    fun setRTCPeerConnectionState(state: PeerConnection.PeerConnectionState) {
        if (callType.isVideoCall && callState.isEstablished) {
            when (state) {
                PeerConnection.PeerConnectionState.NEW, PeerConnection.PeerConnectionState.CONNECTING -> {
                    transition(CallOutgoingUIEvent.RTCPeerConnecting)
                    setTextCallNotice(GapoStrings.call_outgoing_video_connecting)
                }

                PeerConnection.PeerConnectionState.CONNECTED -> {
                    transition(CallOutgoingUIEvent.RTCPeerConnected)
                }

                PeerConnection.PeerConnectionState.DISCONNECTED, PeerConnection.PeerConnectionState.FAILED -> {
                    transition(CallOutgoingUIEvent.RTCPeerDisconnected)
                    setTextCallNotice(GapoStrings.call_outgoing_video_connecting)
                }

                else -> {}
            }
        }
    }

    fun onPictureInPictureModeChanged(isInPIPMode: Boolean) {
        val event = if (isInPIPMode) {
            CallOutgoingUIEvent.EnterPIPMode
        } else {
            CallOutgoingUIEvent.ExitPIPMode
        }
        transition(event)
    }

    fun setRTCPartnerCameraOpened(isOpened: Boolean) {
        val event = if (isOpened) {
            CallOutgoingUIEvent.PartnerCameraOpened
        } else {
            CallOutgoingUIEvent.PartnerCameraClosed
        }
        transition(event)
    }

    fun onConnectivityChanged(isConnected: Boolean) {
        val event = if (isConnected) {
            CallOutgoingUIEvent.InternetConnected
        } else {
            CallOutgoingUIEvent.InternetDisconnected
        }
        transition(event)
    }

    fun setTextCallNotice(value: String) {
        _textCallNoticeLiveData.value = value
    }

    fun setTextCallNotice(@StringRes value: Int) {
        _textCallNoticeLiveData.value = context.getString(value)
    }

    fun setClickableDraggableLayout(isClickable: Boolean) {
        _isClickableDraggableLayoutLiveData.value = isClickable
    }

    fun setClickableCameraAndMic(isClickable: Boolean) {
        _isClickableCameraAndMicLiveData.value = isClickable
    }

    fun setMicrophoneMuted(isMuted: Boolean) {
        viewModelScope.launch {
            room.localParticipant.setMicrophoneEnabled(!isMuted)
        }
        _isMicrophoneMutedLiveData.value = isMuted
    }

    fun setCameraSourceStoppedButton(isStopped: Boolean) {
        _isCameraSourceStoppedLiveData.value = isStopped
        _isSwitchCameraLiveData.value = !isStopped
        val event = if (isStopped) {
            CallOutgoingUIEvent.MyCameraClosed
        } else {
            CallOutgoingUIEvent.MyCameraOpened
        }
        transition(event)
    }

    fun setExternalSpeakerOpened(isOpened: Boolean) {
        _isExternalSpeakerOpenedLiveData.value = isOpened
        viewModelScope.launch {
            if (isOpened) {
                audioHandler.availableAudioDevices.firstOrNull { it is AudioDevice.Speakerphone }
                    ?.let {
                        audioHandler.selectDevice(it)
                    }
            } else {
                audioHandler.availableAudioDevices.firstOrNull { it is AudioDevice.Earpiece }?.let {
                    audioHandler.selectDevice(it)
                }
            }
        }
    }

    fun setSwitchCamera() {
        val videoTrack = room.localParticipant.getTrackPublication(Track.Source.CAMERA)
            ?.track as? LocalVideoTrack
            ?: return

        val newPosition = when (videoTrack.options.position) {
            CameraPosition.FRONT -> CameraPosition.BACK
            CameraPosition.BACK -> CameraPosition.FRONT
            else -> null
        }
        videoTrack.switchCamera(position = newPosition)
    }

    fun onClickDraggableLayout() {
        _onClickDraggableLayoutEventLiveData.value = Event(Unit)
    }

    fun onClickArrowDownButton() {
        _onClickArrowDownButtonEventLiveData.value = Event(Unit)
    }

    fun onClickMuteButton(view: View) {
        if (callState.isEstablished) {
            val isNewValue = !view.isSelected
            _onClickMuteButtonEventLiveData.value = Event(isNewValue)
        }
    }

    fun onClickCameraSourceButton(view: View) {
        if (callState.isEstablished) {
            if (callType.isAudioCall) {
                _onClickTurnOnCameraButtonEventLiveData.value = Event(true)
                return
            }
            _onClickCameraSourceButtonEventLiveData.value = Event(!view.isSelected)
        }
    }

    fun turnOnCameraSource() {
        _onClickCameraSourceButtonEventLiveData.postValue(Event(false))
    }

    fun onClickExternalSpeakerButton(view: View) {
        _onClickExternalSpeakerButtonEventLiveData.value = Event(!view.isSelected)
    }

    fun onClickHangupButton() {
        _onClickButtonHangupEventLiveData.value = Event(Unit)
    }

    fun onClickSwitchCameraButton() {
        _onClickSwitchCameraButtonEventLiveData.value = Event(Unit)
    }
}
