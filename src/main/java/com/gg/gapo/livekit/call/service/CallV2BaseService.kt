package com.gg.gapo.livekit.call.service

import android.content.pm.ServiceInfo
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.LifecycleService
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.feature.call.domain.usecase.GetLastCallIdUseCase
import com.gg.gapo.feature.call.notification.CallNotifier
import com.gg.gapo.livekit.call.center.CallCenterV2
import com.gg.gapo.livekit.call.logger.CallLoggerV2
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * <AUTHOR>
 */
internal abstract class CallV2BaseService : LifecycleService() {

    protected val callCenter by inject<CallCenterV2>()

    protected val getLastCallIdUseCase by inject<GetLastCallIdUseCase>()

    protected val callLogger by inject<CallLoggerV2>()

    protected val userManager by inject<UserManager>()

    protected val myId: String
        get() = userManager.userId

    override fun onCreate() {
        super.onCreate()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CallNotifier.createCallInProgressNotificationChannel(this)
        }
    }

    /**
     * Android 15 timeout handling for foreground services.
     * This method is called when the system determines that the app is not in a state
     * where the service should be running as a foreground service.
     */
    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    override fun onTimeout(startId: Int, fgsType: Int) {
        Timber.d("CallV2BaseService timeout - startId: $startId, fgsType: $fgsType")

        when (fgsType) {
            ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE -> {
                handleSpecialUseTimeout(startId)
            }
            ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC -> {
                handleDataSyncTimeout(startId)
            }
            ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROCESSING -> {
                handleMediaProcessingTimeout(startId)
            }
            else -> {
                handleGenericTimeout(startId, fgsType)
            }
        }
    }

    /**
     * Handle timeout for SPECIAL_USE foreground services (call services).
     * For call services, we typically don't want to stop immediately as calls are critical.
     */
    protected open fun handleSpecialUseTimeout(startId: Int) {
        Timber.w("Special use call service timed out - this should not happen for call services")
        onCallServiceTimeoutAction()
        // Note: We don't call stopSelf() here as call services should continue running
    }

    /**
     * Handle timeout for DATA_SYNC foreground services.
     */
    protected open fun handleDataSyncTimeout(startId: Int) {
        Timber.w("Data sync service timed out - stopping service")
        onDataSyncTimeoutAction()
        stopSelf(startId)
    }

    /**
     * Handle timeout for MEDIA_PROCESSING foreground services.
     */
    protected open fun handleMediaProcessingTimeout(startId: Int) {
        Timber.w("Media processing service timed out - stopping service")
        onMediaProcessingTimeoutAction()
        stopSelf(startId)
    }

    /**
     * Handle timeout for other foreground service types.
     */
    protected open fun handleGenericTimeout(startId: Int, fgsType: Int) {
        Timber.w("Foreground service type $fgsType timed out - stopping service")
        onGenericTimeoutAction(fgsType)
        stopSelf(startId)
    }

    /**
     * Called when a call service times out. Override for custom behavior.
     */
    protected open fun onCallServiceTimeoutAction() {
        // Default: log the event for monitoring
        callLogger.captureMessage("Call service timeout occurred - Android 15 enforcement")
    }

    /**
     * Called when a data sync service times out.
     */
    protected open fun onDataSyncTimeoutAction() {
        // Default: no action
    }

    /**
     * Called when a media processing service times out.
     */
    protected open fun onMediaProcessingTimeoutAction() {
        // Default: no action
    }

    /**
     * Called when a generic service times out.
     */
    protected open fun onGenericTimeoutAction(fgsType: Int) {
        // Default: no action
    }
}
