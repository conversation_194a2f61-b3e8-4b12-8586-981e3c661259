package com.gg.gapo.livekit.call.presentation.outgoing.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageButton
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.customview.widget.ViewDragHelper
import androidx.databinding.BindingAdapter
import androidx.transition.*
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.feature.call.R
import com.gg.gapo.feature.call.presentation.utils.requestApplyInsetsWhenAttached
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingStateMachineTransition
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingUISideEffect
import com.gg.gapo.livekit.call.presentation.outgoing.state.CallOutgoingUIState
import com.gg.gapo.livekit.call.presentation.outgoing.viewmodel.CallV2OutgoingViewModel
import timber.log.Timber
import kotlin.math.max
import kotlin.math.min

/**
 * <AUTHOR>
 */
internal class CallOutgoingDraggableLayoutV2 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    internal var callOutgoingViewModel: CallV2OutgoingViewModel? = null

    private val isPipRenderLayoutDragging: Boolean
        get() = draggingState == ViewDragHelper.STATE_DRAGGING || draggingState == ViewDragHelper.STATE_SETTLING

    private val space10dp = context.resources.getDimensionPixelSize(GapoAutoDimens._10dp)
    private val space15dp = context.resources.getDimensionPixelSize(GapoAutoDimens._15dp)
    private val space120dp = context.resources.getDimensionPixelSize(GapoAutoDimens._120dp)
    private val space180dp = context.resources.getDimensionPixelSize(GapoAutoDimens._180dp)

    private lateinit var layoutFullRender: CallOutgoingFullRenderLayout
    private lateinit var layoutPipRender: CallOutgoingPipRenderLayout
    private lateinit var layoutController: CallOutgoingControllerLayout
    private lateinit var buttonArrowDown: AppCompatImageButton
    private lateinit var textInternetConnection: AppCompatTextView
    private lateinit var textPartnerName: AppCompatTextView
    private lateinit var textStatus: AppCompatTextView

    private var preventTouch = false
    private var draggingState = ViewDragHelper.STATE_IDLE
    private var dragHelper: ViewDragHelper? = null

    private var layoutOrientation = Configuration.ORIENTATION_PORTRAIT

    private var isInPIPMode = false

    private var systemWindowInsetTop = 0
    private var systemWindowInsetLeft = 0
    private var systemWindowInsetRight = 0

    private var pipRenderLayoutRadius = 0f
    private var pipRenderLayoutCardElevation = 0f
    private var pipRenderLayoutPreventCornerOverlap = false

    private val dragCallback = object : ViewDragHelper.Callback() {

        override fun onViewDragStateChanged(state: Int) {
            draggingState = state
            if (state == ViewDragHelper.STATE_IDLE) {
                TransitionManager.endTransitions(this@CallOutgoingDraggableLayoutV2)
                TransitionManager.beginDelayedTransition(
                    this@CallOutgoingDraggableLayoutV2,
                    pipRenderLayoutAutoTransition
                )
                reConstraintPipRenderLayout(
                    buttonArrowDown.isVisible,
                    textInternetConnection.isVisible
                )
            }
        }

        override fun tryCaptureView(child: View, pointerId: Int): Boolean {
            val params = child.layoutParams
            return child == layoutPipRender && pointerId == 0 && params.width != ViewGroup.LayoutParams.MATCH_PARENT && params.height != ViewGroup.LayoutParams.MATCH_PARENT
        }

        override fun clampViewPositionHorizontal(child: View, left: Int, dx: Int): Int {
            val rightBound = width - child.width
            return min(max(left, 0), rightBound)
        }

        override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
            val bottomBound = height - child.height
            return min(max(top, 0), bottomBound)
        }
    }

    private val pipRenderLayoutAutoTransition = AutoTransition()
        .apply {
            addListener(object : TransitionListenerAdapter() {
                override fun onTransitionStart(transition: Transition) {
                    preventTouch = true
                }

                override fun onTransitionEnd(transition: Transition) {
                    preventTouch = false
                }
            })
        }

    init {
        setOnApplyWindowInsetsListener { _, insets ->
            if (!isInPIPMode) {
                val shouldReConstraint =
                    systemWindowInsetTop != insets.systemWindowInsetTop ||
                        systemWindowInsetLeft != insets.systemWindowInsetLeft ||
                        systemWindowInsetRight != insets.systemWindowInsetRight

                systemWindowInsetTop = insets.systemWindowInsetTop
                systemWindowInsetLeft = insets.systemWindowInsetLeft
                systemWindowInsetRight = insets.systemWindowInsetRight

                updateInternetConnectionTextParams()

                if (shouldReConstraint) {
                    reConstraintArrowDownButton(textInternetConnection.isVisible)

                    reConstraintPipRenderLayout(
                        buttonArrowDown.isVisible,
                        textInternetConnection.isVisible
                    )
                }
            }
            insets
        }
        requestApplyInsetsWhenAttached()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        layoutFullRender = findViewById(R.id.layout_full_render)
        layoutPipRender = findViewById<CallOutgoingPipRenderLayout>(R.id.layout_pip_render)
            .also {
                pipRenderLayoutRadius = it.radius
                pipRenderLayoutCardElevation = it.cardElevation
                pipRenderLayoutPreventCornerOverlap = it.preventCornerOverlap
            }
        layoutController = findViewById(R.id.layout_controller)
        buttonArrowDown = findViewById(R.id.button_arrow_down)
        textInternetConnection = findViewById(R.id.text_internet_connection)
        textInternetConnection.isVisible = false
        pipRenderLayoutAutoTransition.addTarget(layoutPipRender)
        textPartnerName = findViewById(R.id.text_partner_name)
        textStatus = findViewById(R.id.text_status)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        dragHelper = ViewDragHelper.create(this, 1.0f, dragCallback)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        TransitionManager.endTransitions(this)
    }

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        val dragHelper = dragHelper
        if (preventTouch || dragHelper == null) return false
        return isTargetTouched(event) && dragHelper.shouldInterceptTouchEvent(event)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val dragHelper = dragHelper
        if (preventTouch || dragHelper == null) return false
        return if (isTargetTouched(event) || isPipRenderLayoutDragging) {
            dragHelper.processTouchEvent(event)
            true
        } else super.onTouchEvent(event)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (this.layoutOrientation != newConfig.orientation) {
            this.layoutOrientation = newConfig.orientation
            updatePipRenderLayoutSizeInPIPMode(false)
        }
    }

    override fun computeScroll() {
        if (dragHelper?.continueSettling(true) == true) {
            ViewCompat.postInvalidateOnAnimation(this)
        }
    }

    private fun isTargetTouched(event: MotionEvent): Boolean {
        if (layoutPipRender.measuredWidth == 0) return false
        val location = IntArray(2)
        layoutPipRender.getLocationOnScreen(location)
        val lowerXLimit = location[0]
        val upperXLimit = location[0] + layoutPipRender.measuredWidth
        val lowerYLimit = location[1]
        val upperYLimit = location[1] + layoutPipRender.measuredHeight
        return event.rawX.toInt() in (lowerXLimit + 1) until upperXLimit && event.rawY.toInt() in (lowerYLimit + 1) until upperYLimit
    }

    fun updateUIByState(
        context: Context,
        state: CallOutgoingUIState,
        sideEffect: CallOutgoingUISideEffect?,
        brandingName: String
    ) {
        when {
            state.isStartedState -> {
                textInternetConnection.isVisible = state.isInternetDisconnected
                buttonArrowDown.isVisible = false
                layoutController.isVisible = false
                textPartnerName.isVisible = false
                textStatus.isVisible = false
                layoutFullRender.isVisible = false
                layoutPipRender.isVisible = false
            }
            state.isAudioCall -> {
                changeAudioCallUIState(state, sideEffect)
            }
            state.isVideoCall -> {
                if (state.isInPIPMode) {
                    changeVideoCallPIPModeUIState(state, sideEffect)
                } else {
                    changeVideoCallUIState(state, sideEffect)
                }
            }
            state.isEndedState -> {
                reConstraintFullRenderLayoutInPIPMode(false)
                textInternetConnection.isVisible = false
                buttonArrowDown.isVisible = false
                layoutController.isVisible = false
                textPartnerName.isVisible = !state.isInPIPMode
                textStatus.isVisible = !state.isInPIPMode
                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(true)
                layoutFullRender.setVisibleRendererView(false)
                layoutPipRender.isVisible = false
            }
            else -> {
                if (state == CallOutgoingUIState.CallRated) {
                    textPartnerName.isVisible = false
                    textStatus.isVisible = true
                    textStatus.text = context.getString(GapoStrings.call_outgoing_rating_success)
                        .replaceByBrandingName(brandingName)
                    layoutFullRender.setVisiblePartnerCircleAvatar(false)
                }
            }
        }
    }

    private fun setInPIPMode(isInPIPMode: Boolean) {
        this.isInPIPMode = isInPIPMode
        layoutController.setInPIPMode(isInPIPMode)
    }

    private fun changeAudioCallUIState(
        state: CallOutgoingUIState,
        sideEffect: CallOutgoingUISideEffect?
    ) {
        when (state) {
            CallOutgoingUIState.AudioCall.Initializing,
            CallOutgoingUIState.AudioCall.InitializingInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(state.isInternetDisconnected, true)
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                buttonArrowDown.isVisible = true
                layoutController.isVisible = true
                layoutController.lazyInflateAudioController()
                textPartnerName.isVisible = true
                textStatus.isVisible = true
                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(true)
                layoutFullRender.setVisibleRendererView(false)
                layoutPipRender.isVisible = false
            }
            CallOutgoingUIState.AudioCall.Establishing,
            CallOutgoingUIState.AudioCall.EstablishingInternetDisconnected,
            CallOutgoingUIState.AudioCall.Contacting,
            CallOutgoingUIState.AudioCall.ContactingInternetDisconnected,
            CallOutgoingUIState.AudioCall.Ringing,
            CallOutgoingUIState.AudioCall.RingingInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(state.isInternetDisconnected, true)
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                buttonArrowDown.isVisible = true
                layoutController.isVisible = true
                textPartnerName.isVisible = true
                textStatus.isVisible = true
                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(true)
                layoutFullRender.setVisibleRendererView(false)
                layoutPipRender.isVisible = false
            }
            CallOutgoingUIState.AudioCall.Established,
            CallOutgoingUIState.AudioCall.EstablishedInternetDisconnected,
            CallOutgoingUIState.AudioCall.EstablishedHideController,
            CallOutgoingUIState.AudioCall.EstablishedHideControllerInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(
                        state.isInternetDisconnected,
                        state.isControllerVisible
                    )
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideController) {
                    TransitionManager.endTransitions(this)
                    beginDelayedControllerTransition(
                        state.isControllerVisible,
                        state.isInternetDisconnected
                    )
                }
                buttonArrowDown.isVisible = state.isControllerVisible
                layoutController.isVisible = state.isControllerVisible
                textPartnerName.isVisible = true
                textStatus.isVisible = true
                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(true)
                layoutFullRender.setVisibleRendererView(false)
                layoutPipRender.isVisible = false
            }
            else -> {
                Timber.e("changeAudioCallUIState = $state")
            }
        }
    }

    private fun changeVideoCallUIState(
        state: CallOutgoingUIState,
        sideEffect: CallOutgoingUISideEffect?
    ) {
        when (state) {
            CallOutgoingUIState.VideoCall.Initializing,
            CallOutgoingUIState.VideoCall.InitializingInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(state.isInternetDisconnected, true)
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                buttonArrowDown.isVisible = true
                layoutController.isVisible = true
                layoutController.lazyInflateVideoController()
                textPartnerName.isVisible = true
                textStatus.isVisible = true
                layoutFullRender.isVisible = true
                layoutFullRender.setVisibleRendererView(true)
                layoutFullRender.setVisiblePartnerAvatar(false)
                layoutPipRender.isVisible = false
            }
            CallOutgoingUIState.VideoCall.Establishing,
            CallOutgoingUIState.VideoCall.EstablishingInternetDisconnected,
            CallOutgoingUIState.VideoCall.Contacting,
            CallOutgoingUIState.VideoCall.ContactingInternetDisconnected,
            CallOutgoingUIState.VideoCall.Ringing,
            CallOutgoingUIState.VideoCall.RingingInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(state.isInternetDisconnected, true)
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                buttonArrowDown.isVisible = true
                layoutController.isVisible = true
                textPartnerName.isVisible = true
                textStatus.isVisible = true
                layoutFullRender.isVisible = true
                layoutFullRender.setVisibleRendererView(true)
                layoutFullRender.setVisiblePartnerAvatar(false)
                layoutPipRender.isVisible = false
            }
            CallOutgoingUIState.VideoCall.Established,
            CallOutgoingUIState.VideoCall.EstablishedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedHideController,
            CallOutgoingUIState.VideoCall.EstablishedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPartnerCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedMyCameraClosedPartnerCameraClosedHideControllerInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.ReConstraintRendererViewsPIPMode || isInPIPMode) {
                    TransitionManager.endTransitions(this)
                    setInPIPMode(false)
                    layoutFullRender.changeScaleTypeInPIPMode(false)
                    layoutController.dismissBalloon()
                    layoutPipRender.changeScaleTypeInPIPMode(false)
                    reConstraintFullRenderLayoutInPIPMode(false)
                    reConstraintPipRenderLayoutInPIPMode(
                        isInPIPMode = false,
                        isButtonArrowDownVisible = state.isControllerVisible,
                        isTextInternetConnectionVisible = state.isInternetDisconnected
                    )
                }
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(
                        state.isInternetDisconnected,
                        state.isControllerVisible
                    )
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideController) {
                    TransitionManager.endTransitions(this)
                    beginDelayedControllerTransition(
                        state.isControllerVisible,
                        state.isInternetDisconnected
                    )
                }
                buttonArrowDown.isVisible = state.isControllerVisible
                layoutController.isVisible = state.isControllerVisible
                textPartnerName.isVisible = false
                textStatus.isVisible = false
                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(state.isPartnerCameraClosed)
                layoutFullRender.setVisibleRendererView(!state.isPartnerCameraClosed)

                if (state.isMyCameraClosed) {
                    layoutPipRender.isVisible = false
                } else {
                    layoutPipRender.isVisible = true
                    layoutPipRender.setVisiblePartnerAvatar(false)
                    layoutPipRender.setVisibleRendererView(true)
                }
            }
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnecting,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingPartnerCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectingMyCameraClosedPartnerCameraClosedHideControllerInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.ReConstraintRendererViewsPIPMode || isInPIPMode) {
                    TransitionManager.endTransitions(this)
                    setInPIPMode(false)
                    layoutFullRender.changeScaleTypeInPIPMode(false)
                    layoutController.dismissBalloon()
                    layoutPipRender.changeScaleTypeInPIPMode(false)
                    reConstraintFullRenderLayoutInPIPMode(false)
                    reConstraintPipRenderLayoutInPIPMode(
                        isInPIPMode = false,
                        isButtonArrowDownVisible = state.isControllerVisible,
                        isTextInternetConnectionVisible = state.isInternetDisconnected
                    )
                }

                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(
                        state.isInternetDisconnected,
                        state.isControllerVisible
                    )
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideController) {
                    TransitionManager.endTransitions(this)
                    beginDelayedControllerTransition(
                        state.isControllerVisible,
                        state.isInternetDisconnected
                    )
                }
                buttonArrowDown.isVisible = state.isControllerVisible
                layoutController.isVisible = state.isControllerVisible
                textPartnerName.isVisible = true
                textStatus.isVisible = true

                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(state.isPartnerCameraClosed)
                layoutFullRender.setVisibleRendererView(!state.isPartnerCameraClosed)

                if (state.isMyCameraClosed) {
                    layoutPipRender.isVisible = false
                } else {
                    layoutPipRender.isVisible = true
                    layoutPipRender.setVisiblePartnerAvatar(false)
                    layoutPipRender.setVisibleRendererView(true)
                }
            }
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedPartnerCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerConnectedMyCameraClosedPartnerCameraClosedHideControllerInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.ReConstraintRendererViewsPIPMode || isInPIPMode) {
                    TransitionManager.endTransitions(this)
                    setInPIPMode(false)
                    layoutFullRender.changeScaleTypeInPIPMode(false)
                    layoutController.dismissBalloon()
                    layoutPipRender.changeScaleTypeInPIPMode(false)
                    reConstraintFullRenderLayoutInPIPMode(false)
                    reConstraintPipRenderLayoutInPIPMode(
                        isInPIPMode = false,
                        isButtonArrowDownVisible = state.isControllerVisible,
                        isTextInternetConnectionVisible = state.isInternetDisconnected
                    )
                }

                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(
                        state.isInternetDisconnected,
                        state.isControllerVisible
                    )
                }
                textInternetConnection.isVisible = state.isInternetDisconnected

                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideController) {
                    TransitionManager.endTransitions(this)
                    beginDelayedControllerTransition(
                        state.isControllerVisible,
                        state.isInternetDisconnected
                    )
                }
                buttonArrowDown.isVisible = state.isControllerVisible
                layoutController.isVisible = state.isControllerVisible

                textPartnerName.isVisible =
                    state.isPartnerCameraClosed || state.isRTCPeerDisconnected
                textStatus.isVisible = state.isPartnerCameraClosed || state.isRTCPeerDisconnected

                if (state.isPartnerCameraClosed && state.isRTCPeerDisconnected) {
                    textStatus.setText(GapoStrings.call_outgoing_video_connecting)
                } else if (state.isPartnerCameraClosed) {
                    textStatus.setText(GapoStrings.call_outgoing_partner_camera_turn_off)
                } else if (state.isRTCPeerDisconnected) {
                    textStatus.setText(GapoStrings.call_outgoing_video_connecting)
                }

                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(state.isPartnerCameraClosed || state.isRTCPeerDisconnected)
                layoutFullRender.setVisibleRendererView(!state.isPartnerCameraClosed && !state.isRTCPeerDisconnected)

                if (state.isMyCameraClosed) {
                    layoutPipRender.isVisible = false
                } else {
                    layoutPipRender.isVisible = true
                    layoutPipRender.setVisiblePartnerAvatar(false)
                    layoutPipRender.setVisibleRendererView(true)
                }
            }
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedPartnerCameraClosedHideControllerInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedPartnerCameraClosedHideController,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedRTCPeerDisconnectedMyCameraClosedPartnerCameraClosedHideControllerInternetDisconnected
            -> {
                if (sideEffect is CallOutgoingUISideEffect.ReConstraintRendererViewsPIPMode || isInPIPMode) {
                    TransitionManager.endTransitions(this)
                    setInPIPMode(false)
                    layoutFullRender.changeScaleTypeInPIPMode(false)
                    layoutController.dismissBalloon()
                    layoutPipRender.changeScaleTypeInPIPMode(false)
                    reConstraintFullRenderLayoutInPIPMode(false)
                    reConstraintPipRenderLayoutInPIPMode(
                        isInPIPMode = false,
                        isButtonArrowDownVisible = state.isControllerVisible,
                        isTextInternetConnectionVisible = state.isInternetDisconnected
                    )
                }

                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideInternetStatus) {
                    beginDelayedInternetStatusTransition(
                        state.isInternetDisconnected,
                        state.isControllerVisible
                    )
                }
                textInternetConnection.isVisible = state.isInternetDisconnected
                if (sideEffect is CallOutgoingUISideEffect.AnimateShowHideController) {
                    TransitionManager.endTransitions(this)
                    beginDelayedControllerTransition(
                        state.isControllerVisible,
                        state.isInternetDisconnected
                    )
                }
                buttonArrowDown.isVisible = state.isControllerVisible
                layoutController.isVisible = state.isControllerVisible

                textPartnerName.isVisible = true
                textStatus.isVisible = true

                if (state.isPartnerCameraClosed && state.isRTCPeerDisconnected) {
                    textStatus.setText(GapoStrings.call_outgoing_video_connecting)
                } else if (state.isPartnerCameraClosed) {
                    textStatus.setText(GapoStrings.call_outgoing_partner_camera_turn_off)
                } else if (state.isRTCPeerDisconnected) {
                    textStatus.setText(GapoStrings.call_outgoing_video_connecting)
                }

                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(true)
                layoutFullRender.setVisibleRendererView(false)

                if (state.isMyCameraClosed) {
                    layoutPipRender.isVisible = false
                } else {
                    layoutPipRender.isVisible = true
                    layoutPipRender.setVisiblePartnerAvatar(false)
                    layoutPipRender.setVisibleRendererView(true)
                }
            }
            else -> {
                Timber.e("changeVideoCallUIState = $state")
            }
        }
    }

    private fun changeVideoCallPIPModeUIState(
        state: CallOutgoingUIState,
        sideEffect: CallOutgoingUISideEffect?
    ) {
        when (state) {
            CallOutgoingUIState.VideoCall.InitializingPIPMode,
            CallOutgoingUIState.VideoCall.InitializingPIPModeInternetDisconnected,
            CallOutgoingUIState.VideoCall.ConnectingPIPMode,
            CallOutgoingUIState.VideoCall.ConnectingPIPModeInternetDisconnected,
            CallOutgoingUIState.VideoCall.ContactingPIPMode,
            CallOutgoingUIState.VideoCall.ContactingPIPModeInternetDisconnected,
            CallOutgoingUIState.VideoCall.RingingPIPMode,
            CallOutgoingUIState.VideoCall.RingingPIPModeInternetDisconnected
            -> {
                TransitionManager.endTransitions(this)
                textInternetConnection.isVisible = false
                buttonArrowDown.isVisible = false
                layoutController.isVisible = false
                layoutController.lazyInflateVideoController()
                textPartnerName.isVisible = false
                textStatus.isVisible = false
                layoutFullRender.isVisible = true
                layoutFullRender.setVisibleRendererView(true)
                layoutFullRender.setVisiblePartnerAvatar(false)
                layoutPipRender.isVisible = false
            }
            CallOutgoingUIState.VideoCall.EstablishedPIPMode,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModePartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModePartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnecting,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectingMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerConnectedMyCameraClosedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedMyCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedMyCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedPartnerCameraClosedInternetDisconnected,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedMyCameraClosedPartnerCameraClosed,
            CallOutgoingUIState.VideoCall.EstablishedPIPModeRTCPeerDisconnectedMyCameraClosedPartnerCameraClosedInternetDisconnected
            -> {
                textInternetConnection.isVisible = false
                buttonArrowDown.isVisible = false
                layoutController.isVisible = false
                textPartnerName.isVisible = false
                textStatus.isVisible = false

                if (sideEffect is CallOutgoingUISideEffect.ReConstraintRendererViewsPIPMode) {
                    TransitionManager.endTransitions(this)
                    setInPIPMode(true)
                    layoutFullRender.changeScaleTypeInPIPMode(true)
                    layoutController.dismissBalloon()
                    layoutPipRender.changeScaleTypeInPIPMode(true)
                    reConstraintFullRenderLayoutInPIPMode(true)
                    reConstraintPipRenderLayoutInPIPMode(
                        isInPIPMode = true,
                        isButtonArrowDownVisible = false,
                        isTextInternetConnectionVisible = false
                    )
                }

                layoutFullRender.isVisible = true
                layoutFullRender.setVisiblePartnerAvatar(state.isPartnerCameraClosed)
                layoutFullRender.setVisibleRendererView(!state.isPartnerCameraClosed)

                layoutPipRender.isVisible = true
                layoutPipRender.setVisiblePartnerAvatar(state.isMyCameraClosed || state.isRTCPeerDisconnected)
                layoutPipRender.setVisibleRendererView(!state.isMyCameraClosed && !state.isRTCPeerDisconnected)
            }
            else -> {
                Timber.e("changeVideoCallPIPModeUIState = $state")
            }
        }
    }

    private fun beginDelayedInternetStatusTransition(
        isVisible: Boolean,
        isButtonArrowDownVisible: Boolean
    ) {
        val internetConnectionTextFade = Fade(if (isVisible) Fade.IN else Fade.OUT)
        val internetConnectionTextSlide = Slide(if (isVisible) Gravity.START else Gravity.END)

        val internetConnectionTransitionTextSet = TransitionSet()
            .apply {
                ordering = TransitionSet.ORDERING_TOGETHER
                addTransition(internetConnectionTextFade)
                addTransition(internetConnectionTextSlide)
                addTarget(textInternetConnection)
            }

        val arrowDownButtonAuto = AutoTransition()
            .apply {
                addTarget(buttonArrowDown)
            }
        val transitionSet = TransitionSet()
            .apply {
                ordering = TransitionSet.ORDERING_TOGETHER
                addTransition(internetConnectionTransitionTextSet)
                addTransition(arrowDownButtonAuto)
                addTransition(pipRenderLayoutAutoTransition)
            }

        TransitionManager.beginDelayedTransition(this, transitionSet)

        reConstraintArrowDownButton(isVisible)
        reConstraintPipRenderLayout(isButtonArrowDownVisible, isVisible)
    }

    private fun beginDelayedControllerTransition(
        isVisible: Boolean,
        isTextInternetConnectionVisible: Boolean
    ) {
        val arrowDownButtonTransitionSet = TransitionSet()
            .apply {
                ordering = TransitionSet.ORDERING_TOGETHER
                addTransition(Slide(Gravity.TOP))
                if (isTextInternetConnectionVisible) {
                    addTransition(AutoTransition())
                }
                addTarget(buttonArrowDown)
            }

        val controllerLayoutSlide = Slide(Gravity.BOTTOM)
            .apply {
                addTarget(layoutController)
            }

        val transitionSet = TransitionSet()
            .apply {
                ordering = TransitionSet.ORDERING_TOGETHER
                addTransition(arrowDownButtonTransitionSet)
                addTransition(controllerLayoutSlide)
                addTransition(pipRenderLayoutAutoTransition)
            }

        TransitionManager.beginDelayedTransition(this, transitionSet)

        reConstraintArrowDownButton(isTextInternetConnectionVisible)
        reConstraintPipRenderLayout(isVisible, isTextInternetConnectionVisible)
    }

    private fun reConstraintArrowDownButton(isTextInternetConnectionVisible: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(this)

        constraintSet.clearConstraints(buttonArrowDown.id)

        constraintSet.connect(
            buttonArrowDown.id,
            ConstraintSet.TOP,
            if (isTextInternetConnectionVisible) textInternetConnection.id else ConstraintSet.PARENT_ID,
            if (isTextInternetConnectionVisible) ConstraintSet.BOTTOM else ConstraintSet.TOP,
            if (isTextInternetConnectionVisible) space15dp else space15dp + systemWindowInsetTop
        )

        constraintSet.connect(
            buttonArrowDown.id,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START,
            space15dp + systemWindowInsetLeft
        )

        constraintSet.applyTo(this)
    }

    private fun reConstraintFullRenderLayoutInPIPMode(isInPIPMode: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(this)
        constraintSet.clearConstraints(layoutFullRender.id)
        if (layoutOrientation == Configuration.ORIENTATION_PORTRAIT) {
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.TOP,
                if (isInPIPMode) R.id.guide_line_50percent_horizontal else ConstraintSet.PARENT_ID,
                if (isInPIPMode) ConstraintSet.BOTTOM else ConstraintSet.TOP
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID,
                ConstraintSet.BOTTOM
            )
        } else {
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.START,
                if (isInPIPMode) R.id.guide_line_50percent_vertical else ConstraintSet.PARENT_ID,
                if (isInPIPMode) ConstraintSet.END else ConstraintSet.START
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END
            )
            constraintSet.connect(
                layoutFullRender.id,
                ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID,
                ConstraintSet.BOTTOM
            )
        }

        constraintSet.applyTo(this)
    }

    private fun reConstraintPipRenderLayoutInPIPMode(
        isInPIPMode: Boolean,
        isButtonArrowDownVisible: Boolean,
        isTextInternetConnectionVisible: Boolean
    ) {
        if (isInPIPMode) {
            val constraintSet = ConstraintSet()
            constraintSet.clone(this)
            constraintSet.clearConstraints(layoutPipRender.id)
            val isPortrait = layoutOrientation == Configuration.ORIENTATION_PORTRAIT
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP
            )
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START
            )

            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.END,
                if (isPortrait) ConstraintSet.PARENT_ID else R.id.guide_line_50percent_vertical,
                if (isPortrait) ConstraintSet.END else ConstraintSet.START
            )

            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.BOTTOM,
                if (isPortrait) R.id.guide_line_50percent_horizontal else ConstraintSet.PARENT_ID,
                if (isPortrait) ConstraintSet.TOP else ConstraintSet.BOTTOM
            )
            constraintSet.applyTo(this)
        } else {
            reConstraintPipRenderLayout(isButtonArrowDownVisible, isTextInternetConnectionVisible)
        }

        updatePipRenderLayoutSizeInPIPMode(isInPIPMode)
        updatePipRenderPropertiesInPIPMode(isInPIPMode)
    }

    private fun reConstraintPipRenderLayout(
        isButtonArrowDownVisible: Boolean,
        isTextInternetConnectionVisible: Boolean
    ) {
        val constraintSet = ConstraintSet()
        constraintSet.clone(this)

        constraintSet.clearConstraints(layoutPipRender.id)

        val halfParentWidth = width / 2
        val halfParentHeight = height / 2

        val childTop = layoutPipRender.top
        val childLeft = layoutPipRender.left

        val halfChildWidth = layoutPipRender.width / 2
        val halfChildHeight = layoutPipRender.height / 2

        if (halfParentHeight - childTop > halfChildHeight) {
            // Nửa trên
            val endTopId: Int
            val endTopSide: Int
            val marginTop: Int
            when {
                isButtonArrowDownVisible -> {
                    endTopId = buttonArrowDown.id
                    endTopSide = ConstraintSet.BOTTOM
                    marginTop = space10dp
                }
                isTextInternetConnectionVisible -> {
                    endTopId = textInternetConnection.id
                    endTopSide = ConstraintSet.BOTTOM
                    marginTop = space10dp
                }
                else -> {
                    endTopId = ConstraintSet.PARENT_ID
                    endTopSide = ConstraintSet.TOP
                    marginTop = space10dp + systemWindowInsetTop
                }
            }
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.TOP,
                endTopId,
                endTopSide,
                marginTop
            )
        } else {
            // Nửa dưới
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.BOTTOM,
                R.id.barrier_controller,
                ConstraintSet.TOP,
                space15dp
            )
        }

        if (halfParentWidth - childLeft > halfChildWidth) {
            // Bên trái
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START,
                space15dp + systemWindowInsetLeft
            )
        } else {
            // Bên phải
            constraintSet.connect(
                layoutPipRender.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END,
                space15dp + systemWindowInsetRight
            )
        }

        constraintSet.applyTo(this)
    }

    private fun updatePipRenderLayoutSizeInPIPMode(isInPIPMode: Boolean) {
        layoutPipRender.updateLayoutParams<LayoutParams> {
            this.width = when {
                isInPIPMode -> 0
                layoutOrientation == Configuration.ORIENTATION_PORTRAIT -> space120dp
                else -> space180dp
            }
            this.height = if (isInPIPMode) 0 else LayoutParams.WRAP_CONTENT
        }
    }

    private fun updatePipRenderPropertiesInPIPMode(isInPIPMode: Boolean) {
        layoutPipRender.radius = if (isInPIPMode) 0f else pipRenderLayoutRadius
        layoutPipRender.preventCornerOverlap =
            if (isInPIPMode) false else pipRenderLayoutPreventCornerOverlap
    }

    private fun updateInternetConnectionTextParams() {
        textInternetConnection.updateLayoutParams<LayoutParams> {
            topMargin = systemWindowInsetTop
        }
    }

    private fun ConstraintSet.clearConstraints(id: Int) {
        clear(id, ConstraintSet.TOP)
        clear(id, ConstraintSet.START)
        clear(id, ConstraintSet.END)
        clear(id, ConstraintSet.BOTTOM)
    }
}

@BindingAdapter(value = ["callOutgoingViewModel"])
internal fun CallOutgoingDraggableLayoutV2.setCallOutgoingViewModelDataBinding(
    callOutgoingViewModel: CallV2OutgoingViewModel?
) {
    this.callOutgoingViewModel = callOutgoingViewModel
}

internal fun CallOutgoingDraggableLayoutV2.setStateMachineTransitionDataBinding(
    transition: CallOutgoingStateMachineTransition?,
    brandingName: String
) {
    if (transition != null) {
        updateUIByState(this.context, transition.toState, transition.sideEffect, brandingName)
    }
}
