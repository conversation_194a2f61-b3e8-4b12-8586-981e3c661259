package com.gg.gapo.livekit.call.presentation.logger

import android.os.Bundle
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.feature.call.R

@AppDeepLink(value = ["call_v2/logger"])
class CallLoggerActivity : GapoThemeBaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.call_logger_activity)
    }
}
