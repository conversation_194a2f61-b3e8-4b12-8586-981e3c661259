package com.gg.gapo.livekit.call.presentation.incoming

import android.annotation.SuppressLint
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.navigation.deeplink.GapoDeepLink
import com.gg.gapo.core.navigation.deeplink.call.CallGroupOutGoingDeepLink
import com.gg.gapo.core.navigation.deeplink.call.CallV2OutGoingDeepLink
import com.gg.gapo.core.navigation.deeplink.navByDeepLink
import com.gg.gapo.core.navigation.system.openAppSystemSettings
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.feature.call.R
import com.gg.gapo.feature.call.databinding.CallV2IncomingActivityBinding
import com.gg.gapo.feature.call.presentation.utils.*
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_KEYGUARD_LOCK
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_PARTNER_EXTRA
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_TYPE_EXTRA
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_WAKE_LOCK
import com.gg.gapo.livekit.call.common.event.CallStopRingingBusEventV2
import com.gg.gapo.livekit.call.domain.model.LocalCallType
import com.gg.gapo.livekit.call.domain.model.ParticipantModel
import com.gg.gapo.livekit.call.logger.CallLoggerV2
import com.gg.gapo.livekit.call.presentation.incoming.viewmodel.CallV2IncomingViewModel
import com.gg.gapo.livekit.call.service.CallReceiverV2
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

@SuppressLint("InvalidWakeLockTag")
internal class CallV2IncomingActivity : GapoThemeBaseActivity() {

    private val callLogger by inject<CallLoggerV2>()

    private val powerManager by lazy(LazyThreadSafetyMode.NONE) {
        getSystemService<PowerManager>()
    }

    private val wakeUpWakeLock by lazy(LazyThreadSafetyMode.NONE) {
        powerManager?.newWakeLock(
            PowerManager.FULL_WAKE_LOCK or PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            CALL_WAKE_LOCK
        )
    }

    private val keyguardManager by lazy(LazyThreadSafetyMode.NONE) {
        getSystemService<KeyguardManager>()
    }

    private val keyguardLock by lazy(LazyThreadSafetyMode.NONE) {
        keyguardManager?.newKeyguardLock(CALL_KEYGUARD_LOCK)
    }

    private val callIncomingViewModel by viewModel<CallV2IncomingViewModel>()

    @RequiresApi(Build.VERSION_CODES.M)
    private val callPermissions =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.isNotEmpty() && result.values.all { it }) {
                answerCall()
            } else {
                if (shouldShowRequestPermissionRationale(result)) {
                    showAskPermissionAlertDialog()
                } else {
                    showAskPermissionManualAlertDialog()
                }
            }
        }

    private val askPermissionTitleRes: Int
        get() = if (callIncomingViewModel.callType.isVideoCall) {
            GapoStrings.call_video_call_on_gapo_all
        } else {
            GapoStrings.call_audio_call_on_gapo_all
        }

    private lateinit var binding: CallV2IncomingActivityBinding

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        callIncomingViewModel.parseIntent(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setupWindow()
        acquireWakeLookAndDisableKeyguard(
            keyguardManager,
            powerManager,
            wakeUpWakeLock,
            keyguardLock,
            ACQUIRE_WAKE_LOCK_TIME_OUT_IN_MILLIS
        )
        super.onCreate(savedInstanceState)
        binding = CallV2IncomingActivityBinding.inflate(LayoutInflater.from(this)).apply {
            lifecycleOwner = this@CallV2IncomingActivity
            glideRequests = GapoGlide.with(this@CallV2IncomingActivity)
            callIncomingViewModel = <EMAIL>
        }

        setContentView(binding.root)

        registerEventBus()

        callIncomingViewModel.parseIntent(intent)

        callIncomingViewModel.onClickArrowDownButtonEventLiveData.observe(
            this,
            EventObserver {
                onBackPressed()
            }
        )

        callIncomingViewModel.onClickDeclineButtonEventLiveData.observe(
            this,
            EventObserver {
                declineCall()
            }
        )

        callIncomingViewModel.onClickAnswerButtonEventLiveData.observe(
            this,
            EventObserver {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    val callPermission = callIncomingViewModel.callPermissions
                    callPermissions.launch(callPermission)
                } else {
                    answerCall()
                }
            }
        )
    }

    override fun onDestroy() {
        releaseWakeLookAndReenableKeyguard(
            keyguardManager,
            powerManager,
            wakeUpWakeLock,
            keyguardLock
        )
        super.onDestroy()
        unregisterEventBus()
    }

    /**
     * Stop ringing khi partner đang ở màn hình incoming
     * Hoặc Stop ringing các device khác khi 1 device đã answer
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    internal fun onStopRinging(event: CallStopRingingBusEventV2) {
        finishAndRemoveTask()
    }

    private fun setupWindow() {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
    }

    private fun declineCall() {
        sendBroadcast(CallReceiverV2.newDeclineIntent(this))
        finishAndRemoveTask()
    }

    private fun answerCall() {
        sendBroadcast(CallReceiverV2.newAnswerIntent(this))
        if (callIncomingViewModel.callType == LocalCallType.Answer.GroupChat) {
            if (callIncomingViewModel.isRoomInitialized()) {
                navByDeepLink(
                    CallGroupOutGoingDeepLink(
                        CallGroupOutGoingDeepLink.Type.ANSWER_GROUP_CHAT,
                        callIncomingViewModel.room.associateId,
                        GapoDeepLink.Options(
                            bundle = CallGroupOutGoingDeepLink.createBundle(
                                threadName = callIncomingViewModel.room.name,
                                threadAvatar = callIncomingViewModel.room.avatar,
                                threadId = callIncomingViewModel.room.associateId,
                                userIds = listOf(),
                                roomId = callIncomingViewModel.room.id
                            ),
                            flags = listOf(
                                Intent.FLAG_ACTIVITY_NO_ANIMATION to false,
                                Intent.FLAG_ACTIVITY_NEW_TASK to false
                            )
                        )
                    )
                )
            } else {
                callIncomingViewModel.getRoomInfo(true)
            }
        } else {
            navByDeepLink(
                CallV2OutGoingDeepLink(
                    LocalCallType.getType(callIncomingViewModel.callType),
                    callIncomingViewModel.partner.userId.orEmpty(),
                    GapoDeepLink.Options(
                        bundle = CallV2OutGoingDeepLink.createBundle(
                            callIncomingViewModel.partner.name.orEmpty(),
                            callIncomingViewModel.partner.avatar,
                            callIncomingViewModel.partner.avatar
                        ),
                        flags = listOf(
                            Intent.FLAG_ACTIVITY_NO_ANIMATION to false,
                            Intent.FLAG_ACTIVITY_NEW_TASK to false
                        )
                    )
                )
            )
        }
        lifecycleScope.launch {
            if (isTaskRoot) {
                delay(1_000)
            }
            finishAndRemoveTask()
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun showAskPermissionAlertDialog() {
        AlertDialog.Builder(this, R.style.AskPermissionAlertDialogTheme)
            .setTitle(getString(askPermissionTitleRes).replaceByBrandingName(callIncomingViewModel.brandingName))
            .setMessage(GapoStrings.call_incoming_ask_permission_video_call_message)
            .setNegativeButton(GapoStrings.shared_reject) { dialog, _ ->
                dialog.dismiss()
            }.setPositiveButton(GapoStrings.shared_allow) { dialog, _ ->
                dialog.dismiss()
                callPermissions.launch(callIncomingViewModel.callPermissions)
            }.setCancelable(false).show()
    }

    private fun showAskPermissionManualAlertDialog() {
        AlertDialog.Builder(this, R.style.AskPermissionAlertDialogTheme)
            .setTitle(getString(askPermissionTitleRes).replaceByBrandingName(callIncomingViewModel.brandingName))
            .setMessage(GapoStrings.call_incoming_ask_permission_manual_video_call_message)
            .setNegativeButton(GapoStrings.permission_later) { dialog, _ ->
                dialog.dismiss()
            }
            .setPositiveButton(GapoStrings.permission_app_settings) { dialog, _ ->
                dialog.dismiss()
                openAppSystemSettings()
            }.setCancelable(false).show()
    }

    companion object {
        private const val ACQUIRE_WAKE_LOCK_TIME_OUT_IN_MILLIS = 60_000L

        internal const val ACCEPT_CALL_EXTRA = "ACCEPT_CALL_EXTRA"

        internal fun newIntent(
            context: Context,
            partner: ParticipantModel,
            callType: LocalCallType,
            acceptCall: Boolean = false
        ) = Intent(context, CallV2IncomingActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            val bundle = bundleOf(
                CALL_PARTNER_EXTRA to partner,
                CALL_TYPE_EXTRA to callType,
                ACCEPT_CALL_EXTRA to acceptCall
            )
            putExtras(bundle)
        }
    }
}
