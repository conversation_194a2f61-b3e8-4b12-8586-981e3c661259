package com.gg.gapo.livekit.call.service.outgoing

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.media.AudioManager
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.gg.gapo.core.eventbus.authentication.LogoutForceStopCallBusEvent
import com.gg.gapo.core.eventbus.call.CallStickyBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.eventbus.postStickyEvent
import com.gg.gapo.core.eventbus.registerEventBus
import com.gg.gapo.core.eventbus.removeStickyEvent
import com.gg.gapo.core.eventbus.unregisterEventBus
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.core.utilities.bundle.parcelable
import com.gg.gapo.feature.call.connection.InternetConnectionDetector
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_PARTNER_EXTRA
import com.gg.gapo.feature.call.presentation.utils.CallConstants.CALL_TYPE_EXTRA
import com.gg.gapo.feature.call.service.outgoing.state.CallOutgoingCallState
import com.gg.gapo.feature.call.webrtc.audio.RTCAudioManager
import com.gg.gapo.livekit.call.common.event.CallAcceptedBusEventV2
import com.gg.gapo.livekit.call.common.event.CallBusyBusEventV2
import com.gg.gapo.livekit.call.common.event.CallDestroySessionBusEventV2
import com.gg.gapo.livekit.call.common.event.CallDisconnectedBusEventV2
import com.gg.gapo.livekit.call.common.event.CallDisconnectedSoundBusEventV2
import com.gg.gapo.livekit.call.common.event.CallEndedBusEventV2
import com.gg.gapo.livekit.call.common.event.CallHangupCallBusEventV2
import com.gg.gapo.livekit.call.common.event.CallNoAnswerCallBusEventV2
import com.gg.gapo.livekit.call.common.event.CallPartnerDeclineIncomingCallBusEventV2
import com.gg.gapo.livekit.call.common.event.CallPartnerRingingBusEventV2
import com.gg.gapo.livekit.call.common.event.CallRestApiErrorWhileEstablishBusEventV2
import com.gg.gapo.livekit.call.common.proximitysensor.ProximitySensor
import com.gg.gapo.livekit.call.common.proximitysensor.ProximitySensorImpl
import com.gg.gapo.livekit.call.domain.model.CancelCallReason
import com.gg.gapo.livekit.call.domain.model.EndCallReason
import com.gg.gapo.livekit.call.domain.model.LocalCallType
import com.gg.gapo.livekit.call.domain.model.ParticipantModel
import com.gg.gapo.livekit.call.domain.model.RemoteCallType
import com.gg.gapo.livekit.call.notification.CallNotifierV2
import com.gg.gapo.livekit.call.presentation.outgoing.CallV2OutgoingActivity.Companion.getCallOutgoingIntent
import com.gg.gapo.livekit.call.service.CallV2BaseService
import com.gg.gapo.livekit.call.service.player.CallDisconnectedSoundPlayer
import com.gg.gapo.livekit.call.service.player.CallOutgoingDialingPlayer
import com.gg.gapo.livekit.call.service.player.CallOutgoingRingingPlayer
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.webrtc.EglBase
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.Timer
import java.util.TimerTask
import java.util.UUID
import kotlin.concurrent.timerTask

/**
 * <AUTHOR>
 */
internal class CallV2OutgoingService :
    CallV2BaseService(),
    InternetConnectionDetector.Callback,
    RTCAudioManager.Callback {

    private val binder = CallV2OutgoingServiceBinder(WeakReference(this))

    private val onPremiseManager: OnPremiseManager by inject<OnPremiseManager>()

    private val internetConnectionDetector by lazy(LazyThreadSafetyMode.NONE) {
        InternetConnectionDetector(this, this)
    }

    private val eglBase = EglBase.create(null, EglBase.CONFIG_RGBA)

    private val proximitySensor: ProximitySensor by lazy {
        ProximitySensorImpl(
            applicationContext,
            object : ProximitySensor.Callback {
                override fun onProximitySensorChangedState(isSensorNearState: Boolean) {
                    if (
                        /*callType.isAudioCall && */
                        !isExternalSpeakerOpened
                    ) {
                        if (isSensorNearState) {
                            ProximitySensor.Distance.NEAR.postEvent()
                        } else {
                            ProximitySensor.Distance.FAR.postEvent()
                        }
                    }
                }
            }
        )
    }

    val eglBaseContext: EglBase.Context
        get() = eglBase.eglBaseContext

    val isLivekitConnectedLiveData: LiveData<Boolean>
        get() = callCenter.isLivekitConnectedLiveData

    val livekitToken: String
        get() = callCenter.livekitToken

    private val callState: CallOutgoingCallState
        get() = _callStateLiveData.value ?: CallOutgoingCallState.Establishing.Connecting
    val callStateLiveData: LiveData<CallOutgoingCallState>
        get() = _callStateLiveData
    private val _callStateLiveData = MutableLiveData<CallOutgoingCallState>()

    val partnerProfileLiveData: LiveData<ParticipantModel>
        get() = _partnerProfileLiveData
    private val _partnerProfileLiveData = MutableLiveData<ParticipantModel>()

    val isInternetConnectedLiveData: LiveData<Boolean>
        get() = _isInternetConnectedLiveData
    private val _isInternetConnectedLiveData = MutableLiveData<Boolean>()

    private val outgoingActivityIntent: Intent
        get() = getCallOutgoingIntent(partner, callType)

    private var callChronometerExecutor: Job? = null
    private var callDuration = -1L

    private val isIntentParsed: Boolean
        get() = ::partner.isInitialized && ::callType.isInitialized

    private lateinit var partner: ParticipantModel
    private lateinit var callType: LocalCallType

    private var outgoingTimeoutTimer: Timer? = null
    private var outgoingTimeoutTimerTask: TimerTask? = null

    private var enteringTimeoutTimer: Timer? = null
    private var enteringTimeoutTimerTask: TimerTask? = null

    private var disconnectedTimeoutTimer: Timer? = null
    private var disconnectedTimeoutTimerTask: TimerTask? = null

    private var internetConnectivityTimer: Timer? = null
    private var internetConnectivityTimerTask: TimerTask? = null

    private var dialingSoundPlayer: CallOutgoingDialingPlayer? = null
    private var isExternalSpeakerOpened = false

    private val ringingSoundPlayer by lazy(LazyThreadSafetyMode.NONE) {
        CallOutgoingRingingPlayer(this.applicationContext)
    }

    private val disconnectedSoundPlayer by lazy(LazyThreadSafetyMode.NONE) {
        CallDisconnectedSoundPlayer(this.applicationContext)
    }

    /**
     * pause video trong 2 trường hợp
     * 1. khi activity bị destroy
     * 2. khi exit PIP Mode trên android 10 trở lên
     */

    var onEntering = false
    private var shouldIgnoreDuplicateAcceptEvent = false

    private var isVideoSourcePaused = false
    private val clientId = UUID.randomUUID()
    override fun onCreate() {
        super.onCreate()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            startForeground(
                OUTGOING_NOTIFICATION_ID,
                CallNotifierV2.createOutgoingStartUpNotification(this),
                ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
            )
        } else {
            startForeground(
                OUTGOING_NOTIFICATION_ID,
                CallNotifierV2.createOutgoingStartUpNotification(this)
            )
        }
        _callStateLiveData.value = CallOutgoingCallState.Establishing.Connecting

        internetConnectionDetector.registerNetworkCallback()

        startOutgoingTimeoutTimer()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        registerEventBus()
        when (intent?.action) {
            START_OUTGOING_SERVICE_ACTION -> {
                val extras = intent.extras
                if (extras != null && !isIntentParsed) {
                    val partner = extras.parcelable<ParticipantModel>(CALL_PARTNER_EXTRA)
                        ?: throw IllegalStateException()
                    updatePartnerProfile(partner)

                    callType =
                        extras.parcelable(CALL_TYPE_EXTRA) ?: throw IllegalStateException()
                    callCenter.setUpParams(callType)
                    callLogger.setPartnerId(partner.userId.orEmpty())
                    if (callType.isOffer) {
                        val remoteOfferCallType = when {
                            callType.isAudioCall -> RemoteCallType.AUDIO
                            callType.isVideoCall -> RemoteCallType.VIDEO
                            else -> RemoteCallType.GROUP_CHAT
                        }
                        callCenter.makeCallToPartner(
                            partnerId = partner.userId.orEmpty(),
                            remoteOfferCallType = remoteOfferCallType
                        )

                        dialingSoundPlayer = CallOutgoingDialingPlayer(this.applicationContext)
                            .apply {
                                playDialingSound()
                            }
                        callLogger.setCallType("offer")
                    }

                    val contactingNotification = if (callType.isOffer) {
                        _callStateLiveData.value = CallOutgoingCallState.Establishing.Contacting
                        CallNotifierV2.createOutgoingContactingPartnerNotification(
                            this,
                            partner,
                            callType
                        )
                    } else {
                        CallNotifierV2.createOutgoingConnectingServerNotification(
                            this,
                            partner,
                            callType,
                            onPremiseManager.onPremiseConfigData?.brandName.orEmpty()
                        )
                    }

                    if (ContextCompat.checkSelfPermission(
                            this,
                            Manifest.permission.POST_NOTIFICATIONS
                        ) == PackageManager.PERMISSION_GRANTED
                    ) {
                        NotificationManagerCompat.from(this.applicationContext).apply {
                            notify(OUTGOING_NOTIFICATION_ID, contactingNotification)
                        }
                    }

                    if (callType.isOffer) {
                        callLogger.captureMessage("Call service start: call_type = $callType")
                        startOutgoingTimeoutTimer()
                    } else {
                        val callId = getLastCallIdUseCase(partner.userId.orEmpty())
                        callLogger.captureMessage("Call service start: call_type = $callType")
                    }

                    if (callState.isEstablishing) {
                        Timber.d("onStartCommand postExternalOutgoingBusEvent")
                        postExternalOutgoingBusEvent(outgoingActivityIntent, -1)
                    }

                    proximitySensor.start()
                }
            }

            STOP_OUTGOING_SERVICE_ACTION -> {
                callLogger.captureHangup("Hangup do bản thân chủ động")
                callEnded(CallOutgoingCallState.Ended.ForceHangup)
            }
        }
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }

    override fun onConnectivityChanged(isConnected: Boolean) {
        _isInternetConnectedLiveData.postValue(isConnected)
        if (isConnected) {
            stopInternetConnectivityTimer()
        } else {
            startInternetConnectivityTimer()
        }
    }

    override fun onAudioFocusChange(focusChange: Int) {
        Timber.e("onAudioFocusChange = $focusChange")
        if (focusChange == AudioManager.AUDIOFOCUS_LOSS || focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT || focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
            callLogger.captureMessage("Audio Focus Lost : AUDIOFOCUS_LOSS || AUDIOFOCUS_LOSS_TRANSIENT || AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK")
        }
    }

    override fun onAudioDeviceChanged(
        selectedAudioDevice: RTCAudioManager.AudioDevice,
        availableAudioDevices: Set<RTCAudioManager.AudioDevice>
    ) {
        Timber.e("onAudioDeviceChanged: $selectedAudioDevice")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun callV2AcceptedBusEvent(event: CallAcceptedBusEventV2) {
        callLogger.captureMessage("CallEstablished : roomId = ${event.roomId}")
        if (!shouldIgnoreDuplicateAcceptEvent) {
            shouldIgnoreDuplicateAcceptEvent = true
            onEntering = true
            startEnteringTimeoutTimer()
        }
        stopOutgoingTimeoutTimer()
        callEstablished()
    }

    /**
     * partner đã wake up thành công và đang đổ chuông
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onPartnerRinging(event: CallPartnerRingingBusEventV2) {
        if (isIntentParsed && callState.isEstablishing && event.partnerId == partner.userId) {
            startOutgoingTimeoutTimer()

            val contactingNotification = CallNotifierV2.createOutgoingPartnerRingingNotification(
                this,
                partner,
                callType
            )

            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                NotificationManagerCompat.from(this.applicationContext).apply {
                    notify(OUTGOING_NOTIFICATION_ID, contactingNotification)
                }
            }

            _callStateLiveData.value =
                CallOutgoingCallState.Establishing.PartnerRinging(event.partnerId)
            stopDialingSound()
            ringingSoundPlayer.playRingingSound()
            callLogger.captureMessage("onPartnerRinging: partner_id = ${event.partnerId}")
        }
    }

    /**
     * case A gọi cho B, bên B đang ringing nhưng B tắt luôn => chung quy là kết thúc
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onPartnerDecline(event: CallPartnerDeclineIncomingCallBusEventV2) {
        if (isIntentParsed && callState.isEstablishing) {
            callLogger.captureHangup("Hangup do Partner từ chối")
            callEnded(CallOutgoingCallState.Ended.PartnerDecline)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onPartnerNoAnswer(event: CallNoAnswerCallBusEventV2) {
        callLogger.captureHangup("Time out - Hangup do Partner không trả lời")
        callEnded(CallOutgoingCallState.Ended.NoAnswer)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onHangupCall(event: CallHangupCallBusEventV2) {
        callLogger.captureHangup("Hangup do bản thân chủ động")
        if (onEntering) {
            callEnded(CallOutgoingCallState.Ended.EndOnEntering)
        } else {
            callEnded(CallOutgoingCallState.Ended.ForceHangup)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onPartnerBusy(event: CallBusyBusEventV2) {
        if (callState.isEstablishing) {
            callLogger.captureHangup("Hangup do Partner busy")
            callEnded(CallOutgoingCallState.Ended.Hangup)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onRestApiErrorWhileEstablish(event: CallRestApiErrorWhileEstablishBusEventV2) {
        callLogger.captureMessage("Call Cancel Reason.UNREACHABLE")
        callEnded(CallOutgoingCallState.Ended.Unreachable)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onLogout(event: LogoutForceStopCallBusEvent) {
        if (!callState.isEnded) {
            callLogger.captureHangup("Hangup do bản thân Logout")
            callEnded(CallOutgoingCallState.Ended.ForceHangup)
            lifecycleScope.launch {
                callCenter.destroyLivekit()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onCallDisconnectedBusEvent(event: CallDisconnectedBusEventV2) {
        callLogger.captureHangup("Hangup do disconnect - ping timeout")
        callEnded(CallOutgoingCallState.Ended.Disconnected)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onCallDisconnectedSoundBusEvent(event: CallDisconnectedSoundBusEventV2) {
        if (!callState.isEstablished) return
        if (event.isPlayingSound) {
            disconnectedSoundPlayer.playDisconnectedSound()
        } else {
            disconnectedSoundPlayer.stopDisconnectedSound()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onCallEndedBusEvent(event: CallEndedBusEventV2) {
        callLogger.captureHangup("Hangup do partner kết thúc cuộc gọi")
        callEnded(CallOutgoingCallState.Ended.Hangup)
    }

    internal fun switchCamera() {
    }

    internal fun startVideoSource() {
        isVideoSourcePaused = false
    }

    internal fun stopVideoSource() {
        isVideoSourcePaused = false
    }

    internal fun pauseVideoSource() {
    }

    internal fun resumeVideoSource() {
        if (isVideoSourcePaused) {
            startVideoSource()
        }
    }

    internal fun setMicrophoneMute(mute: Boolean) {
    }

    internal fun setSpeakerphoneOn(on: Boolean) {
        isExternalSpeakerOpened = on
    }

    private fun updatePartnerProfile(partner: ParticipantModel) {
        this.partner = partner
        this._partnerProfileLiveData.value = partner
    }

    private fun callEstablished() {
        lifecycleScope.launch {
            _callStateLiveData.value = CallOutgoingCallState.Established
            stopRingingSound()
            startCallChronometer()
            if (::partner.isInitialized) {
                val establishedNotification =
                    CallNotifierV2.createOutgoingCallEstablishedNotification(
                        this@CallV2OutgoingService,
                        partner,
                        callType
                    )

                if (ContextCompat.checkSelfPermission(
                        this@CallV2OutgoingService,
                        Manifest.permission.POST_NOTIFICATIONS
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    NotificationManagerCompat.from(applicationContext).apply {
                        notify(OUTGOING_NOTIFICATION_ID, establishedNotification)
                    }
                }
            }
        }
    }

    private fun callEnded(reason: CallOutgoingCallState.Ended) {
        lifecycleScope.launch {
            callLogger.captureSuccess()
            val currentState = callState
            if (!currentState.isEnded) {
                _callStateLiveData.value = reason
                callChronometerExecutor?.cancel()
                Timber.d("callEnded postExternalOutgoingBusEvent")
                postExternalOutgoingBusEvent(null, -1)

                stopRingingSound()
                stopOutgoingTimeoutTimer()
                internetConnectionDetector.unregisterNetworkCallback()
                stopInternetConnectivityTimer()

                if (isIntentParsed) {
                    val endCallNotification = CallNotifierV2.createOutgoingCallEndedNotification(
                        this@CallV2OutgoingService,
                        partner,
                        callType
                    )

                    if (ContextCompat.checkSelfPermission(
                            this@CallV2OutgoingService,
                            Manifest.permission.POST_NOTIFICATIONS
                        ) == PackageManager.PERMISSION_GRANTED
                    ) {
                        NotificationManagerCompat.from(applicationContext).apply {
                            notify(OUTGOING_NOTIFICATION_ID, endCallNotification)
                        }
                    }

                    CallDestroySessionBusEventV2().postEvent()
                }

                // tuỳ reason mới send hangup request
                when (reason) {
                    CallOutgoingCallState.Ended.ForceHangup -> {
                        if (currentState.isEstablished) {
                            callCenter.endCall(EndCallReason.ON_PURPOSE)
                            callLogger.captureEndCall(EndCallReason.ON_PURPOSE.name)
                        } else {
                            if (currentState is CallOutgoingCallState.Establishing.PartnerRinging) {
                                callCenter.cancelCall(CancelCallReason.ON_RINGING)
                                callLogger.captureCancelCall(CancelCallReason.ON_RINGING.name)
                            } else {
                                callCenter.cancelCall(CancelCallReason.ON_DIALING)
                                callLogger.captureCancelCall(CancelCallReason.ON_DIALING.name)
                            }
                        }
                    }

                    CallOutgoingCallState.Ended.EndOnEntering -> {
                        callCenter.endCall(EndCallReason.END_ON_ENTERING)
                        callLogger.captureEndCall(EndCallReason.END_ON_ENTERING.name)
                    }

                    CallOutgoingCallState.Ended.Disconnected -> {
                        callCenter.endCall(EndCallReason.RECONNECT_TIMEOUT)
                        callLogger.captureEndCall(EndCallReason.RECONNECT_TIMEOUT.name)
                    }

                    CallOutgoingCallState.Ended.NoAnswer -> {
                        callCenter.cancelCall(CancelCallReason.RING_TIMEOUT)
                        callLogger.captureCancelCall(CancelCallReason.RING_TIMEOUT.name)
                    }

                    CallOutgoingCallState.Ended.Unreachable -> {
                        callCenter.cancelCall(CancelCallReason.UNREACHABLE)
                        callLogger.captureCancelCall(CancelCallReason.UNREACHABLE.name)
                    }

                    CallOutgoingCallState.Ended.CanNotJoinRoom -> {
                        callCenter.endCall(EndCallReason.CONNECT_FAILED)
                        callLogger.captureEndCall(EndCallReason.CONNECT_FAILED.name)
                    }

                    else -> {
                        callCenter.stopCall()
                    }
                }

                CallStickyBusEvent::class.java.removeStickyEvent()
                <EMAIL>()
                proximitySensor.stop()
                try {
                    stopForeground(true)
                    stopSelf()
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        }
    }

    /**
     * Bắt đầu đếm call duration, post EventBus liên tục
     */
    private fun startCallChronometer() {
        callLogger.captureMessage("start Call Chronometer")
        callChronometerExecutor?.cancel()
        callChronometerExecutor = lifecycleScope.launch {
            while (isActive && callState.isEstablished && isIntentParsed) {
                callDuration += 1
                Timber.d("startCallChronometer postExternalOutgoingBusEvent $callDuration")
                postExternalOutgoingBusEvent(outgoingActivityIntent, callDuration)
                delay(CALL_CHRONOMETER_PERIOD)
            }
        }
    }

    private fun startOutgoingTimeoutTimer() {
        stopOutgoingTimeoutTimer()
        outgoingTimeoutTimer = Timer(OUTGOING_TIMEOUT_COMMON_TIMER_NAME)
        outgoingTimeoutTimerTask = timerTask {
            onPartnerNoAnswer(CallNoAnswerCallBusEventV2())
        }
        outgoingTimeoutTimer?.schedule(
            outgoingTimeoutTimerTask,
            OUTGOING_TIMEOUT_COMMON_TIMER_DELAY
        )
    }

    fun stopOutgoingTimeoutTimer() {
        Timber.e("akaizz stopOutgoingTimeoutTimer")
        outgoingTimeoutTimerTask?.cancel()
        outgoingTimeoutTimer?.cancel()
        outgoingTimeoutTimerTask = null
        outgoingTimeoutTimer = null
    }

    private fun startEnteringTimeoutTimer() {
        if (enteringTimeoutTimer != null) return
        Timber.e("checkOnEntering startEnteringTimeoutTimer")
        enteringTimeoutTimer = Timer(ENTERING_TIMEOUT_COMMON_TIMER_NAME)
        enteringTimeoutTimerTask = timerTask {
            callEnded(CallOutgoingCallState.Ended.EndOnEntering)
        }
        enteringTimeoutTimer?.schedule(
            enteringTimeoutTimerTask,
            ENTERING_TIMEOUT_COMMON_TIMER_DELAY
        )
    }

    fun stopEnteringTimeoutTimer() {
        enteringTimeoutTimerTask?.cancel()
        enteringTimeoutTimer?.cancel()
        enteringTimeoutTimerTask = null
        enteringTimeoutTimer = null
    }

    fun startDisconnectedTimeoutTimer() {
        if (disconnectedTimeoutTimer != null) return
        disconnectedTimeoutTimer = Timer(DISCONNECTED_TIMEOUT_COMMON_TIMER_NAME)
        disconnectedTimeoutTimerTask = timerTask {
            callEnded(CallOutgoingCallState.Ended.CanNotJoinRoom)
        }
        disconnectedTimeoutTimer?.schedule(
            disconnectedTimeoutTimerTask,
            DISCONNECTED_TIMEOUT_COMMON_TIMER_DELAY
        )
    }

    fun stopDisconnectedTimeoutTimer() {
        disconnectedTimeoutTimerTask?.cancel()
        disconnectedTimeoutTimer?.cancel()
        disconnectedTimeoutTimerTask = null
        disconnectedTimeoutTimer = null
    }

    private fun startInternetConnectivityTimer() {
        stopInternetConnectivityTimer()
        internetConnectivityTimer = Timer(INTERNET_CONNECTIVITY_TIMER_NAME)
        internetConnectivityTimerTask = timerTask {
            callEnded(CallOutgoingCallState.Ended.Hangup)
        }
        internetConnectivityTimer?.schedule(
            internetConnectivityTimerTask,
            INTERNET_CONNECTIVITY_TIMER_DELAY
        )
    }

    private fun stopInternetConnectivityTimer() {
        internetConnectivityTimerTask?.cancel()
        internetConnectivityTimer?.cancel()
        internetConnectivityTimerTask = null
        internetConnectivityTimer = null
    }

    private fun stopRingingSound() {
        ringingSoundPlayer.stopRingingSound()
    }

    private fun stopDisconnectedSound() {
        disconnectedSoundPlayer.stopDisconnectedSound()
    }

    private fun stopDialingSound() {
        dialingSoundPlayer?.stopDialingSound()
    }

    private fun postExternalOutgoingBusEvent(
        outgoingIntent: Intent?,
        callDuration: Long
    ) {
        if (isIntentParsed) {
            Timber.d("postExternalOutgoingBusEvent $callDuration")
            CallStickyBusEvent(
                type = callType.externalCallBusEventType,
                partnerId = partner.userId.orEmpty(),
                callIntent = outgoingIntent,
                callDuration = callDuration
            ).postStickyEvent()
        } else {
            CallStickyBusEvent(
                type = "",
                partnerId = "",
                callIntent = null,
                callDuration = -1
            ).postStickyEvent()
        }
    }

    override fun onDestroy() {
        callChronometerExecutor?.cancel()
        callChronometerExecutor = null
        stopRingingSound()
        stopDisconnectedSound()
        stopDialingSound()
        super.onDestroy()
    }

    fun setCallType(callType: LocalCallType) {
        this.callType = callType
    }

    companion object {
        private const val OUTGOING_NOTIFICATION_ID = 9

        private const val CALL_CHRONOMETER_DELAY = 500L // 0,5s
        private const val CALL_CHRONOMETER_PERIOD = 1_000L // 1s

        private const val OUTGOING_TIMEOUT_COMMON_TIMER_NAME = "OutgoingTimeoutCommonTimer"
        private const val ENTERING_TIMEOUT_COMMON_TIMER_NAME = "EnteringTimeoutCommonTimer"
        private const val DISCONNECTED_TIMEOUT_COMMON_TIMER_NAME = "DisconnectedTimeoutCommonTimer"
        internal const val OUTGOING_TIMER_LIMIT_CALL_DELAY = 60 * 60 * 1_000L // 60m
        private const val OUTGOING_TIMEOUT_COMMON_TIMER_DELAY = 40 * 1_000L
        private const val ENTERING_TIMEOUT_COMMON_TIMER_DELAY = 30 * 1_000L
        private const val DISCONNECTED_TIMEOUT_COMMON_TIMER_DELAY = 30 * 1_000L

        private const val INTERNET_CONNECTIVITY_TIMER_NAME = "InternetConnectivityTimer"
        private const val INTERNET_CONNECTIVITY_TIMER_DELAY = 30 * 1_000L // 30s

        internal const val START_OUTGOING_SERVICE_ACTION = "START_OUTGOING_SERVICE_ACTION"
        internal const val STOP_OUTGOING_SERVICE_ACTION = "STOP_OUTGOING_SERVICE_ACTION"
    }
}
