package com.gg.gapo.messenger.helper.tools.customview

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * Created by vietbq on 8/27/18.
 */

class DisableSwipeViewPager : ViewPager {
    private var disable: Boolean? = false

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    override fun onInterceptTouchEvent(event: MotionEvent): <PERSON>olean {
        return if (disable!!) false else super.onInterceptTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): <PERSON><PERSON>an {
        return if (disable!!) false else super.onTouchEvent(event)
    }

    fun disableScroll(disable: Boolean?) {
        // When disable = true not work the scroll and when disble = false work the scroll
        this.disable = disable
    }
}
