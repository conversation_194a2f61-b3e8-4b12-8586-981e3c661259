package com.gg.gapo.messenger.data.sources.models

import com.gg.gapo.messenger.data.base.EntityMapper
import com.gg.gapo.messenger.domain.models.Forward
import com.gg.gapo.messenger.domain.models.Sender
import com.google.gson.annotations.SerializedName

data class ForwardDto(
    @SerializedName("message_id")
    val messageId: Long?,
    @SerializedName("thread_id")
    val threadId: Long?,
    @SerializedName("user")
    val user: SenderDto?,
    @SerializedName("is_save_message")
    val isSaveMessage: Boolean? = null
) : EntityMapper<ForwardDto, Forward> {
    override fun mapFromEntity(): Forward {
        return Forward(
            messageId = messageId?.toString() ?: "",
            threadId = threadId?.toString() ?: "",
            user = user?.mapFromEntity() ?: Sender(),
            isSaveMessage = isSaveMessage ?: false
        )
    }

    override fun mapToEntity(): ForwardDto {
        TODO("Not yet implemented")
    }
}
