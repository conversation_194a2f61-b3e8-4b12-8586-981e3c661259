package com.gg.gapo.messenger.presentation.features.chat.models.factory

import com.gg.gapo.messenger.domain.models.Message
import com.gg.gapo.messenger.domain.models.MessageType
import kotlin.math.abs

object MessageBackgroundType {
    fun getBackgroundType(
        position: Int,
        data: List<Message>
    ): BackgroundType {
        val currentMessage: Message = data[position]
        if (currentMessage.body.type == MessageType.ASANA.type) {
            return BackgroundType.NORMAL
        }
        if (currentMessage.body.text.isNotEmpty() && (
            currentMessage.body.type == MessageType.CONFERENCE.type ||
                currentMessage.body.type == MessageType.MEETING_RECORD.type ||
                currentMessage.body.type == MessageType.MEETING.type ||

                currentMessage.body.type == MessageType.MULTI_IMAGE.type ||
                currentMessage.body.type == MessageType.VIDEO.type ||
                currentMessage.body.type == MessageType.IMAGE.type ||
                currentMessage.body.type == MessageType.FILE.type
            )
        ) {
            if (currentMessage.body.type == MessageType.CONFERENCE.type ||
                currentMessage.body.type == MessageType.MEETING_RECORD.type ||
                currentMessage.body.type == MessageType.MEETING.type
            ) {
                return BackgroundType.NORMAL
            }
            return BackgroundType.BLOCK_TOP
        }
        if (!shouldChangeBackground(currentMessage)) {
            return when (MessageType.from(currentMessage.body.type)) {
                MessageType.VIDEO, MessageType.IMAGE -> BackgroundType.BLOCK_BOTTOM
                else -> BackgroundType.NORMAL
            }
        }
        val previousMessage: Message? = if (position + 1 < data.size) data[position + 1] else null
        val nextMessage: Message? = if (position - 1 >= 0) data[position - 1] else null

        return when {
            /** BLOCK_TOP */
            nextMessage != null && isSameSender(currentMessage, nextMessage) &&
                isNotReplyMessage(nextMessage) && shouldChangeBackground(nextMessage) &&
                (
                    previousMessage == null ||
                        breakBlockMedia(previousMessage) ||
                        (
                            isNotSameSender(
                                currentMessage,
                                previousMessage
                            ) && isNotReplyMessage(nextMessage)
                            ) ||
                        shouldShowTimeHeader(previousMessage, currentMessage) ||
                        isReplyMessage(currentMessage) && isTextType(nextMessage)
                    )
            -> BackgroundType.BLOCK_TOP

            /** BLOCK_MIDDLE */
            previousMessage != null &&
                nextMessage != null &&
                isNotReplyMessage(currentMessage) &&
                isNotReplyMessage(nextMessage) &&
                shouldChangeBackground(previousMessage) &&
                shouldChangeBackground(nextMessage) &&
                isSameSender(currentMessage, previousMessage) &&
                isSameSender(currentMessage, nextMessage) &&
                !shouldShowTimeHeader(currentMessage, previousMessage) &&
                !shouldShowTimeHeader(currentMessage, nextMessage)
            -> BackgroundType.BLOCK_MIDDLE

            /** BLOCK_BOTTOM */
            isNotReplyMessage(currentMessage) && previousMessage != null && isSameSender(
                currentMessage,
                previousMessage
            ) &&
                shouldChangeBackground(previousMessage) &&
                (
                    nextMessage != null && isNotSameSender(currentMessage, nextMessage) ||
                        nextMessage != null && breakBlockMedia(nextMessage) ||
                        nextMessage != null && shouldShowTimeHeader(
                        nextMessage,
                        currentMessage
                    ) ||
                        nextMessage != null && isReplyMessage(nextMessage) ||
                        nextMessage == null
                    )
            -> BackgroundType.BLOCK_BOTTOM

            else -> {
                BackgroundType.NORMAL
            }
        }
    }

    private fun isSameSender(first: Message, second: Message) = first.sender.id == second.sender.id
    private fun isNotSameSender(first: Message, second: Message) = !(isSameSender(first, second))

    private fun breakBlockMedia(message: Message): Boolean {
        return when {
            shouldChangeBackground(message) -> false
            else -> true
        }
    }

    private fun shouldChangeBackground(message: Message): Boolean {
        return message.body.type == MessageType.TEXT.type ||
            (message.body.type == MessageType.VIDEO.type && message.body.text.isEmpty()) ||
            (message.body.type == MessageType.IMAGE.type && message.body.text.isEmpty())
    }

    private fun shouldShowTimeHeader(message: Message, previousMess: Message): Boolean {
        val diff = abs(previousMess.createdAt - message.createdAt)
        return java.util.concurrent.TimeUnit.MILLISECONDS.toMinutes(diff) > 10
    }

    private fun isReplyMessage(message: Message) = message.body.replyToMsg.isNotEmpty()

    private fun isNotReplyMessage(message: Message) = !isReplyMessage(message)

    private fun isTextType(message: Message) = message.body.type == MessageType.TEXT.type
}

enum class BackgroundType {
    BLOCK_TOP, BLOCK_MIDDLE, BLOCK_BOTTOM, NORMAL
}
