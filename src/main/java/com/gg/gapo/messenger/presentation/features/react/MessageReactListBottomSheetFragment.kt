package com.gg.gapo.messenger.presentation.features.react

import android.app.Dialog
import android.content.DialogInterface
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.gg.gapo.core.ui.bottomsheet.GapoBottomSheetFragment
import com.gg.gapo.core.utilities.databinding.autoCleared
import com.gg.gapo.messenger.databinding.FragmentChatReactListBinding
import com.gg.gapo.messenger.domain.models.*
import com.gg.gapo.messenger.domain.models.responses.PagingLinksModel
import com.gg.gapo.messenger.helper.extensions.invisible
import com.gg.gapo.messenger.helper.extensions.visible
import com.gg.gapo.messenger.helper.utils.setWhiteNavigationBar
import com.gg.gapo.messenger.presentation.features.managers.UserChatManager
import com.gg.gapo.messenger.presentation.features.react.models.ItemReactHeaderListener
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class MessageReactListBottomSheetFragment : GapoBottomSheetFragment(), KoinComponent {

    private var binding by autoCleared<FragmentChatReactListBinding>()
    private var controllerHeader by autoCleared<ReactHeaderController>()
    private var controllerUsers by autoCleared<ReactUserListController>()
    private val viewModel: UserReactionListViewModel by inject()
    private val userManager: UserChatManager by inject()
    private var reactId = ""

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            dialog.setWhiteNavigationBar()
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChatReactListBinding.inflate(inflater, container, false)
        reactId = arguments?.getString(ARGUMENT_REACT_ID).orEmpty()
        if (reactId.isNotEmpty()) {
            loadFromNetwork(reactId)
        } else {
            val messageReact = arguments?.getParcelable<MessageReact>(ARGUMENT_REACT_MESSAGE_REACT)
            val partner = arguments?.getParcelable<ChatUser>(ARGUMENT_REACT_PARTNER)
            if (messageReact != null && partner != null) {
                loadFromLocal(messageReact.id, messageReact, partner)
            }
        }
        viewModel.onCompletedUnReact.observe(viewLifecycleOwner, {
            if (it) {
                dismissAllowingStateLoss()
            }
        })
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.scrollView.viewTreeObserver
            .addOnScrollChangedListener {
                if (!binding.scrollView.canScrollVertically(1)) {
                    viewModel.getReactUsers(reactId, controllerUsers.currentType(), controllerUsers.getNextTimeToLoadMore())
                }
            }
    }

    private fun loadFromLocal(reactId: String, messageReact: MessageReact, partner: ChatUser) {
        binding.loadMoreProgress.invisible()
        controllerUsers = ReactUserListController(
            requireContext(),
            object : ReactionUserListener {
                override fun onLoadMore(reactTypeSelected: String, lastedTime: Long) {
                }

                override fun onUnReact(reactType: String) {
                    controllerHeader.unReact(reactType)
                    viewModel.unReact(reactId)
                    dismissAllowingStateLoss()
                }
            }
        )
        controllerHeader =
            ReactHeaderController(
                requireContext(),
                object : ItemReactHeaderListener {
                    override fun onSelectedType(type: String) {
                        controllerHeader.setSelectedType(type)
                        controllerUsers.changeReactType(type)
                    }
                }
            )
        binding.recyclerViewHeader.setController(controllerHeader)
        binding.recyclerViewUser.setController(controllerUsers)
        controllerHeader.setHeader(messageReact)
        loadMyInfoLocal(messageReact)
        loadAnotherUser(messageReact, partner)
    }

    private fun loadAnotherUser(messageReact: MessageReact, userChat: ChatUser) {
        val reactType = when {
            messageReact.reactCount?.reactType1 ?: 0 == 1 && 1 != messageReact.reactYourself -> {
                1
            }
            messageReact.reactCount?.reactType2 ?: 0 == 1 && 2 != messageReact.reactYourself -> {
                2
            }
            messageReact.reactCount?.reactType3 ?: 0 == 1 && 3 != messageReact.reactYourself -> {
                3
            }
            messageReact.reactCount?.reactType4 ?: 0 == 1 && 4 != messageReact.reactYourself -> {
                4
            }
            messageReact.reactCount?.reactType5 ?: 0 == 1 && 5 != messageReact.reactYourself -> {
                5
            }
            messageReact.reactCount?.reactType6 ?: 0 == 1 && 6 != messageReact.reactYourself -> {
                6
            }
            else -> {
                if (messageReact.reactCountTotal >= 2) {
                    var max = messageReact.reactCount?.reactType1 ?: 0
                    if (messageReact.reactCount?.reactType2 ?: 0 > max) {
                        max = messageReact.reactCount?.reactType2 ?: 0
                    }
                    if (messageReact.reactCount?.reactType3 ?: 0 > max) {
                        max = messageReact.reactCount?.reactType3 ?: 0
                    }
                    if (messageReact.reactCount?.reactType4 ?: 0 > max) {
                        max = messageReact.reactCount?.reactType4 ?: 0
                    }
                    if (messageReact.reactCount?.reactType5 ?: 0 > max) {
                        max = messageReact.reactCount?.reactType5 ?: 0
                    }
                    if (messageReact.reactCount?.reactType6 ?: 0 > max) {
                        max = messageReact.reactCount?.reactType6 ?: 0
                    }
                    max
                } else 0
            }
        }
        if (reactType > 0) {
            controllerUsers.addReactUser(
                reactType.toString(),
                Pair(
                    first = arrayListOf(
                        createUserReactionModel(reactType, userChat)
                    ),
                    second = PagingLinksModel()
                )
            )
            controllerUsers.addReactUser(
                "0",
                Pair(
                    first = arrayListOf(
                        createUserReactionModel(reactType, userChat)
                    ),
                    second = PagingLinksModel()
                )
            )
        }
    }

    private fun loadMyInfoLocal(messageReact: MessageReact) {
        if (messageReact.reactYourself > 0) {
            val userChat = userManager.getCurrentUserChat()
            controllerUsers.addReactUser(
                messageReact.reactYourself.toString(),
                Pair(
                    first = arrayListOf(
                        createUserReactionModel(messageReact.reactYourself, userChat)
                    ),
                    second = PagingLinksModel()
                )
            )
            controllerUsers.addReactUser(
                "0",
                Pair(
                    first = arrayListOf(
                        createUserReactionModel(messageReact.reactYourself, userChat)
                    ),
                    second = PagingLinksModel()
                )
            )
        }
    }

    private fun createUserReactionModel(reactType: Int, userChat: ChatUser): UserReactionModel {
        return UserReactionModel(
            reactType = reactType,
            relation = "",
            updatedAt = System.currentTimeMillis(),
            user = User(
                id = userChat.getId(),
                displayName = userChat.name,
                name = userChat.name,
                avatar = userChat.avatar,
                avatarThumbPattern = userChat.avatarThumb,
                preference = "",
                readCount = 0,
                role = UserRole.MEMBER,
                type = "",
                status = 0,
                lastSeen = 0,
                statusVerify = userChat.statusVerify.value
            )
        )
    }

    private fun loadFromNetwork(reactId: String) {
        controllerUsers = ReactUserListController(
            requireContext(),
            object : ReactionUserListener {
                override fun onLoadMore(reactTypeSelected: String, lastedTime: Long) {
                }

                override fun onUnReact(reactType: String) {
                    controllerHeader.unReact(reactType)
                    viewModel.unReact(reactId)
                }
            }
        )
        controllerHeader =
            ReactHeaderController(
                requireContext(),
                object : ItemReactHeaderListener {
                    override fun onSelectedType(type: String) {
                        controllerHeader.setSelectedType(type)
                        controllerUsers.changeReactType(type)
                        viewModel.getReactUsers(reactId, type, 0)
                        binding.loadMoreProgress.invisible()
                    }
                }
            )

        binding.recyclerViewHeader.setController(controllerHeader)
        binding.recyclerViewUser.setController(controllerUsers)

        viewModel.onResultReactCount.observe(viewLifecycleOwner, {
            controllerHeader.setHeader(it)
        })
        viewModel.onResultUsersLike.observe(viewLifecycleOwner, {
            controllerUsers.addReactUser(it.first, Pair(it.second, it.third))
        })
        viewModel.onCompletedRequest.observe(viewLifecycleOwner, {
            binding.loadMoreProgress.invisible()
        })
        viewModel.getChatReactCount(reactId)
        viewModel.getReactUsers(reactId, "0", 0)
        binding.loadMoreProgress.visible()
    }

    override fun dismissAllowingStateLoss() {
        releaseAll()
        super.dismissAllowingStateLoss()
    }

    override fun onCancel(dialog: DialogInterface) {
        releaseAll()
        super.onCancel(dialog)
    }

    private fun releaseAll() {
        controllerHeader.cancelPendingModelBuild()
        controllerUsers.cancelPendingModelBuild()
    }

    companion object {
        fun show(manager: FragmentManager, reactId: String) {
            val ft = manager.beginTransaction()
            ft.add(
                MessageReactListBottomSheetFragment().apply {
                    arguments = Bundle().apply {
                        putString(ARGUMENT_REACT_ID, reactId)
                    }
                },
                "MessageReactListBottomSheetFragment"
            )
            ft.commitAllowingStateLoss()
        }

        fun show(manager: FragmentManager, messageReact: MessageReact, partner: ChatUser) {
            val ft = manager.beginTransaction()
            ft.add(
                MessageReactListBottomSheetFragment().apply {
                    arguments = Bundle().apply {
                        putParcelable(ARGUMENT_REACT_MESSAGE_REACT, messageReact)
                        putParcelable(ARGUMENT_REACT_PARTNER, partner)
                    }
                },
                "MessageReactListBottomSheetFragment"
            )
            ft.commitAllowingStateLoss()
        }

        private const val ARGUMENT_REACT_ID = "ARGUMENT_REACT_ID"
        private const val ARGUMENT_REACT_MESSAGE_REACT = "ARGUMENT_REACT_MESSAGE_REACT"
        private const val ARGUMENT_REACT_PARTNER = "ARGUMENT_REACT_PARTNER"
    }
}
