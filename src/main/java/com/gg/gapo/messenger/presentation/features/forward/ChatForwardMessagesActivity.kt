package com.gg.gapo.messenger.presentation.features.forward

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.SearchView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.KeyboardUtils
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.deeplink.messenger.MessengerForwardDeepLink
import com.gg.gapo.core.ui.GapoIntegers
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.core.ui.snackbar.makeNegativeSnackbar
import com.gg.gapo.core.utilities.livedata.EventObserver
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.presentation.messenger.warning.MessengerWarningDialogFragment
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.databinding.ActivityChatForwardMessagesBinding
import com.gg.gapo.messenger.helper.Constant
import com.gg.gapo.messenger.helper.extensions.*
import com.gg.gapo.messenger.helper.utils.AlertUtils
import com.gg.gapo.messenger.helper.utils.ChatUtils
import com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageMetadata
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import timber.log.Timber
import java.util.*

@AppDeepLink("messenger/forward")
internal class ChatForwardMessagesActivity :
    GapoThemeBaseActivity(),
    ForwardMessageController.ForwardMessageControllerListener {

    companion object {
        const val EXTRA_MESSAGE_METADATA = "message_metadata"
        const val EXTRA_MESSAGE_FROM_MULTIPLE_TARGET = "message_from_multiple_target"
        const val EXTRA_MESSAGE_MARKDOWN = "message_is_markdown"

        fun start(
            context: Context,
            content: String = "",
            isFromMultipleTarget: Boolean = false,
            isMarkdownText: Boolean = false
        ) {
            context.startActivity(
                start(
                    context = context,
                    messageIds = arrayListOf(),
                    threadId = "",
                    isFromMultipleTarget = isFromMultipleTarget,
                    isMarkdownText = isMarkdownText,
                    content = content
                )
            )
        }

        fun start(
            context: Context,
            messageId: String,
            threadId: String,
            content: String = "",
            isFromMultipleTarget: Boolean = false,
            isMarkdownText: Boolean = false
        ) {
            context.startActivity(
                start(
                    context = context,
                    messageIds = arrayListOf(messageId),
                    threadId = threadId,
                    isFromMultipleTarget = isFromMultipleTarget,
                    isMarkdownText = isMarkdownText,
                    content = content
                )
            )
        }

        fun start(
            context: Context,
            messageIds: ArrayList<String>,
            threadId: String,
            content: String = "",
            isFromMultipleTarget: Boolean = false,
            isMarkdownText: Boolean = false
        ): Intent {
            return Intent(context, ChatForwardMessagesActivity::class.java).apply {
                this.putExtras(
                    Bundle().apply {
                        this.putBoolean(EXTRA_MESSAGE_FROM_MULTIPLE_TARGET, isFromMultipleTarget)
                        this.putBoolean(EXTRA_MESSAGE_MARKDOWN, isMarkdownText)
                        this.putString(MessengerForwardDeepLink.CONTENT_ID_EXTRA, content)
                        this.putStringArrayList(
                            MessengerForwardDeepLink.MESSAGE_ID_EXTRA,
                            messageIds
                        )
                        this.putString(
                            MessengerForwardDeepLink.THREAD_ID_EXTRA,
                            threadId
                        )
                    }
                )
            }
        }
    }

    private lateinit var binding: ActivityChatForwardMessagesBinding
    private lateinit var threadController: ForwardMessageController

    private val messageId by extraNotNull(
        MessengerForwardDeepLink.MESSAGE_ID_EXTRA,
        arrayListOf<String>()
    )
    private val threadId by extraNotNull(MessengerForwardDeepLink.THREAD_ID_EXTRA, "")
    private val content by extraNotNull(MessengerForwardDeepLink.CONTENT_ID_EXTRA, "")
    private val messageMetadata by extra<ChatForwardMessageMetadata>(EXTRA_MESSAGE_METADATA)
    private val isFromMultipleTarget by extraNotNull(EXTRA_MESSAGE_FROM_MULTIPLE_TARGET, false)
    private val isMarkdownText by extraNotNull(EXTRA_MESSAGE_MARKDOWN, false)
    private val viewModel: ChatForwardMessagesViewModel by inject()

    private var pastVisibleItems: Int = 0
    private var visibleItemCount: Int = 0
    private var totalItemCount: Int = 0

    private val dataTable = arrayListOf<Uri>()
    private var job: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_chat_forward_messages)
        init()
    }

    private fun init() {
        initTabLayout()
        if (isFromMultipleTarget) {
            binding.title.setText(GapoStrings.new_approval_todo_tab_title)
            binding.textDone.visibility = View.VISIBLE
            binding.textDone.setDebouncedClickListener {
                finish()
            }
        }

        threadController = ForwardMessageController(this, this, viewModel.myUserId)
        with(binding.recyclerView) {
            adapter = threadController.adapter
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    if (dy > 0) {
                        visibleItemCount = layoutManager!!.childCount
                        totalItemCount = layoutManager!!.itemCount
                        pastVisibleItems =
                            (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                        if (visibleItemCount + pastVisibleItems >= totalItemCount) {
                            if (binding.searchView.query.isNotEmpty()) {
                                viewModel.search(binding.searchView.query.toString())
                            }
                        }
                    }
                }
            })
        }

        binding.imvBack.setDebouncedClickListener {
            if (binding.searchView.query.isNotEmpty()) {
                viewModel.resetSearch()
                viewModel.cancelSearch()
                binding.searchView.setQuery("", false)
                binding.loadMoreProgress.gone()
                binding.recyclerView.scrollToPosition(0)
                return@setDebouncedClickListener
            }
            finish()
        }

        viewModel.onConversations.observe(this) {
            binding.loadMoreProgress.gone()
            threadController.addConversation(it)
        }
        viewModel.onSubThreads.observe(this) {
            binding.loadMoreProgress.gone()
            threadController.addSubThreads(it)
        }
        viewModel.onSearchSubThreadResult.observe(this) {
            binding.loadMoreProgress.gone()
            threadController.addSubThreads(it)
        }

        viewModel.onSearchSubThreadResult.observe(this) {
            binding.loadMoreProgress.gone()
            threadController.addSearchSubThreadResult(it)
        }

        viewModel.onSearchResult.observe(this) {
            binding.loadMoreProgress.gone()
            threadController.addSearchResult(it)
        }

        viewModel.onShowShareResult.observe(this) {
            showProgressBar(false)
        }
        viewModel.getLocalConversation()

        viewModel.snackBarErrorMessage.observe(this) {
            it.getContentIfNotHandled()?.let {
                makeNegativeSnackbar(it)?.show()
            }
        }
        viewModel.onSendResult.observe(this) {
            onHandleSend(it)
        }

        setupSearchView()

        if (intent?.action == Intent.ACTION_SEND || intent?.action == Intent.ACTION_SEND_MULTIPLE) {
            shareFilePermissionLauncher.launch(arrayPermissions)
        } else if (messageMetadata != null) {
            binding.title.setText(com.gg.gapo.core.ui.GapoStrings.shared_share)
        }
        viewModel.viewEvent.observe(
            this,
            EventObserver {
                onViewEvent(it)
            }
        )
    }

    private fun initTabLayout() {
        binding.tabLayout.addTab(
            binding.tabLayout.newTab().setText(GapoStrings.messenger_search_conversation)
        )
        binding.tabLayout.addTab(
            binding.tabLayout.newTab().setText(GapoStrings.messenger_sub_threads)
        )
        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab != null) {
                    if (tab.position == 0) {
                        threadController.setShowSubThread(false)
                    } else {
                        threadController.setShowSubThread(true)
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
    }

    private fun onViewEvent(event: ChatForwardMessageViewEvent) {
        when (event) {
            is ChatForwardMessageViewEvent.OnWarning -> {
                MessengerWarningDialogFragment.newInstance(event.message)
                    .show(supportFragmentManager, null)
            }

            is ChatForwardMessageViewEvent.OnSendSuccess -> {
                threadController.updateSent(event.threadId, event.userId)
            }
        }
    }

    override fun onBackPressed() {
        if (binding.searchView.query.isNotEmpty()) {
            viewModel.resetSearch()
            viewModel.cancelSearch()
            binding.searchView.setQuery("", false)
            binding.loadMoreProgress.gone()
            binding.recyclerView.scrollToPosition(0)
            return
        }
        super.onBackPressed()
    }

    override fun onSearchBarClicked() {
        binding.searchView.visible()
        binding.searchView.requestLayout()
        KeyboardUtils.showSoftInput(binding.searchView)
    }

    private val arrayPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO
        )
    } else {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }

    private val shareFilePermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.values.all { it }) {
                val isSingleFile = intent?.action == Intent.ACTION_SEND
                handleSendFile(intent, isSingleFile)
                if (dataTable.isNotEmpty()) {
                    binding.title.setText(com.gg.gapo.core.ui.GapoStrings.messenger_forward_title_share)
                }
            } else {
                makeNegativeSnackbar(GapoStrings.messenger_forward_file_from_outside_error)?.show()
            }
        }

    private val uploadPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.values.all { it }) {
                uploadPermissionRequest?.invoke()
                uploadPermissionRequest = null
            } else {
                makeNegativeSnackbar(GapoStrings.messenger_forward_file_from_outside_error)?.show()
            }
        }

    private var uploadPermissionRequest: (() -> Unit)? = null

    override fun onSend(item: ConversationModel?) {
        if (item?.type == ConversationType.GROUP || item?.type == ConversationType.SUB_THREAD) {
            onHandleSend(item)
        } else {
            viewModel.createThread(item, item?.partner?.id.orEmpty())
        }
        setResult(
            RESULT_OK,
            Intent().apply {
                this.putExtra(MessengerForwardDeepLink.THREAD_ID_EXTRA, threadId)
            }
        )
    }

    private fun onHandleSend(item: ConversationModel?) {
        if (isFromMultipleTarget) {
            viewModel.sendMultipleUsers(
                threadId = item?.id ?: 0L,
                partnerId = item?.partner?.id?.toLongOrNull() ?: 0L,
                message = content,
                isMarkdownText = isMarkdownText
            )
            return
        }
        if (intent?.action == "$packageName.intent.action.MeetingInvite") {
            intent?.data?.let {
                val zoomId = intent.getLongExtra("meetingId", 0)
                val password = intent.getStringExtra("meetingRawPassword")
                val message = "Zoom Id: $zoomId\nPassword: $password\n$it"
                viewModel.sendText(this, item, message)
            }
        } else if (messageMetadata != null) {
            viewModel.sendNewMetadataMessage(this, messageMetadata!!, item)
        } else if (dataTable.isNotEmpty()) {
            uploadPermissionRequest = {
                showProgressBar(true)
                viewModel.uploadFile(item, dataTable)
            }
            uploadPermissionLauncher.launch(
                arrayPermissions
            )
        } else {
            if (content.isNotEmpty()) {
                viewModel.sendText(this, item, content)
            } else {
                when (item?.type) {
                    ConversationType.DIRECT -> {
                        viewModel.forwardMessage(
                            sourceMessageIds = messageId,
                            sourceThreadId = threadId,
                            threadId = "",
                            partnerId = item.partner?.id.orEmpty()
                        )
                    }

                    ConversationType.GROUP -> {
                        viewModel.forwardMessage(
                            sourceThreadId = threadId,
                            sourceMessageIds = messageId,
                            threadId = item.id.toString(),
                            partnerId = item.partner?.id.orEmpty()
                        )
                    }

                    else -> {
                        viewModel.forwardMessage(
                            sourceThreadId = threadId,
                            sourceMessageIds = messageId,
                            threadId = item?.id.toString(),
                            partnerId = item?.partner?.id.orEmpty()
                        )
                    }
                }
            }
        }
    }

    private fun setupSearchView() {
        with(binding.searchView) {
            isFocusable = true
            isIconified = false
            clearFocus()

            if (!query.isNullOrEmpty() && query.length >= 0) {
                setQuery(query, true)
            }

            setOnQueryTextListener(object : SearchView.OnQueryTextListener {

                override fun onQueryTextSubmit(query: String): Boolean {
                    return false
                }

                override fun onQueryTextChange(newText: String): Boolean {
                    job?.cancel()
                    job = lifecycleScope.launch {
                        delay(resources.getInteger(GapoIntegers.search_debounce_value).toLong())
                        viewModel.resetSearch()
                        threadController.clearSearch()
                        threadController.hasSearch(query = newText)
                        if (newText.isEmpty()) {
                            binding.loadMoreProgress.gone()
                            viewModel.cancelSearch()
                        } else {
                            binding.loadMoreProgress.visible()
                            viewModel.search(newText, false)
                        }
                    }
                    return false
                }
            })
        }
    }

    private fun handleSendFile(intent: Intent, isSingle: Boolean) {
        if (isSingle) {
            (intent.getParcelableExtra<Parcelable>(Intent.EXTRA_STREAM) as? Uri)?.let {
                Timber.d(it.toString())
                dataTable.add(it)
            }
        } else {
            intent.getParcelableArrayListExtra<Parcelable>(Intent.EXTRA_STREAM)?.let {
                it.filterIsInstance<Uri>().forEach { uri ->
                    handleValidationUploadFile(uri)
                }
            }
        }
    }

    private fun showProgressBar(isEnable: Boolean) {
        binding.loadMoreProgress.visibility = if (isEnable) View.VISIBLE else View.GONE
        binding.recyclerView.alpha = if (isEnable) 0.5f else 1f
    }

    private fun handleValidationUploadFile(selectedFile: Uri) {
        val fileInfo = ChatUtils.getFileInfoByUri(selectedFile, this)
        if (fileInfo.first.isNotEmpty() && FileExtensionSupport.isUnSupportUploadFile(fileInfo.first)) {
            AlertUtils.warningUnsupportedFileType(this, viewModel.brandName)
        } else if (fileInfo.second != -1L && fileInfo.second > Constant.MAX_FILE_4GB) {
            AlertUtils.warningFileTooLarge(this)
        } else {
            dataTable.add(selectedFile)
        }
    }
}
