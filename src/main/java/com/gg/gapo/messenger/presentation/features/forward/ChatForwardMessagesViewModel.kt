package com.gg.gapo.messenger.presentation.features.forward

import android.app.Application
import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.UriUtils
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.branding.BrandingName.replaceByBrandingName
import com.gg.gapo.core.utilities.coroutines.CoroutineDispatchers
import com.gg.gapo.core.utilities.file.isUnSupportedFileExt
import com.gg.gapo.core.utilities.result.Result
import com.gg.gapo.core.utilities.settings.GapoGeneralSettings.setUploadHighQualityEnabled
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.BooleanValue
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationGroupLevel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationType
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageMultipleTargetRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.create_request.MessageRequestModel
import com.gg.gapo.feature.messenger.domain.messenger.model.message.enum_message.MessageBodyType
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.FetchConversationByFolderUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.GetConversationsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.conversation.SearchConversationsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.CreateMessageMultipleTargetUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.FetchUsersByUserIdsUseCase
import com.gg.gapo.feature.messenger.domain.messenger.usecase.message.SaveMessageCreatorRequestUseCase
import com.gg.gapo.feature.messenger.utils.MessengerConstant
import com.gg.gapo.feature.messenger.utils.getFileNameAndSizeByUri
import com.gg.gapo.feature.messenger.utils.isVideo
import com.gg.gapo.feature.messenger.worker.createMessage
import com.gg.gapo.messenger.data.sources.models.upload.EmitProgressFileUpload
import com.gg.gapo.messenger.domain.models.*
import com.gg.gapo.messenger.domain.usecases.ConversationUseCase
import com.gg.gapo.messenger.domain.usecases.ForwardUseCase
import com.gg.gapo.messenger.helper.Constant
import com.gg.gapo.messenger.helper.extensions.FilePhotoExtensionSupport
import com.gg.gapo.messenger.helper.extensions.FileVideoExtensionSupport
import com.gg.gapo.messenger.helper.utils.ChatUtils
import com.gg.gapo.messenger.presentation.common.BaseViewModel
import com.gg.gapo.messenger.presentation.common.ErrorUtils
import com.gg.gapo.messenger.presentation.common.Event
import com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageMetadata
import com.gg.gapo.messenger.presentation.features.forward.models.mapToEmitProgressFileUpload
import com.gg.gapo.messenger.workers.SendMessageWorker
import com.gg.gapo.messenger.workers.UploadMediaWorker
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import io.reactivex.Flowable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.koin.core.component.KoinComponent
import timber.log.Timber
import java.util.concurrent.TimeUnit

internal class ChatForwardMessagesViewModel(
    private val application: Application,
    private val useCases: ForwardUseCase,
    private val fetchUsersWithIdsUseCase: FetchUsersByUserIdsUseCase,
    private val conversationUseCase: ConversationUseCase,
    private val getConversationsUseCase: GetConversationsUseCase,
    private val onPremiseManager: OnPremiseManager,
    private val createMessageMultipleTargetUseCase: CreateMessageMultipleTargetUseCase,
    private val saveMessageCreatorRequestUseCase: SaveMessageCreatorRequestUseCase,
    private val coroutineDispatchers: CoroutineDispatchers,
    private val searchConversationsUseCase: SearchConversationsUseCase,
    private val userManager: UserManager,
    private val fetchConversationByFolderUseCase: FetchConversationByFolderUseCase
) : BaseViewModel(), KoinComponent {

    val brandName get() = onPremiseManager.onPremiseConfigData?.brandName.orEmpty()

    val onSubThreads = MutableLiveData<List<ConversationModel>>()
    val onSearchSubThreadResult = MutableLiveData<List<ConversationModel>>()

    val onConversations = MutableLiveData<List<ConversationModel>>()
    val onSearchResult = MutableLiveData<List<ConversationModel>>()
    val onShowShareResult = MutableLiveData<Boolean>()
    val onSendResult = MutableLiveData<ConversationModel>()

    private var page = 0
    private var searchPageNumber = 1
    private var searchSubThreadPageNumber = 1

    private var job: Job? = null

    private val _viewEvent =
        MutableLiveData<com.gg.gapo.core.utilities.livedata.Event<ChatForwardMessageViewEvent>>()
    val viewEvent: LiveData<com.gg.gapo.core.utilities.livedata.Event<ChatForwardMessageViewEvent>> =
        _viewEvent

    val myUserId by lazy {
        userManager.userId
    }

    fun getLocalConversation() {
        viewModelScope.launch {
            when (val result = getConversationsUseCase()) {
                is Result.Success -> {
                    val threadData = ArrayList(
                        result.data.filter { conversation ->
                            conversation.blockedBy.orEmpty().isEmpty() && (conversation.settings?.disableMemberSendMessage() == null || conversation.settings.disableMemberSendMessage() == false)
                        }
                    )

                    val ids = threadData.filter {
                        it.partner?.id.orEmpty().isNotEmpty() && it.partner?.isSystemOrBot == false
                    }.map { it.partner?.id.orEmpty() }
                    when (
                        val partner =
                            fetchUsersWithIdsUseCase(ids)
                    ) {
                        is Result.Success -> {
                            threadData.forEachIndexed { index, thread ->
                                partner.data.firstOrNull {
                                    thread.partner?.id.orEmpty()
                                        .isNotEmpty() && thread.partner?.id.orEmpty() == it.id
                                }?.let {
                                    threadData[index].apply {
                                        this.partner?.copy(
                                            status = partner.data.firstOrNull()?.status ?: 0
                                        )
                                    }
                                }
                            }
                            when (
                                val resultSubThread =
                                    fetchConversationByFolderUseCase(lastId = 0, pageSize = 20, ConversationType.SUB_THREAD.type)
                            ) {
                                is Result.Success -> {
                                    val subThreadFilter = arrayListOf<ConversationModel>()
                                    for (conversation in resultSubThread.data) {
                                        val findThread = threadData.firstOrNull { it.id == conversation.parentId }
                                        when (findThread) {
                                            null -> {
                                                subThreadFilter.add(conversation)
                                            }
                                            else ->
                                                if (findThread.settings?.disableMemberSendSubMessage() == false &&
                                                    conversation.blockedBy.orEmpty().isEmpty() && (conversation.settings?.disableMemberSendSubMessage() == null || conversation.settings.disableMemberSendSubMessage() == false)
                                                ) {
                                                    subThreadFilter.add(conversation)
                                                } else {
                                                    // nothing
                                                }
                                        }
                                    }
                                    onSubThreads.postValue(
                                        subThreadFilter
                                    )
                                }
                                is Result.Error -> {
                                    Timber.e(resultSubThread.exception)
                                }
                            }
                            onConversations.postValue(threadData)
                        }

                        is Result.Error -> {
                            Timber.e(partner.exception)
                            onConversations.postValue(threadData)
                        }
                    }
                }

                else -> {}
            }
        }
    }

    fun reset() {
        page = 0
    }

    fun forwardMessage(
        sourceThreadId: String,
        sourceMessageIds: List<String>,
        threadId: String,
        partnerId: String = ""
    ) {
        val partnerIdToSend = if (partnerId == "0") {
            ""
        } else partnerId
        disposables.add(
            useCases.forwardMessage(
                sourceThreadId = sourceThreadId,
                sourceMessageIds = sourceMessageIds.sorted(),
                threadId = threadId,
                partnerId = if (threadId.isEmpty()) partnerIdToSend else ""
            )
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    {
                        action(ChatForwardMessageViewEvent.OnSendSuccess(threadId = threadId, userId = partnerId))
                    },
                    { error ->
                        handleError(error)
                    }
                )
        )
    }

    fun cancelSearch() {
        job?.cancel()
    }

    fun search(query: String, isLoadMore: Boolean = true) {
        if (query.isNotEmpty()) {
            if (!isLoadMore) {
                job?.cancel()
            }
            job = viewModelScope.launch {
                try {
                    // Launch both API calls concurrently
                    val threadDeferred = async {
                        searchConversationsUseCase.searchThread(
                            keyword = query,
                            pageNumber = searchPageNumber,
                            limit = Constant.LIMIT_SEARCH_PAGE
                        )
                    }

                    val subThreadDeferred = async {
                        searchConversationsUseCase.searchSubThread(
                            keyword = query,
                            pageNumber = searchPageNumber,
                            limit = Constant.LIMIT_SEARCH_PAGE
                        )
                    }

                    // Wait for both results
                    val threadResult = threadDeferred.await()
                    val subThreadResult = subThreadDeferred.await()
                    val threadData = arrayListOf<ConversationModel>()
                    // Process thread results
                    when (threadResult) {
                        is Result.Success -> {
                            val resultData = threadResult.data.filter { conversation ->
                                conversation.blockedBy.orEmpty().isEmpty() &&
                                    (conversation.isDirect || conversation.isGroup) &&
                                    (conversation.settings?.disableMemberSendMessage() == null || conversation.settings.disableMemberSendMessage() == false)
                            }
                            threadData.addAll(threadResult.data)
                            if (resultData.isNotEmpty()) {
                                searchPageNumber++
                            }
                            onSearchResult.postValue(resultData)
                        }
                        is Result.Error -> {
                            Timber.e(threadResult.exception)
                            onSearchResult.postValue(emptyList())
                        }
                    }

                    // Process subThread results
                    when (subThreadResult) {
                        is Result.Success -> {
                            val subThreadFilter = arrayListOf<ConversationModel>()
                            for (conversation in subThreadResult.data) {
                                val findThread = threadData.firstOrNull { it.id == conversation.parentId }
                                when (findThread) {
                                    null -> {
                                        subThreadFilter.add(conversation)
                                    }
                                    else ->
                                        if (findThread.settings?.disableMemberSendSubMessage() == false &&
                                            conversation.blockedBy.orEmpty().isEmpty() && (conversation.settings?.disableMemberSendSubMessage() == null || conversation.settings.disableMemberSendSubMessage() == false)
                                        ) {
                                            subThreadFilter.add(conversation)
                                        } else {
                                            // nothing
                                        }
                                }
                            }

                            if (subThreadFilter.isNotEmpty()) {
                                searchSubThreadPageNumber++
                            }
                            onSearchSubThreadResult.postValue(subThreadFilter)
                        }
                        is Result.Error -> {
                            Timber.e(subThreadResult.exception)
                            onSearchSubThreadResult.postValue(emptyList())
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    onSearchSubThreadResult.postValue(emptyList())
                    onSearchResult.postValue(emptyList())
                }
            }
        }
    }

    fun resetSearch() {
        searchPageNumber = 1
        searchSubThreadPageNumber = 1
    }

    fun uploadFile(item: ConversationModel?, uri: List<Uri>) {
        viewModelScope.launch(coroutineDispatchers.io) {
            try {
                val conversationId = item?.id ?: 0L
                val videoImageUri = arrayListOf<Uri>()
                val fileUris = arrayListOf<Uri>()
                uri.forEach {
                    val file = UriUtils.uri2FileNoCacheCopy(it)
                    when {
                        isVideoFile(file.path) || isImageFile(file.path) -> {
                            videoImageUri.add(it)
                        }

                        else -> {
                            fileUris.add(it)
                        }
                    }
                }
                viewModelScope.launch(coroutineDispatchers.main) {
                    if (fileUris.isNotEmpty()) {
                        sendFiles(conversationId = conversationId, fileUris)
                    }
                    if (videoImageUri.isNotEmpty()) {
                        sendMediaFromGallery(
                            media = videoImageUri,
                            conversationId = conversationId
                        )
                    }
                    onShowShareResult.value = true
                    action(ChatForwardMessageViewEvent.OnSendSuccess(threadId = item?.id.toString(), userId = item?.partner?.id.orEmpty()))
                }
            } catch (e: Exception) {
                Timber.e(e)
                onShowShareResult.postValue(false)
            }
        }
    }

    fun sendNewMetadataMessage(
        context: Context,
        messageData: ChatForwardMessageMetadata,
        item: ConversationModel?
    ) {
        Timber.e("messageMetadata $messageData")
        disposables.add(
            Flowable.fromCallable {
                val conversationId = item?.id.toString()
                val emitProgressFileUpload: EmitProgressFileUpload =
                    messageData.mapToEmitProgressFileUpload()
                return@fromCallable Triple(conversationId, messageData, emitProgressFileUpload)
            }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe({ tripe ->
                    // TODO a @HungNC send to worker. các type lấy ở [com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageMetadata.Type]
                    val conversationId = tripe.first
                    val messageData = tripe.second
                    val forwardData = tripe.third
                    if (messageData.type == ChatForwardMessageMetadata.Type.LINK && tripe.second.url.orEmpty()
                        .isNotEmpty()
                    ) {
                        val content = tripe.second.url.orEmpty()
                        val clientId = System.currentTimeMillis().toString()
                        val body = JsonObject()
                        body.addProperty("text", content)
                        body.addProperty("type", MessageType.TEXT.type)
                        SendMessageWorker.start(
                            clientId,
                            requestParams = body,
                            clientId = clientId,
                            conversationId = conversationId,
                            context = context,
                            message = Message()
                        )
                    } else {
                        UploadMediaWorker.start(
                            workerTag = System.currentTimeMillis().toString(),
//                            paths = arrayOf(),
//                            replyMess = 0,
                            clientId = System.currentTimeMillis().toString(),
                            conversationId = conversationId,
                            type = when (messageData.type) {
                                ChatForwardMessageMetadata.Type.IMAGE -> UploadMediaWorker.Companion.UploadType.IMAGE_OR_GIF
                                ChatForwardMessageMetadata.Type.VIDEO -> UploadMediaWorker.Companion.UploadType.VIDEO
                                ChatForwardMessageMetadata.Type.FILE -> UploadMediaWorker.Companion.UploadType.FILE
                                else -> UploadMediaWorker.Companion.UploadType.IMAGE_OR_GIF
                            },
                            forwardData = forwardData,
//                            isSendHD = true,
                            context = context
//                            message = Message()
                        )
                    }

                    onShowShareResult.value = true
                    action(ChatForwardMessageViewEvent.OnSendSuccess(threadId = item?.id.toString(), userId = item?.partner?.id.orEmpty()))
                }, {
                    Timber.e(it)
                })

        )
    }

    private fun isVideoFile(path: String?): Boolean {
        return FileVideoExtensionSupport.from(
            path.orEmpty().split(".").lastOrNull().orEmpty()
        ) != null
    }

    private fun isImageFile(path: String?): Boolean {
        return FilePhotoExtensionSupport.from(
            path.orEmpty().split(".").lastOrNull().orEmpty()
        ) != null
    }

    fun sendText(context: Context, item: ConversationModel?, content: String) {
        disposables.add(
            Flowable.fromCallable {
                val conversationId = item?.id
                return@fromCallable Pair(conversationId, content)
            }.subscribeOn(Schedulers.io())
                .map { pair ->
                    val conversationId = pair.first
                    val content = pair.second
                    val clientId = System.currentTimeMillis().toString()
                    val body = JsonObject()
                    body.addProperty("text", content)
                    body.addProperty("type", MessageType.TEXT.type)
                    SendMessageWorker.start(
                        clientId,
                        requestParams = body,
                        clientId = clientId,
                        conversationId = conversationId.toString(),
                        context = context,
                        message = Message()
                    )
                }
                .delay(250, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({
                    onShowShareResult.value = true
                    action(ChatForwardMessageViewEvent.OnSendSuccess(threadId = item?.id.toString(), userId = item?.partner?.id.orEmpty()))
                }, {
                    Timber.e(it)
                    onShowShareResult.value = false
                })
        )
    }

    private fun handleError(error: Throwable) {
        ErrorUtils.handleHttpError(error) { mess ->
            _snackbarErrorMessage.value = Event(mess)
        }
        Timber.e(error)
    }

    fun sendMultipleUsers(
        threadId: Long? = null,
        partnerId: Long? = null,
        message: String,
        isMarkdownText: Boolean
    ) {
        viewModelScope.launch {
            when (
                val result = createMessageMultipleTargetUseCase.invoke(
                    MessageMultipleTargetRequestModel(
                        body = MessageRequestModel.MessageBodyRequestModel(
                            isMarkdownText = isMarkdownText,
                            text = message,
                            type = MessageBodyType.TEXT
                        ),
                        threadId = threadId,
                        partnerId = partnerId
                    )
                )
            ) {
                is Result.Success -> {
                    result.data.messageId?.let {
                        action(ChatForwardMessageViewEvent.OnSendSuccess(threadId = threadId.toString(), userId = partnerId.toString()))
                    }
                }

                is Result.Error -> {
                    Timber.e(result.exception)
                    onShowShareResult.postValue(false)
                }
            }
        }
    }

    private fun sendFiles(conversationId: Long, uris: List<Uri>) {
        uris.forEach { uri ->
            val fileInfo = application.getFileNameAndSizeByUri(uri)
            val name = fileInfo.first
            val size = fileInfo.second
            val messageError = when {
                name.isUnSupportedFileExt() -> application.getString(GapoStrings.common_file_error_un_support)
                    .replaceByBrandingName(brandName)

                size != -1L && size > MessengerConstant.MAX_FILE_SIZE -> application.getString(
                    GapoStrings.common_file_error_max_length
                )

                else -> null
            }
            if (messageError != null) {
                action(ChatForwardMessageViewEvent.OnWarning(messageError))
            } else {
                val messageRequestModel = MessageRequestModel.buildMessageRequestFileType(
                    conversationId = conversationId,
                    uriFile = uri.toString(),
                    name = name,
                    size = size
                )
                createMessage(messageRequestModel)
            }
        }
    }

    private fun sendMediaFromGallery(
        media: List<Uri>,
        conversationId: Long,
        isSendHd: Boolean = true
    ) {
        viewModelScope.launch(coroutineDispatchers.io) {
            application.setUploadHighQualityEnabled(isSendHd)
            val mediaFile = try {
                media.mapNotNull {
                    if (it.toString().contains("miui")) {
                        ChatUtils.createFileFromInputStream(application, it)
                    } else {
                        UriUtils.uri2FileNoCacheCopy(it)
                    }
                }
            } catch (e: Exception) {
                emptyList()
            }
            launch(coroutineDispatchers.main) {
                val images = mediaFile.mapNotNull {
                    if (it.path.isVideo()) {
                        val messageRequestModel = MessageRequestModel.buildMessageRequestVideoType(
                            conversationId = conversationId,
                            pathVideo = it.path
                        )
                        createMessage(messageRequestModel)
                        null
                    } else {
                        it.path
                    }
                }
                if (images.isNotEmpty()) {
                    val messageRequestModel = MessageRequestModel.buildMessageRequestImageType(
                        conversationId = conversationId,
                        pathsImage = images,
                        isQualityHD = isSendHd
                    )
                    createMessage(messageRequestModel)
                }
            }
        }
    }

    private fun createMessage(
        messageRequestModel: MessageRequestModel
    ) {
        viewModelScope.launch {
            application.createMessage(
                messageRequestModel,
                saveMessageCreatorRequestUseCase,
                coroutineDispatchers
            )
        }
    }

    private fun action(event: ChatForwardMessageViewEvent) {
        _viewEvent.value = com.gg.gapo.core.utilities.livedata.Event(event)
    }

    fun createThread(
        conversationModel: ConversationModel?,
        userId: String
    ) {
        val inputData = JsonObject()
        inputData.addProperty(
            "type",
            Constant.ThreadType.DIRECT.type
        )

        val participants = JsonArray()
        participants.add(userId)
        inputData.add("participant_ids", participants)
        val requestBody = inputData.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val subscription = conversationUseCase.createThread(requestBody)
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .subscribe(
                { result ->
                    onSendResult.postValue(conversationModel?.copy(id = result.id.toLong()))
                },
                { error ->
                    handleError(error)
                }
            )
        disposables.add(subscription)
    }
}

// TODO tạm thời convert 1 số field sang code cũ để dùng
internal fun ConversationModel.mapToConversationOldVersion(
    context: Context
): Conversation {
    // map name cua subthread
    val size = 20
    val name = if (type != ConversationType.SUB_THREAD) {
        name
    } else {
        val conversationName = if (name.length > size) "${name.substring(0, size)}..." else name
        val referencedMessageName =
            com.gg.gapo.feature.messenger.presentation.helper.ChatUtils.getLastMessageString(
                context,
                referencedMessage,
                "",
                type.type,
                false
            ).let {
                if (it.length > size) "${it.substring(0, size)}..." else it
            }
        "[$conversationName] » $referencedMessageName"
    }
    return Conversation(
        id = this.id.toString(),
        groupLevel = if (this.groupLevel == ConversationGroupLevel.PRIVATE) GroupLevel.PRIVATE else GroupLevel.PUBLIC,
        type = this.type.type,
        isLock = false,
        lastMessage = Message(
            id = "",
            body = Body(),
            sender = Sender(),
            createdAt = this.lastMessage?.createdAt ?: 0L,
            userId = "",
            deliverStatus = 0,
            deleted = false,
            conversationId = "",
            conversationType = Constant.ThreadType.DIRECT.type,
            conversationName = "",
            conversationAvatar = "",
            clientId = "",
            status = -1,
            enableNotify = Constant.FlashMessageNotification.ENABLE.status,
            isShowTimeHeader = false,
            isShowSeenAvatar = false,
            folder = Constant.Folder.DEFAULT.type,
            eventId = "",
            react = MessageReact(),
            read = arrayListOf(),
            replyToMsgObject = ReplyMessageObject(),
            pinnedAt = 0
        ),
        name = name,
        description = this.description.orEmpty(),
        avatar = this.avatar ?: "",
        totalMessage = 0,
        readCount = 0,
        partnerId = "",
        statusVerify = StatusVerify.fromInt(this.partner?.statusVerify?.statusVerify),
        blockedBy = this.blockedBy,
        lastSeen = 0,
        unreadMessage = this.unReadCount,
        unReadCount = this.unReadCount,
        partner = Partner(
            avatar = this.partner?.avatar.orEmpty(),
            id = this.partner?.id.orEmpty(),
            name = this.partner?.name.orEmpty(),
            readCount = this.partner?.readCount ?: 0,
            statusVerify = StatusVerify.fromInt(this.partner?.statusVerify?.statusVerify),
            type = this.partner?.type?.type.orEmpty()
        ),
        lastMessageCreateAt = lastMessage?.createdAt,
        roleId = this.role.role,
        link = "",
        pinnedMessageId = "",
        role = UserRole.fromString(this.role.role),
        enableNotify = EnableNotify.fromInt(this.enableNotify.enableNotify) ?: EnableNotify.ON,
        memberCount = memberCount,
        banCount = 0,
        folder = Constant.Folder.DEFAULT.type,
        banner = BannerContact(),
        pinnedAt = 0,
        pinnedCount = 0,
        associateLink = this.associateLink,
        settings = Settings(
            disableMemberSendMessage = this.settings?.disableMemberSendMessage ?: BooleanValue.FALSE.value,
            isPublic = this.settings?.isPublic ?: BooleanValue.FALSE.value,
            deleteMsgAfterDays = this.settings?.deleteMsgAfterDays ?: 0,
            public = this.settings?.isPublic == BooleanValue.TRUE.value,
            needApprove = this.settings?.disableMemberSendMessage == BooleanValue.TRUE.value,
            sharePublicLink = false,
            disableMemberSendSubMessage = this.settings?.disableMemberSendSubMessage ?: BooleanValue.FALSE.value
        ),
        departments = departments.orEmpty(),
        parentThreadType = parentThreadType?.type ?: "",
        collabId = collabId.orEmpty(),
        status = partner?.statusVerify?.statusVerify ?: 1,
        referencedMessage = null
    )
}
