package com.gg.gapo.messenger.presentation.features.chat

import android.app.Application
import androidx.annotation.StringRes
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.gg.gapo.core.eventbus.fcm.FcmDeleteInboxMessageEvent
import com.gg.gapo.core.onpremise.manager.OnPremiseManager
import com.gg.gapo.messenger.data.sources.models.media.MediaData
import com.gg.gapo.messenger.data.sources.models.media.MediaType
import com.gg.gapo.messenger.domain.models.*
import com.gg.gapo.messenger.domain.usecases.*
import com.gg.gapo.messenger.helper.Constant
import com.gg.gapo.messenger.helper.extensions.SingleLiveEvent
import com.gg.gapo.messenger.helper.utils.ChatUtils
import com.gg.gapo.messenger.helper.utils.CommonUtils
import com.gg.gapo.messenger.helper.utils.ParseUtils
import com.gg.gapo.messenger.helper.utils.ToastUtils
import com.gg.gapo.messenger.presentation.common.ErrorUtils
import com.gg.gapo.messenger.presentation.common.Event
import com.gg.gapo.messenger.presentation.common.StickerType
import com.gg.gapo.messenger.presentation.features.chat.bases.ChatCommonState
import com.gg.gapo.messenger.presentation.features.chat.bases.ChatCommonViewModel
import com.gg.gapo.messenger.presentation.features.managers.UserChatManager
import com.gg.gapo.messenger.room.ConversationDao
import com.gg.gapo.messenger.workers.SendMessageWorker
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import io.reactivex.Completable
import io.reactivex.Flowable
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import retrofit2.HttpException
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.TimeUnit

class ChatViewModel constructor(
    private val context: Application,
    private val userUseCase: UserUseCase,
    private val messageUseCase: MessageUseCase,
    private val conversationUseCase: ConversationUseCase,
    private val conversationDao: ConversationDao,
    private val userManager: UserChatManager,
    private val draftMessageUseCase: DraftMessageUseCase,
    private val errorMessageUseCase: ErrorMessageUseCase,
    private val onPremiseManager: OnPremiseManager
) : ChatCommonViewModel(
    application = context,
    messageUseCase = messageUseCase,
    conversationUseCase = conversationUseCase,
    errorMessageUseCase = errorMessageUseCase,
    userUseCase = userUseCase,
    draftMessageUseCase = draftMessageUseCase,
    conversationDao = conversationDao
) {

    val brandName get() = onPremiseManager.onPremiseConfigData?.brandName.orEmpty()

    private val _stateDirect = MutableLiveData<ChatDirectViewState>()
    val stateDirect: LiveData<ChatDirectViewState>
        get() = _stateDirect

    private val userOnlineStatus = MutableLiveData<Boolean>()
    private val userLastSeen = MutableLiveData<Long>()
    private val lastParams = CopyOnWriteArrayList<Pair<Int, Int>>()

    val toggleSecretChat = MutableLiveData<Pair<String, Boolean>>()
    var threadToggleNotify: SingleLiveEvent<String> = SingleLiveEvent()
    var isBlockByPartner: SingleLiveEvent<Boolean> = SingleLiveEvent()
    var createThreadError: SingleLiveEvent<String> = SingleLiveEvent()
    var isTriggerLockScreen: MutableLiveData<Boolean> = MutableLiveData()
    val onCleared = SingleLiveEvent<Boolean>()
    val resultMessages = MutableLiveData<List<Message>>()

    fun createThread(
        partner: ChatUser,
        conversationType: String,
        passCode: String = Constant.EMPTY
    ) {
        val inputData = JsonObject()
        inputData.addProperty(
            "type",
            Constant.ThreadType.DIRECT.type
        )

        val participants = JsonArray()
        participants.add(partner.userId)
        inputData.add("participant_ids", participants)
        if (passCode.isNotEmpty()) {
            inputData.addProperty("pass_code", passCode)
        }

        val requestBody = inputData.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val subscription = conversationUseCase.createThread(requestBody)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { result ->

                    onCreateDirectSuccess(result)
                },
                { error ->
                    onCreateDirectError(error)
                }
            )
        disposables.add(subscription)
    }

    private fun onCreateDirectSuccess(conversation: Conversation) {
        _state.value = ChatCommonState.Thread(thread = conversation)
        updateLocalConversation(conversation)
    }

    private fun onCreateDirectError(error: Throwable) {
        if (error is HttpException) {
            error.response()?.errorBody()?.let {
                val code = ServerError.parse(it)
                _state.postValue(ChatCommonState.Error(it, code.code()))
            }
        } else {
            ErrorUtils.handleHttpError(error) { it ->
                _state.postValue(ChatCommonState.Error(it))
            }
        }
    }

    fun getMessages(
        conversationId: String,
        from: Int,
        to: Int,
        isFirstRequest: Boolean,
        replyMessageId: String,
        passCode: String
    ) {
        if (isFirstRequest) {
            lastParams.clear()
        }
        if (lastParams.contains(Pair(from, to))) return
        lastParams.add(Pair(from, to))

        val queryMap = mutableMapOf<String, Any>()
        queryMap["thread_id"] = conversationId
        queryMap["from"] = from
        queryMap["to"] = to
        if (CommonUtils.isNotNullAndEmpty(passCode)) {
            queryMap["pass_code"] = passCode
        }
        val subscription = Flowable.just(queryMap)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeOn(Schedulers.io())
            .flatMap { params -> messageUseCase.getMessages(params) }
            .flatMapIterable { it }
            .map { handleMessage(it) }
            .toList()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { result ->
                    onGetMessagesSuccess(
                        result,
                        isFirstRequest,
                        replyMessageId
                    )
                },
                { error ->
                    lastParams.removeAll { it.toString() == Pair(from, to).toString() }
                    onGetMessagesError(error)
                }
            )
        disposables.add(subscription)
    }

    private fun handleMessage(it: Message): Message {
        if (CommonUtils.convertStringToIntSafety(it.body.replyToMsg) > 0 && it.replyToMsgObject.sender.id.isEmpty()) {
            val replyMap = mutableMapOf<String, Any>()
            replyMap["thread_id"] = it.conversationId
            if (userManager.isExistPassCode()) replyMap["pass_code"] = userManager.getCurrentPassCode()
            disposables.add(
                messageUseCase.getMessage(it.body.replyToMsg, replyMap).subscribe({
                    Timber.d("success it.body.replyToMsg ${it.body.replyToMsg}")
                }, { Timber.e(it) })
            )
        }
        return it
    }

    private fun getLatestMessages(queryMap: MutableMap<String, Any>) {
        _state.postValue(ChatCommonState.Loading(true))
        val subscription = messageUseCase.getLocalLastedMessages(queryMap)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { result ->
                    if (result.isNotEmpty()) {
                        _state.postValue(ChatCommonState.LastedMessageList(result))
                    }
                    _state.value = ChatCommonState.Loading(false)
                },
                { error ->
                    _state.postValue(ChatCommonState.Loading(false))
                    handleError(error)
                    Timber.e(error)
                }
            )
        disposables.add(subscription)
    }

    fun sendSticker(
        url: String,
        clientId: String,
        conversationId: String,
        replyToMsg: Int,
        passCode: String,
        type: Int,
        message: Message
    ) {
        val body = JsonObject()
        body.addProperty("text", Constant.EMPTY)
        body.addProperty(
            "type",
            if (type == StickerType.ANIMATED.type) MessageType.ANIMATED_STICKER.type
            else MessageType.STICKER.type
        )
        if (replyToMsg >= 0) {
            body.addProperty("reply_to_msg", replyToMsg)
        }
        val medias = JsonArray()
        medias.add(url)
        body.add("media", medias)
        sendMessage(clientId, conversationId, body, message)
    }

    private fun onGetMessagesSuccess(
        messages: List<Message>,
        isFirstRequest: Boolean,
        replyMessageId: String
    ) {
        if (messages.isNotEmpty()) {
            if (replyMessageId.isNotEmpty()) {
                _state.postValue(ChatCommonState.ScrollToMessage(messages, replyMessageId))
            } else {
                resultMessages.value = messages
                if (isFirstRequest) {
                    _stateDirect.value = ChatDirectViewState.ScrollToBottom
                }
            }
        } else if (isFirstRequest) {
            _stateDirect.postValue(ChatDirectViewState.ShowSayHi)
        }
    }

    private fun onGetMessagesError(error: Throwable) {
        handleError(error)
        Timber.e(error)
    }

    fun sendMessage(
        clientId: String,
        conversationId: String,
        data: JsonObject,
        message: Message,
        editMessageId: String = ""
    ) {
        SendMessageWorker.start(
            clientId,
            requestParams = data,
            clientId = clientId,
            conversationId = conversationId,
            context = context,
            message = message,
            editMessageId = editMessageId
        )
    }

    fun getPartnerStatusWorker(id: String) {
        val disposable = Observable.interval(
            0,
            1,
            TimeUnit.MINUTES
        )
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { getOnlineStatus(id) },
                { Timber.d("Init Partner Worker Error") }
            )
        disposables.add(disposable)
    }

    fun getOnlineStatus(userId: String) {
        if (userId.isNotEmpty()) {
            val queryMap = mutableMapOf<String, String>()
            queryMap["uids"] = userId
            val subscription = userUseCase.getListOnline(queryMap)
                .subscribeOn(Schedulers.io())
                .map {
                    val data = ParseUtils.optJsonArray(it, "data")
                    if (data.size() > 0) {
                        val json = data[0].asJsonObject
                        val lastSeen = ParseUtils.optLong(json, "seen_at")
                        return@map lastSeen
                    }
                    return@map 0L
                }
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    { result -> onGetOnlineStatusSuccess(result) },
                    { Timber.d("GetOnlineStatus Error") }
                )
            disposables.add(subscription)
        }
    }

    private fun onGetOnlineStatusSuccess(lastSeen: Long) {
        userLastSeen.value = lastSeen
        if (lastSeen == 0L) {
            userOnlineStatus.value = false
        } else {
            userOnlineStatus.value = System.currentTimeMillis() - lastSeen <= Constant.ONLINE_STATUS_MAX_TIME_2_MINUTE
        }
    }

    fun onReceivedMessage(
        message: Message,
        conversationId: String,
        isRetain: Boolean
    ) {
        val listMessage = ArrayList<Message>()
        listMessage.add(message)
        setMessages(listMessage)

        if (!isRetain && conversationId == message.conversationId) {
            cacheMediaList(arrayListOf(message))
        }
    }

    fun userStatus() = userOnlineStatus

    fun userLastSeen() = userLastSeen

    override fun mediaCacheList() = mediaList

    fun getDestinationId(from: Int, limit: Int): Int {
        return if (from - limit <= 0) {
            0
        } else {
            from - limit
        }
    }

    private fun handleError(error: Throwable) {
        when (error) {
            is HttpException -> {
                val responseBody = error.response()?.errorBody()
                if (responseBody != null) {
                    val serverError = ServerError.parse(responseBody)
                    when (error.response()?.code()) {
//                        401 -> {
//                        }
                        403 -> {
                            if (serverError.code() == 11) {
                                isBlockByPartner.value = true
                            }

                            if (serverError.code() == 3) {
                                isTriggerLockScreen.value = true
                            }

                            serverError.message()?.let {
                                createThreadError.value = it
                            }
                        }
                        429 -> {
                            serverError.message()?.let {
                                createThreadError.value = it
                            }
                        }
                        else -> {
                            serverError.message()?.let {
                                ToastUtils.show(context, it)
                            }
                        }
                    }
                }
            }
        }
    }

    fun clearHistory(conversationId: String) {
        if (conversationId.isNotEmpty() && conversationId.toLongOrNull() != null) {
            val disposable = conversationUseCase.clearHistory(conversationId)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    {
                        deleteAllMessages(conversationId)
                    },
                    { Timber.d("Clear history success") }
                )
            disposables.add(disposable)
        }
    }

    fun deleteHistory(conversationId: String) {
        val disposable = conversationUseCase.delete(conversationId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                {
                    deleteAllMessages(conversationId)
                },
                { Timber.d("Delete history success") }
            )
        disposables.add(disposable)
    }

    private fun deleteAllMessages(conversationId: String) {
        val disposable =
            Completable.fromCallable {
                messageUseCase.deleteAllMessages(conversationId)
            }
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    {
                        onCleared.value = true
                        Timber.d("Delete All Message Success $conversationId")
                    },
                    { Timber.d("Delete All Message Failure") }
                )
        disposables.add(disposable)
    }

    fun blockUser(requestBody: RequestBody) {
        val disposable = userUseCase.blockUser(requestBody)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { Timber.d("Block user success") },
                { Timber.d("Block user failure") }
            )
        disposables.add(disposable)
    }

    fun threadToggleNotify(
        threadId: String,
        @StringRes messageOnSuccess: Int,
        passCode: String = Constant.EMPTY
    ) {
        disposables.add(
            conversationUseCase.threadToggleNotify(threadId)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    {
                        threadToggleNotify.value = threadId
                        _snackbarAlertMessage.value =
                            Event(messageOnSuccess)
                    },
                    {
                        CommonUtils.handleHttpError(context, it)
                        Timber.d("Update fail")
                    }
                )
        )
    }

    fun toggleConversationSecret(threadId: String, folder: String, passCode: String) {
        val putData = JsonObject()
        putData.addProperty("to_folder", folder)
        putData.addProperty("pass_code", passCode)
        val requestBody = putData.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val subscription = conversationUseCase.toggleThreadSecret(threadId, requestBody)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { toggleSecretChat.value = Pair(folder, true) },
                {
                    toggleSecretChat.value = Pair(folder, false)
                    handleError(it)
                }
            )
        disposables.add(subscription)
    }

    fun toggleConversationDefault(threadId: String) {
        val putData = JsonObject()
        putData.addProperty("to_folder", Constant.Folder.DEFAULT.type)
        val requestBody = putData.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val subscription = conversationUseCase.toggleThreadSecret(threadId, requestBody)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                {
                    _stateDirect.postValue(ChatDirectViewState.StrangerAccepted)
                },
                {
                    handleError(it)
                }
            )
        disposables.add(subscription)
    }

    fun cacheMediaList(messages: List<Message>) {
        disposables.add(
            Flowable.just(messages)
                .subscribeOn(Schedulers.io())
                .flatMapIterable { it }
                .filter {
                    return@filter MessageType.VIDEO.name.equals(
                        it.body.type,
                        true
                    ) || MessageType.IMAGE.name.equals(
                        it.body.type,
                        true
                    ) || MessageType.STORY.name.equals(
                        it.body.type,
                        true
                    ) || MessageType.MULTI_IMAGE.name.equals(it.body.type, true)
                }.map { message ->
                    val urls = ArrayList<Pair<String, String?>>() // <src, thumb>
                    if (MessageType.VIDEO.name.equals(message.body.type, true)) {
                        urls.add(Pair(ChatUtils.getSingleMediaCdn(message.body.metadata.videoInformation), null))
                    } else {
                        message.body.media.forEachIndexed { index, media ->
                            urls.add(Pair(media, message.body.metadata.imageThumb.getOrNull(index)?.url))
                        }
                    }
                    val messageId = message.id
                    val isVideo = MessageType.VIDEO.name.equals(message.body.type, true)
                    val medias = ArrayList<MediaData>()

                    urls.filter { it.first.startsWith("http") }.forEach {
                        val index = mediaList.indexOfFirst { data -> it.first.equals(data.src, true) }
                        if (index == -1) {
                            medias.add(
                                MediaData(
                                    id = messageId,
                                    createdAt = System.currentTimeMillis(),
                                    type = if (isVideo) {
                                        MediaType.VIDEO.type
                                    } else MediaType.IMAGE.type,
                                    videoId = "",
                                    duration = 0,
                                    store = "",
                                    path = "",
                                    src = it.first,
                                    thumbPattern = it.second,
                                    availableFormat = null,
                                    thumb = null,
                                    imageId = "",
                                    width = 0,
                                    height = 0,
                                    srcFromLocal = 0,
                                    amount = null
                                )
                            )
                        }
                    }
                    return@map medias
                }.flatMapIterable {
                    it
                }.toList()
                .map {
                    it.addAll(mediaList)
                    return@map it.sortedWith(compareBy({ it.id }, { it.createdAt }))
                }
                .observeOn(Schedulers.io())
                .subscribe({
                    mediaList.clear()
                    mediaList.addAll(it)
                }, { e -> Timber.e(e) })
        )
    }

    fun clearCachedMessage(threadId: String?) {
        threadId?.let {
            disposables.add(
                Flowable.fromCallable {
                    messageUseCase.deleteAllMessages(threadId)
                }.subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                        {
                            Timber.d("Clear All")
                        },
                        {
                            Timber.e(it)
                        }
                    )
            )
            viewModelScope.launch {
                EventBus.getDefault().post(FcmDeleteInboxMessageEvent(it))
            }
        }
    }

    fun getBotCommands(id: String) {
        disposables.add(
            messageUseCase.getBotCommands(
                botId = id
            )
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    {
                        _stateDirect.value = ChatDirectViewState.BotCommands(it)
                    },
                    {
                        Timber.e(it)
                        ErrorUtils.handleHttpError(it) {
                            _state.postValue(ChatCommonState.Error(it))
                        }
                    }
                )
        )
    }
}
