package com.gg.gapo.messenger.presentation.features.forward

import android.content.Context
import com.airbnb.epoxy.AsyncEpoxyController
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.feature.messenger.domain.messenger.model.conversation.ConversationModel
import com.gg.gapo.messenger.presentation.features.conversation.models.ConversationTitleModel_
import com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageItemModel
import com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageItemModel_
import com.gg.gapo.messenger.presentation.features.forward.models.ChatForwardMessageSearchEmptyItemModel_
import org.koin.core.component.KoinComponent
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean

internal class ForwardMessageController(
    private val context: Context,
    private val listener: ForwardMessageControllerListener,
    private val myUserId: String,
    private val isSubThread: AtomicBoolean = AtomicBoolean(false)
) : AsyncEpoxyController(), KoinComponent {

    companion object {
        private val ID_SEARCH_BAR = System.currentTimeMillis()
        private val ID_NEARBY = ID_SEARCH_BAR + 1
        private val ID_GROUP = ID_NEARBY + 1
        private val ID_MEMBER = ID_GROUP + 1
        private val ID_SEARCH_EMPTY = ID_MEMBER + 1
    }

    private val threads = CopyOnWriteArrayList<ConversationModel>()
    private val subThreads = CopyOnWriteArrayList<ConversationModel>()

    private val searchResult = CopyOnWriteArrayList<ConversationModel>()
    private val searchSubThreadResult = CopyOnWriteArrayList<ConversationModel>()

    private val sent = ArrayList<String>()
    private val sending = ArrayList<String>()

    private val isSearching = AtomicBoolean(false)

    fun setShowSubThread(isSubThread: Boolean) {
        this.isSubThread.set(isSubThread)
        requestModelBuild()
    }

    private val itemListener = object : ChatForwardMessageItemModel.ForwardItemListener {
        override fun onSend(item: ConversationModel?) {
            if (item != null) {
                if (!sending.contains(item.id.toString())) {
                    sending.add(item.id.toString())
                }
                listener.onSend(item)
            }
        }
    }

    init {
        setFilterDuplicates(true)
    }

    override fun buildModels() {
        val indexGroup = 4
        val conversations = if (isSubThread.get()) {
            subThreads
        } else threads.distinctBy { it.id }

        if (!isSearching.get()) {
            val shouldShowGroup = conversations.firstOrNull { it.isGroup } != null && !isSubThread.get()
            conversations.forEachIndexed { index, conversation ->
                when {
                    index == 0 && !isSubThread.get() -> {
                        ConversationTitleModel_()
                            .id(ID_NEARBY)
                            .title(context.getString(GapoStrings.messenger_forward_nearby))
                            .addTo(this)
                    }

                    index == indexGroup -> {
                        ConversationTitleModel_()
                            .id(ID_GROUP)
                            .title(context.getString(GapoStrings.messenger_forward_group))
                            .addIf(shouldShowGroup, this)
                    }

                    (index > indexGroup) && conversation.isDirect && !isSubThread.get() -> {
                        ConversationTitleModel_()
                            .id(ID_MEMBER)
                            .title(context.getString(GapoStrings.messenger_forward_friends))
                            .addIf(conversation.isDirect, this)
                    }
                }

                ChatForwardMessageItemModel_()
                    .id(conversation.id)
                    .listener(itemListener)
                    .sent(
                        sent.contains(conversation.id.toString()) || (
                            conversation.partner?.id.orEmpty()
                                .isNotEmpty() && sent.contains(conversation.partner?.id)
                            )
                    )
                    .sending(
                        sending.contains(conversation.id.toString()) || (
                            conversation.partner?.id.orEmpty()
                                .isNotEmpty() && sending.contains(conversation.partner?.id)
                            )
                    )
                    .conversation(conversation)
                    .content(
                        conversation.highlight(
                            currentUserChatId = myUserId
                        )
                    )
                    .subThread(isSubThread.get() && conversation.isSubthread == true)
                    .addIf(
                        if (isSubThread.get()) {
                            conversation.isSubthread == true && conversation.referencedMessage != null
                        } else {
                            true
                        },
                        this
                    )
            }
        } else {
            val searchResult = if (isSubThread.get()) {
                searchSubThreadResult
            } else {
                searchResult.distinctBy { it.id }
            }

            searchResult.forEachIndexed { _, conversation ->
                ChatForwardMessageItemModel_()
                    .id(conversation.id)
                    .listener(itemListener)
                    .sent(
                        sent.contains(conversation.id.toString()) || (
                            conversation.partner?.id.orEmpty()
                                .isNotEmpty() && sent.contains(conversation.partner?.id)
                            )
                    )
                    .sending(
                        sending.contains(conversation.id.toString()) || (
                            conversation.partner?.id.orEmpty()
                                .isNotEmpty() && sending.contains(conversation.partner?.id)
                            )
                    )
                    .conversation(conversation)
                    .content(
                        conversation.highlight(
                            currentUserChatId = myUserId
                        )
                    )
                    .subThread(isSubThread.get() && conversation.isSubthread == true)
                    .addIf(
                        if (isSubThread.get()) {
                            conversation.isSubthread == true && conversation.referencedMessage != null
                        } else {
                            conversation.isSubthread == false
                        },
                        this
                    )
            }
            ChatForwardMessageSearchEmptyItemModel_().id(ID_SEARCH_EMPTY)
                .addIf(isSearching.get() && searchResult.isEmpty(), this)
        }
    }

    fun hasSearch(query: String) {
        isSearching.set(query.isNotEmpty())
        requestModelBuild()
    }

    fun addConversation(data: List<ConversationModel>) {
        threads.addAll(data)
        requestModelBuild()
    }

    fun addSearchResult(data: List<ConversationModel>) {
        searchResult.addAll(data)
        requestModelBuild()
    }

    fun addSubThreads(data: List<ConversationModel>) {
        subThreads.addAll(data)
        requestModelBuild()
    }

    fun addSearchSubThreadResult(data: List<ConversationModel>) {
        searchSubThreadResult.addAll(data)
        requestModelBuild()
    }

    fun updateSent(threadId: String, userId: String) {
        val indexThread = sent.firstOrNull { it == threadId }
        val indexUserId = sent.firstOrNull { it == userId }
        if (indexThread == null) {
            sent.add(threadId)
        }
        if (indexUserId == null) {
            sent.add(userId)
        }
        sending.remove(threadId)
        sending.remove(userId)
        requestModelBuild()
    }

    fun clearSearch() {
        isSearching.set(false)
        searchResult.clear()
        searchSubThreadResult.clear()
        requestModelBuild()
    }

    private fun ConversationModel.highlight(
        currentUserChatId: String = ""
    ): String {
        return com.gg.gapo.feature.messenger.presentation.helper.ChatUtils.getLastMessageString(
            context = context,
            lastMessage = lastMessage,
            userId = currentUserChatId,
            conversationType = type.type,
            isShowName = true
        )
    }

    interface ForwardMessageControllerListener {
        fun onSearchBarClicked()
        fun onSend(item: ConversationModel?)
    }
}
