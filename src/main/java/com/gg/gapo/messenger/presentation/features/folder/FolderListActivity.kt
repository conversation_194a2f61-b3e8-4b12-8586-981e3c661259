package com.gg.gapo.messenger.presentation.features.folder

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import androidx.navigation.fragment.NavHostFragment
import com.gg.gapo.core.ui.activity.GapoThemeBaseActivity
import com.gg.gapo.messenger.R
import com.gg.gapo.messenger.domain.models.FolderModel
import com.gg.gapo.messenger.presentation.features.folder.create.FolderCreationFragment

class FolderListActivity : GapoThemeBaseActivity() {

    val selected = MutableLiveData<LinkedHashMap<String, Any>>(LinkedHashMap())
    val isBackToChatList: Boolean by lazy {
        intent.hasExtra(TAG)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_folder_list)
        if (intent.hasExtra(TAG)) {
            val folderModel = intent.getParcelableExtra<FolderModel>(TAG)
            val navHostFragment =
                supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
            val navController = navHostFragment.navController
            navController.navigate(
                FolderListFragmentDirections.actionFolderListFragmentToFolderCreationFragment(
                    folderModel
                )
            )
        }
    }

    companion object {
        const val TAG = "FolderListActivity"

        fun start(
            context: Context,
            folder: FolderModel? = null
        ) {
            context.startActivity(
                Intent(context, FolderListActivity::class.java).apply {
                    if (folder != null) {
                        this.putExtra(TAG, folder)
                    }
                }
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        supportFragmentManager.fragments.forEach {
            it.onRequestPermissionsResult(requestCode, permissions, grantResults)
        }
    }

    override fun onBackPressed() {
        val nav =
            (supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment).navController

        val isLastScreen = nav.currentBackStackEntry?.destination?.label.toString().orEmpty().contains(FolderListFragment.TAG)
        val isEditScreen = nav.currentBackStackEntry?.destination?.label.toString().orEmpty().contains(
            FolderCreationFragment.TAG
        )
        if (isBackToChatList && isEditScreen) {
            finish()
        } else if (isLastScreen) {
            finish()
        } else {
            nav.popBackStack()
        }
    }
}
