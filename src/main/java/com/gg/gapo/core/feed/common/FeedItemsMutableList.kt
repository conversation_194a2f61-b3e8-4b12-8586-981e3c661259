package com.gg.gapo.core.feed.common

import android.content.Context
import android.widget.Toast
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentCreatedOrUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.comment.FeedCommentDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostMediaDeletedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostSharedBusEvent
import com.gg.gapo.core.eventbus.feed.post.FeedPostUpdatedBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadCanceledBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadedBusEvent
import com.gg.gapo.core.eventbus.feed.post.attachment.FeedPostAttachmentDownloadingBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadedBusEvent
import com.gg.gapo.core.eventbus.feed.post.comment.attachment.FeedPostCommentAttachmentDownloadingBusEvent
import com.gg.gapo.core.eventbus.feed.post.pollvote.FeedPostPollExpiredBusEvent
import com.gg.gapo.core.eventbus.feed.post.pollvote.FeedPostPollVoteCreatedBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.feed.item.FeedViewData
import com.gg.gapo.core.feed.item.announcement.interactor.FeedAnnouncementPostListInteractor
import com.gg.gapo.core.feed.item.announcement.model.FeedAnnouncementPostsListViewData
import com.gg.gapo.core.feed.item.empty.model.FeedEmptyViewData
import com.gg.gapo.core.feed.item.findfriend.model.FeedFindFriendViaContactViewData
import com.gg.gapo.core.feed.item.group.details.header.model.FeedGroupDetailsHeaderViewData
import com.gg.gapo.core.feed.item.group.details.pinnedrow.model.FeedGroupDetailsPinnedPostRowViewData
import com.gg.gapo.core.feed.item.group.mine.model.FeedGroupMyGroupEmptyViewData
import com.gg.gapo.core.feed.item.group.mine.model.FeedGroupMyGroupsListViewData
import com.gg.gapo.core.feed.item.onboarding.model.FeedOnboardingsListViewData
import com.gg.gapo.core.feed.item.placeholder.model.FeedPlaceholderViewData
import com.gg.gapo.core.feed.item.post.FeedPostViewData
import com.gg.gapo.core.feed.item.post.announcement.summary.interactor.FeedPostAnnouncementSummaryInteractor
import com.gg.gapo.core.feed.item.post.announcement.summary.model.FeedPostAnnouncementSummaryViewData
import com.gg.gapo.core.feed.item.post.attachment.model.FeedPostAttachmentViewData
import com.gg.gapo.core.feed.item.post.attachment.model.FeedPostAttachmentsListViewData
import com.gg.gapo.core.feed.item.post.comment.model.FeedPostCommentContentViewData
import com.gg.gapo.core.feed.item.post.footer.model.FeedPostFooterViewData
import com.gg.gapo.core.feed.item.post.header.model.FeedCombinePostHeaderViewData
import com.gg.gapo.core.feed.item.post.header.model.FeedPostHeaderViewData
import com.gg.gapo.core.feed.item.post.media.model.FeedPostMediaListViewData
import com.gg.gapo.core.feed.item.post.more.model.FeedCombinePostViewMoreViewData
import com.gg.gapo.core.feed.item.post.pollvote.model.FeedPostPollVoteListViewData
import com.gg.gapo.core.feed.item.post.question.model.FeedPostQuestionViewData
import com.gg.gapo.core.feed.item.post.text.model.FeedPostTextViewData
import com.gg.gapo.core.feed.item.reachedend.model.FeedReachedEndViewData
import com.gg.gapo.core.feed.item.shimmer.model.FeedPostShimmerViewData
import com.gg.gapo.core.feed.item.story.model.FeedStoriesListViewData
import com.gg.gapo.core.feed.item.suggestion.friend.model.FeedSuggestedFriendViewData
import com.gg.gapo.core.feed.item.suggestion.friend.model.FeedSuggestedFriendsListViewData
import com.gg.gapo.core.feed.item.suggestion.group.model.FeedSuggestedGroupViewData
import com.gg.gapo.core.feed.item.suggestion.group.model.FeedSuggestedGroupsListViewData
import com.gg.gapo.core.feed.item.uploading.model.FeedPostUploadingViewData
import com.gg.gapo.core.feed.item.user.achievement.model.FeedUserProfileAchievementsListViewData
import com.gg.gapo.core.feed.item.user.album.model.FeedUserProfilePhotoAlbumListViewData
import com.gg.gapo.core.feed.item.user.friend.model.FeedUserProfileFriendsListViewData
import com.gg.gapo.core.feed.item.user.profile.model.FeedUserProfileViewData
import com.gg.gapo.core.feed.post.domain.PostCommentModel
import com.gg.gapo.core.feed.post.domain.PostModel
import com.gg.gapo.core.feed.post.domain.PostReactType
import com.gg.gapo.core.feed.post.domain.PostUserModel
import com.gg.gapo.core.feed.usecase.post.domain.usecase.PostUnVoteUseCase
import com.gg.gapo.core.feed.usecase.post.domain.usecase.PostVoteUseCase
import com.gg.gapo.core.feed.usecase.post.exception.PostPollExpiredException
import com.gg.gapo.core.feed.usecase.post.exception.PostYouHaveAlreadyVotedException
import com.gg.gapo.core.feed.usecase.post.exception.PostYouHaveNotVotedYetException
import com.gg.gapo.core.feed.usecase.react.domain.usecase.ReactCommentUseCase
import com.gg.gapo.core.feed.usecase.react.domain.usecase.ReactPostUseCase
import com.gg.gapo.core.ui.toast.GapoToast
import com.gg.gapo.core.utilities.result.Result
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 09/05/2021
 */
open class FeedItemsMutableList : ArrayList<FeedViewData>() {

    private val posts = mutableListOf<PostModel>()

    private val announcementPosts = mutableListOf<PostModel>()

    val postSize: Int
        get() = posts.size

    val isPostEmpty: Boolean
        get() = posts.isEmpty()

    val liveData = FeedViewData.LiveData()

    fun setLiveData(shallowCopy: Boolean = true) = liveData.setValue(this, shallowCopy)

    fun postLiveData(shallowCopy: Boolean = true) = liveData.postValue(this, shallowCopy)

    override fun clear() {
        posts.clear()
        super.clear()
    }

    fun indexOfFirstPost(postId: String): Int {
        return posts.indexOfFirst { it.id == postId }
    }

    fun indexOfFirstAnnouncementPost(postId: String): Int {
        return announcementPosts.indexOfFirst { it.id == postId }
    }

    /**
     * Remove và return FeedViewData hoặc null
     */
    fun removeAtNullable(index: Int): FeedViewData? {
        return if (index >= size) {
            null
        } else {
            val item = super.removeAt(index)
            return if (item is FeedPostViewData) {
                super.add(index, item)
                null
            } else {
                item
            }
        }
    }

    fun addAllPosts(postModels: List<PostModel>, data: List<List<FeedPostViewData>>): Boolean {
        posts.addAll(postModels)
        return if (posts.isEmpty()) false else super.addAll(data.flatten())
    }

    fun setAllPosts(postModels: List<PostModel>, data: List<List<FeedPostViewData>>): Boolean {
        clear()
        return addAllPosts(postModels, data)
    }

    fun setPosts(posts: List<PostModel>): Boolean {
        this.posts.clear()
        return this.posts.addAll(posts)
    }

    fun addAllPosts(data: List<List<FeedPostViewData>>): Boolean {
        return if (posts.isEmpty()) false else super.addAll(data.flatten())
    }

    /**
     * Add list comment của post
     * @param target có thể là comment của post hoặc comment của ảnh trong post
     */
    fun addAllComments(
        postId: String,
        commentModels: List<PostCommentModel>,
        target: PostCommentModel.Target
    ): PostModel? {
        val post = getPost(postId) ?: return null
        val parentCommentId = commentModels.firstOrNull()?.parentId
        val mediaId = target.mediaId
        post.addAllComment(commentModels, parentCommentId, mediaId)
        return post
    }

    fun addAllPreviousComments(
        postId: String,
        commentModels: List<PostCommentModel>,
        target: PostCommentModel.Target
    ): PostModel? {
        val post = getPost(postId) ?: return null
        // update comment
        post.comments.firstOrNull()?.isFirstComment = false
        val parentCommentId = commentModels.firstOrNull()?.parentId
        val mediaId = target.mediaId
        post.addAllPreviousComment(commentModels, parentCommentId, mediaId)
        return post
    }

    /**
     * Get Post theo postId
     * @param postId
     * @return [com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getPost(postId: String) = posts.find { it.id == postId }

    /**
     * Get ChildPost theo postId
     * @param postId
     * @return [com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getChildPost(postId: String) = posts.filter { it.type == PostModel.Type.COMBINE }.map {
        it.childPosts
    }.flatten().find { it.id == postId }

    /**
     * Get Post theo postId, nếu null thì tìm previewPost của post đầu tiên có previewPost.id = postId
     * @param postId
     * @return [com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getPostOrFirstSharedPost(postId: String) =
        posts.find { it.id == postId } ?: posts.find { it.previewPost?.id == postId }?.previewPost

    /**
     * Get Post theo postId, nếu null thì tìm previewPost của post đầu tiên có previewPost.id = postId, hoặc child Post trong combine post
     * @param postId
     * @return [com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getPostOrChildPostOrFirstSharedPost(postId: String) =
        posts.find { it.id == postId } ?: posts.find { it.previewPost?.id == postId }?.previewPost ?: getChildPost(postId)

    /**
     * Get tất cả Post có previewPost.id = postId
     * @param postId
     * @return List[com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getAllPostsHaveSharedPost(postId: String) = posts.filter { it.previewPost?.id == postId }

    /**
     * Get Shared Post theo parentPostId và postId
     * @param parentPostId
     * @param postId
     * @return [com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getSharedPost(parentPostId: String, postId: String) =
        posts.find { it.id == parentPostId && it.previewPost?.id == postId }?.previewPost

    /**
     * Get Post theo postId và tất cả Post có previewPost.id = postId
     * @param postId
     * @return List[com.gg.gapo.core.feed.post.domain.PostModel]
     */
    fun getPostAndAllPostsHaveSharedPost(postId: String) =
        posts.filter { it.id == postId || it.previewPost?.id == postId }

    fun getAllChildPost(postId: String) =
        posts.map { it.childPosts }.flatten().filter { it.id == postId }

    fun containsPost(postId: String) = getPost(postId) != null

    fun containsSharedPost(parentPostId: String, postId: String) =
        getSharedPost(parentPostId, postId) != null

    fun shouldUpdatePost(post: PostModel): Boolean {
        val currentPost = getPost(post.id) ?: getChildPost(post.id) ?: return false
        // set giống nhau vì chỉ quan tâm các fields content khác
        post.isAnnouncement = currentPost.isAnnouncement
        post.createdPinAt = currentPost.createdPinAt
        return currentPost != post
    }

    /**
     * Add Post theo index
     * Cần tìm index của ViewData để add UI
     */
    fun addPost(index: Int, postModel: PostModel, data: List<FeedViewData>) {
        if (index == NO_POSITION) return
        posts.add(index, postModel.shallowCopy())

        val nextPost = posts.getOrNull(index + 1)

        removePostShimmer()
        removeFeedEmpty()

        val indexOfViewData = if (nextPost != null) {
            val nextPostId = nextPost.id
            if (nextPost.type != PostModel.Type.COMBINE) indexOfFirst { it is FeedPostHeaderViewData && it.postId == nextPostId && it.parentPostId.isNullOrEmpty() }
            else indexOfFirst { it is FeedCombinePostHeaderViewData && it.postId == nextPostId }
        } else if (findReachedEnd() == null) {
            size
        } else {
            size - 1
        }

        if (indexOfViewData > NO_POSITION && super.addAll(indexOfViewData, data)) {
            setLiveData()
        }
    }

    fun updateChildPost(postModel: PostModel, data: List<FeedViewData>) {
        val postId = postModel.id
        val combinePost = getCombinePostByChildPost(postId)
        val indexOfPost = posts.indexOfFirst { it.id == combinePost?.id }
        if (combinePost != null && indexOfPost > NO_POSITION) {
            val childPosts = combinePost.childPosts
            val index = childPosts.indexOfFirst { it.id == postId }
            if (index > NO_POSITION) {
                childPosts[index] = postModel
            }
            posts[indexOfPost] = combinePost.copy(childPosts = childPosts).shallowCopy()
            val indexOfChildPostHeaderViewData = indexOfFirstPostHeader(postId)
            val indexOfChildPostFooterViewData = indexOfFirstChildPostFooter(postId)

            if (indexOfChildPostHeaderViewData > NO_POSITION && indexOfChildPostFooterViewData > NO_POSITION) {
                // Removes the range of elements from this list starting from fromIndex and ending with but not including toIndex.
                removeRange(indexOfChildPostHeaderViewData, indexOfChildPostFooterViewData + 1)
                if (super.addAll(indexOfChildPostHeaderViewData, data)) {
                    setLiveData()
                }
            }
        }
    }

    /**
     * Chỉ update post, không update shared post, không update post media vertical
     * Cần tối ưu lại để update tất cả các case
     */
    fun updatePost(postModel: PostModel, data: List<FeedViewData>) {
        val postId = postModel.id
        val indexOfPost = posts.indexOfFirst { it.id == postId }
        if (indexOfPost > NO_POSITION) {
            posts[indexOfPost] = postModel.shallowCopy()
            val indexOfPostHeaderViewData = indexOfFirstPostHeader(postId)
            val indexOfPostFooterViewData = indexOfFirstPostFooter(postId)

            if (indexOfPostHeaderViewData > NO_POSITION && indexOfPostFooterViewData > NO_POSITION) {
                // Removes the range of elements from this list starting from fromIndex and ending with but not including toIndex.
                removeRange(indexOfPostHeaderViewData, indexOfPostFooterViewData + 1)
                if (super.addAll(indexOfPostHeaderViewData, data)) {
                    setLiveData()
                }
            }
        }
    }

    fun removePost(postId: String): Boolean {
        // Xóa tất cả shared post
        val posts = getAllPostsHaveSharedPost(postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            newPost.previewPost = null
            FeedPostUpdatedBusEvent(newPost).postEvent()
        }
        val indexOfPost = this.posts.indexOfFirst { it.id == postId }
        return if (indexOfPost > NO_POSITION) {
            this.posts.removeAt(indexOfPost)
            val indexOfPostHeaderViewData = indexOfFirstPostHeader(postId)
            val indexOfPostFooterViewData = indexOfFirstPostFooter(postId)
            if (indexOfPostHeaderViewData > NO_POSITION && indexOfPostFooterViewData > NO_POSITION) {
                // Remove các item view trong khoảng indexOfPostHeaderViewData đến indexOfPostFooterViewData
                removeRange(indexOfPostHeaderViewData, indexOfPostFooterViewData + 1)
                true
            } else {
                false
            }
        } else {
            false
        }
    }

    fun updateCombinePost(postModel: PostModel, data: List<FeedPostViewData>) {
        val postId = postModel.id
        val indexOfPost = posts.indexOfFirst { it.id == postId }
        if (indexOfPost > NO_POSITION) {
            posts[indexOfPost] = postModel.shallowCopy()
            val indexOfPostHeaderViewData = indexOfFirstCombinePostHeader(postId)
            val indexOfPostFooterViewData = indexOfFirstCombinePostFooter(postId)

            if (indexOfPostHeaderViewData > NO_POSITION && indexOfPostFooterViewData > NO_POSITION) {
                // Removes the range of elements from this list starting from fromIndex and ending with but not including toIndex.
                // include viewmore (+2)
                removeRange(indexOfPostHeaderViewData, indexOfPostFooterViewData + 2)
                if (super.addAll(indexOfPostHeaderViewData, data)) {
                    setLiveData()
                }
            }
        }
    }

    fun getComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ): PostCommentModel? {
        val post = getPost(postId) ?: return null
        val mediaId = target.mediaId
        return post.getComment(commentId, parentCommentId, mediaId)
    }

    fun containsComment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: PostCommentModel.Target
    ) = getComment(postId, commentId, parentCommentId, target) != null

    /**
     * @param useShallowCopy tạm thời dùng cho case cập nhập comment ở post details
     * Ở Post details luôn trust vào list comments của current post để map
     * vì api post by id hay feed đều trả về tối đa 2 comments.
     */
    fun addOrSetComment(
        event: FeedCommentCreatedOrUpdatedBusEvent,
        useShallowCopy: Boolean = true
    ): PostModel? {
        val commentModel = event.commentModel as? PostCommentModel ?: return null
        val target = event.target as? PostCommentModel.Target ?: return null
        val mediaId = target.mediaId
        var postModel = getPost(commentModel.postId) ?: return null
        if (useShallowCopy) {
            postModel = postModel.shallowCopy()
        }
        postModel.addOrSetComment(commentModel, mediaId, event.changeCommentCount)
        return postModel
    }

    /**
     * @param useShallowCopy tạm thời dùng cho case cập nhập comment ở post details
     * Ở Post details luôn trust vào list comments của current post để map
     * vì api post by id hay feed đều trả về tối đa 2 comments.
     */
    fun removeComment(
        event: FeedCommentDeletedBusEvent,
        useShallowCopy: Boolean = true
    ): PostModel? {
        val target = event.target as? PostCommentModel.Target ?: return null
        val mediaId = target.mediaId
        val commentId = event.commentId
        val parentCommentId = event.parentCommentId
        var postModel = getPost(event.postId) ?: return null
        if (useShallowCopy) {
            postModel = postModel.shallowCopy()
        }
        postModel.removeComment(commentId, parentCommentId, mediaId, event.replyCount)
        return postModel
    }

    fun setAllAnnouncementPost(posts: List<PostModel>) {
        this.announcementPosts.clear()
        this.announcementPosts.addAll(posts)
    }

    fun getAnnouncementPost(postId: String) = announcementPosts.find { it.id == postId }

    fun containsAnnouncementPost(postId: String) = getAnnouncementPost(postId) != null

    fun shouldUpdateAnnouncementPost(post: PostModel): Boolean {
        val currentPost = getAnnouncementPost(post.id) ?: return false
        // set giống nhau vì chỉ quan tâm các fields content khác
        post.isAnnouncement = currentPost.isAnnouncement
        post.createdPinAt = currentPost.createdPinAt
        return currentPost != post
    }

    fun isChildPost(postId: String): Boolean {
        val childPosts = posts.map { it.childPosts }.filterNotNull().flatten().map { it.id }
        return childPosts.contains(postId)
    }

    fun getCombinePostByChildPost(childPostId: String): PostModel? {
        return posts.filter { it.type == PostModel.Type.COMBINE }
            .firstOrNull { it.childPosts.any { it.id == childPostId } }
    }

    fun updateAnnouncementPost(postModel: PostModel, data: List<FeedViewData>) {
        val announcementSummaryViewData =
            data.firstOrNull() as? FeedPostAnnouncementSummaryViewData ?: return
        val postId = postModel.id
        val indexOfPost = announcementPosts.indexOfFirst { it.id == postId }
        val list = findFeedAnnouncementPostsList()
        if (indexOfPost > NO_POSITION && list != null) {
            announcementPosts[indexOfPost] = postModel.shallowCopy()
            if (list.updatePost(announcementSummaryViewData)) {
                setLiveData()
            }
        }
    }

    fun addUploadingPost(item: FeedPostUploadingViewData, index: Int = NO_POSITION) {
        val indexToAdd = if (index == NO_POSITION) {
            val firstPostUploadingItem = findIndexOfFirstUploadingPost()
            if (firstPostUploadingItem == NO_POSITION) {
                findIndexByHeaderPostIndex(0)
            } else {
                firstPostUploadingItem
            }
        } else {
            index
        }

        if (indexToAdd == NO_POSITION) return
        add(indexToAdd, item)
        postLiveData()
    }

    fun addAllUploadingPosts(items: List<FeedPostUploadingViewData>) {
        val index = findIndexByHeaderPostIndex(0)
        if (index == NO_POSITION) return
        super.addAll(index, items)
        postLiveData()
    }

    fun findIndexByHeaderPostIndex(postIndex: Int): Int {
        val postId = posts.getOrNull(postIndex)?.id ?: return NO_POSITION
        return findIndexByHeaderPostId(postId)
    }

    fun findIndexByHeaderPostId(postId: String): Int {
        return indexOfFirstPostHeader(postId)
    }

    fun findIndexByFooterPostIndex(postIndex: Int): Int {
        val postId = posts.getOrNull(postIndex)?.id ?: return NO_POSITION
        return indexOfFirstPostFooter(postId)
    }

    fun findIndexByFooterPostId(postId: String): Int {
        return indexOfFirstPostFooter(postId)
    }

    fun findIndexOfFirstUploadingPost() = indexOfFirst { it is FeedPostUploadingViewData }

    fun collapseOrExpandPostText(itemId: String) {
        val item = getPostText(itemId) ?: return
        item.isFullTextShown = !item.isFullTextShown
    }

    fun collapseOrExpandPostQuestionText(itemId: String) {
        val item = getPostQuestion(itemId) ?: return
        item.isFullTextShown = !item.isFullTextShown
    }

    fun expandCommentText(itemId: String) {
        val item = getCommentText(itemId) ?: return
        item.isFullTextShown = true
    }

    fun collapseOrExpandGroupDetailsDescription() {
        val item = findGroupDetailsHeader() ?: return
        item.isFullDescriptionShown = !item.isFullDescriptionShown
    }

    fun expandPollVote(itemId: String) {
        val pollVote = findFeedPostPollVoteListByItemId(itemId) ?: return
        pollVote.allShown = true
    }

    fun expandAttachment(itemId: String) {
        val attachments = findFeedPostAttachmentsListByItemId(itemId) ?: return
        attachments.allShown = true
    }

    suspend fun onPostReactClickOnReact(
        reactPostUseCase: ReactPostUseCase,
        postId: String,
        mediaId: String?,
        reactType: Int
    ) {
        val post = getPost(postId)?.shallowCopy() ?: getChildPost(postId)?.shallowCopy() ?: return
        val oldReactType = if (mediaId.isNullOrEmpty()) {
            post.reactType
        } else {
            post.media.find { it.mediaId == mediaId }?.reactType ?: PostReactType.UNKNOWN
        }
        val newReactType = PostReactType.getByType(reactType) ?: PostReactType.UNKNOWN
        if (oldReactType == newReactType) return
        post.updateMyReact(mediaId, newReactType)
        FeedPostUpdatedBusEvent(post).postEvent()
        when (val result = reactPostUseCase(postId, mediaId, reactType)) {
            is Result.Success -> {
            }
            is Result.Error -> {
                val restoredPost = post.shallowCopy()
                restoredPost.updateMyReact(mediaId, oldReactType)
                FeedPostUpdatedBusEvent(restoredPost).postEvent()
                Timber.e(result.exception)
            }
        }
    }

    suspend fun onPostCommentClickOnReact(
        reactCommentUseCase: ReactCommentUseCase,
        postId: String,
        commentId: String,
        parentCommentId: String?,
        reactType: Int,
        target: PostCommentModel.Target
    ) {
        val comment =
            getComment(postId, commentId, parentCommentId, target)?.shallowCopy() ?: return
        val oldReactType = comment.reactType
        val newReactType = PostReactType.getByType(reactType) ?: PostReactType.UNKNOWN
        if (oldReactType == newReactType) return
        comment.updateMyReact(newReactType)
        FeedCommentCreatedOrUpdatedBusEvent(comment, target, false).postEvent()
        when (val result = reactCommentUseCase(commentId, reactType)) {
            is Result.Success -> {
            }
            is Result.Error -> {
                val restoredComment = comment.shallowCopy()
                restoredComment.updateMyReact(oldReactType)
                FeedCommentCreatedOrUpdatedBusEvent(restoredComment, target, false).postEvent()
                Timber.e(result.exception)
            }
        }
    }

    fun handlePostAttachmentDownloadingEvent(event: FeedPostAttachmentDownloadingBusEvent) {
        val postAttachments = getPostAttachmentsList(event.postId)
        var shouldUpdateUI = false
        for (postAttachment in postAttachments) {
            val attachment = postAttachment.attachments.find { it.fileId == event.fileId }
            if (attachment != null) {
                val state = attachment.state as? FeedPostAttachmentViewData.State.Downloading

                if (state == null || state.percentage == 0 || event.percentage - state.percentage >= 5) {
                    shouldUpdateUI = true

                    attachment.state =
                        FeedPostAttachmentViewData.State.Downloading(event.percentage)
                }
            }
        }
        if (shouldUpdateUI) {
            postLiveData()
        }
    }

    fun handlePostCommentAttachmentDownloadingEvent(
        event: FeedPostCommentAttachmentDownloadingBusEvent
    ) {
        val commentAttachment = getCommentAttachment(
            event.postId,
            event.commentId,
            event.parentCommentId,
            event.target as String
        )
        var shouldUpdateUI = false
        if (commentAttachment != null) {
            if (commentAttachment.media != null) {
                val state =
                    commentAttachment.media.state as? FeedPostCommentContentViewData.Media.State.Downloading

                if (state == null || state.percentage == 0 || event.percentage - state.percentage >= 5) {
                    shouldUpdateUI = true

                    commentAttachment.media.state =
                        FeedPostCommentContentViewData.Media.State.Downloading(event.percentage)
                }
            }
            if (shouldUpdateUI) {
                postLiveData()
            }
        }
    }

    fun handlePostCommentAttachmentDownloadedEvent(event: FeedPostCommentAttachmentDownloadedBusEvent) {
        val commentAttachment = getCommentAttachment(
            event.postId,
            event.commentId,
            event.parentCommentId,
            event.target as String
        )
        var shouldUpdateUI = false
        if (commentAttachment?.media != null) {
            shouldUpdateUI = true
            commentAttachment.media.state =
                FeedPostCommentContentViewData.Media.State.Exists(event.path)
        }
        if (shouldUpdateUI) {
            postLiveData()
        }
    }

    fun handlePostAttachmentDownloadedEvent(event: FeedPostAttachmentDownloadedBusEvent) {
        val postAttachments = getPostAttachmentsList(event.postId)
        var shouldUpdateUI = false
        for (postAttachment in postAttachments) {
            val attachment = postAttachment.attachments.find { it.fileId == event.fileId }
            if (attachment != null) {
                shouldUpdateUI = true
                attachment.state = FeedPostAttachmentViewData.State.Exists(event.path)
            }
        }
        if (shouldUpdateUI) {
            postLiveData()
        }
    }

    fun handlePostAttachmentDownloadCanceledEvent(event: FeedPostAttachmentDownloadCanceledBusEvent) {
        val postAttachments = getPostAttachmentsList(event.postId)
        var shouldUpdateUI = false
        for (postAttachment in postAttachments) {
            val attachment = postAttachment.attachments.find { it.fileId == event.fileId }
            if (attachment != null) {
                shouldUpdateUI = true
                attachment.state = FeedPostAttachmentViewData.State.DownloadAvailable
            }
        }
        if (shouldUpdateUI) {
            setLiveData()
        }
    }

    fun handleSendAddFriendRequestBusEvent(userId: String): Boolean {
        val suggestedFriendsList = findSuggestedFriendsList() ?: return false
        val suggestedFriend = suggestedFriendsList.suggestedFriends.find { it.userId == userId }
        if (suggestedFriend != null && suggestedFriend.state != FeedSuggestedFriendViewData.RelationState.SentRequest) {
            suggestedFriend.state = FeedSuggestedFriendViewData.RelationState.SentRequest
            setLiveData()
            return true
        }
        return false
    }

    fun handleCancelAddFriendRequestBusEvent(userId: String): Boolean {
        val suggestedFriendsList = findSuggestedFriendsList() ?: return false
        val suggestedFriend = suggestedFriendsList.suggestedFriends.find { it.userId == userId }
        if (suggestedFriend != null && suggestedFriend.state != FeedSuggestedFriendViewData.RelationState.Unknown) {
            suggestedFriend.state = FeedSuggestedFriendViewData.RelationState.Unknown
            setLiveData()
            return true
        }
        return false
    }

    fun handleRemovedSuggestedFriendBusEvent(userId: String): Boolean {
        val suggestedFriendsList = findSuggestedFriendsList() ?: return false
        return suggestedFriendsList.suggestedFriends.removeAll { it.userId == userId }.also {
            if (it) {
                setLiveData()
            }
        }
    }

    fun handleSendJoinGroupRequestBusEvent(
        groupId: String,
        isAutoAcceptedWhenJoin: Boolean
    ): Boolean {
        val suggestedGroupsList = findSuggestedGroupsList() ?: return false
        val suggestedGroup = suggestedGroupsList.suggestedGroups.find { it.groupId == groupId }
        if (suggestedGroup != null && suggestedGroup.state != FeedSuggestedGroupViewData.State.RequestedToJoin) {
            suggestedGroup.state = if (isAutoAcceptedWhenJoin) {
                FeedSuggestedGroupViewData.State.Joined
            } else {
                FeedSuggestedGroupViewData.State.RequestedToJoin
            }
            setLiveData()
            return true
        }
        return false
    }

    fun handleCancelJoinGroupRequestBusEvent(groupId: String): Boolean {
        val suggestedGroupsList = findSuggestedGroupsList() ?: return false
        val suggestedGroup = suggestedGroupsList.suggestedGroups.find { it.groupId == groupId }
        if (suggestedGroup != null && suggestedGroup.state == FeedSuggestedGroupViewData.State.RequestedToJoin) {
            suggestedGroup.state = FeedSuggestedGroupViewData.State.Guest
            setLiveData()
            return true
        }
        return false
    }

    fun handleRemovedSuggestedGroupBusEvent(groupId: String): Boolean {
        val suggestedGroupsList = findSuggestedGroupsList() ?: return false
        return suggestedGroupsList.suggestedGroups.removeAll { it.groupId == groupId }.also {
            if (it) {
                setLiveData()
            }
        }
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    fun handlePostLiveStreamFinishedBusEvent(postId: String): List<PostModel> {
        val returnPosts = mutableListOf<PostModel>()
        val posts = getPostAndAllPostsHaveSharedPost(postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.setLiveStreamFinished()) {
                returnPosts.add(newPost)
            }
        }
        return returnPosts
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    suspend fun onPostPollVoteClickOnVote(
        context: Context,
        voteUseCase: PostVoteUseCase,
        myUserProfile: PostUserModel,
        postId: String,
        voteId: String
    ) {
        val posts = getPostAndAllPostsHaveSharedPost(postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.vote(voteId, myUserProfile)) {
                FeedPostUpdatedBusEvent(newPost).postEvent()
            }
        }
        when (val result = voteUseCase(postId, voteId)) {
            is Result.Success -> {
            }
            is Result.Error -> {
                if (result.exception !is PostYouHaveAlreadyVotedException) {
                    val message = result.exception.message
                    if (!message.isNullOrEmpty()) {
                        GapoToast.makeNegative(
                            context,
                            result.exception.message.orEmpty(),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    posts.forEach { post ->
                        val restoredPost = post.shallowCopy()
                        val restoredResult = if (result.exception is PostPollExpiredException) {
                            restoredPost.setPollExpired()
                        } else {
                            restoredPost.unVote(voteId, myUserProfile)
                        }
                        if (restoredResult) {
                            FeedPostUpdatedBusEvent(restoredPost).postEvent()
                        }
                    }
                }
            }
        }
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    suspend fun onPostPollVoteClickOnUnVote(
        context: Context,
        unVoteUseCase: PostUnVoteUseCase,
        myUserProfile: PostUserModel,
        postId: String,
        voteId: String
    ) {
        val posts = getPostAndAllPostsHaveSharedPost(postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.unVote(voteId, myUserProfile)) {
                FeedPostUpdatedBusEvent(newPost).postEvent()
            }
        }
        when (val result = unVoteUseCase(postId, voteId)) {
            is Result.Success -> {
            }
            is Result.Error -> {
                if (result.exception !is PostYouHaveNotVotedYetException) {
                    val message = result.exception.message
                    if (!message.isNullOrEmpty()) {
                        GapoToast.makeNegative(
                            context,
                            result.exception.message.orEmpty(),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    posts.forEach { post ->
                        val restoredPost = post.shallowCopy()
                        val restoredResult = if (result.exception is PostPollExpiredException) {
                            restoredPost.setPollExpired()
                        } else {
                            restoredPost.vote(voteId, myUserProfile)
                        }
                        if (restoredResult) {
                            FeedPostUpdatedBusEvent(restoredPost).postEvent()
                        }
                    }
                }
            }
        }
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    fun handlePostPollNewVoteCreatedEvent(
        event: FeedPostPollVoteCreatedBusEvent
    ): List<PostModel> {
        val returnPosts = mutableListOf<PostModel>()
        val posts = getPostAndAllPostsHaveSharedPost(event.postId).toMutableList()
        val childPosts = getAllChildPost(event.postId)
        posts.addAll(childPosts)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.addVote(event.voteId, event.voteTitle, event.createdAt)) {
                returnPosts.add(newPost)
            }
        }
        return returnPosts
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    fun handlePostPollExpiredEvent(event: FeedPostPollExpiredBusEvent): List<PostModel> {
        val returnPosts = mutableListOf<PostModel>()
        val posts = getPostAndAllPostsHaveSharedPost(event.postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.setPollExpired()) {
                returnPosts.add(newPost)
            }
        }
        return returnPosts
    }

    /**
     * Chuyển announcement post lên đầu tiên chỉ trong khối Announcement
     */
    fun handlePostAnnouncementPushOnTopEvent(postId: String) {
        if (announcementPosts.isEmpty()) {
            val indexOfPost = indexOfFirstPost(postId)
            if (indexOfPost > NO_POSITION) {
                val post = posts.removeAt(indexOfPost)
                posts.add(0, post)
                val iterator = listIterator()
                val items = buildList {
                    while (iterator.hasNext()) {
                        val viewData = iterator.next()
                        if (viewData is FeedPostViewData && viewData.postId == postId) {
                            iterator.remove()
                            this.add(viewData)
                            if (viewData is FeedPostFooterViewData) {
                                break
                            }
                        }
                    }
                }
                if (items.isNotEmpty() && addAll(0, items)) {
                    setLiveData()
                }
            }
        } else {
            val indexOfAnnouncementPost = announcementPosts.indexOfFirst { it.id == postId }
            if (indexOfAnnouncementPost > NO_POSITION) {
                val post = announcementPosts.removeAt(indexOfAnnouncementPost)
                announcementPosts.add(0, post)
                val list = findFeedAnnouncementPostsList() ?: return
                if (list.pushPostOnTop(postId)) {
                    setLiveData()
                }
            }
        }
    }

    fun handlePostAnnouncementCreatedEvent(
        context: Context,
        index: Int,
        postModel: PostModel,
        announcementPostListInteractor: FeedAnnouncementPostListInteractor,
        postAnnouncementSummaryInteractor: FeedPostAnnouncementSummaryInteractor,
        announcementContext: FeedPostAnnouncementSummaryViewData.AnnouncementContext
    ) {
        if (!announcementPosts.contains(postModel)) {
            announcementPosts.add(0, postModel.shallowCopy())
            var list = findFeedAnnouncementPostsList()
            // nếu chưa có thì tạo mới
            if (list == null) {
                list = FeedAnnouncementPostsListViewData.create(
                    announcementContext,
                    announcementPostListInteractor
                )
                add(index, list)
            }
            val data = FeedPostAnnouncementSummaryViewData.createFromPost(
                context,
                postModel,
                postAnnouncementSummaryInteractor,
                announcementContext
            )
            list.addFirstPost(data)
            setLiveData()
        }
    }

    fun handlePostAnnouncementRemovedEvent(
        context: Context,
        index: Int,
        postId: String,
        interactor: FeedPostAnnouncementSummaryInteractor,
        announcementContext: FeedPostAnnouncementSummaryViewData.AnnouncementContext
    ) {
        val list = findFeedAnnouncementPostsList() ?: return
        if (announcementPosts.removeAll { it.id == postId } && list.posts.removeAll { it.postId == postId }) {
            if (announcementPosts.isEmpty() && list.posts.isEmpty()) {
                // sau khi removed mà empty thì xóa list
                removeAt(index)
            } else if (announcementPosts.size > list.posts.size) {
                // sau khi removed mà vẫn còn announcement post thì add thêm
                val postNeedAdd = announcementPosts.getOrNull(list.posts.size + 1)
                if (postNeedAdd != null) {
                    val data = FeedPostAnnouncementSummaryViewData.createFromPost(
                        context,
                        postNeedAdd,
                        interactor,
                        announcementContext
                    )
                    list.addLastPost(data)
                }
            }
            setLiveData()
        }
    }

    /**
     * Xử lý cho post.id = event.postId và tất cả post có sharedPost.id = event.postId
     */
    fun handlePostMediaDeletedEvent(event: FeedPostMediaDeletedBusEvent): List<PostModel> {
        val returnPosts = mutableListOf<PostModel>()
        val posts = getPostAndAllPostsHaveSharedPost(event.postId)
        posts.forEach { post ->
            val newPost = post.shallowCopy()
            if (newPost.removeMedia(event.mediaId)) {
                returnPosts.add(newPost)
            }
        }
        return returnPosts
    }

    /**
     * Post A share Post B hoặc một media của Post B thì sẽ tăng share count cho Post B hoặc media của Post B
     */
    fun handlePostSharedEvent(event: FeedPostSharedBusEvent): PostModel? {
        val post = getPost(event.postId)?.shallowCopy() ?: getChildPost(event.postId)?.shallowCopy() ?: return null
        if (!event.mediaId.isNullOrEmpty()) {
            post.media.find { it.mediaId == event.mediaId }?.counts?.increaseShareCount()
        } else {
            post.counts.increaseShareCount()
        }
        return post
    }

    fun removePostShimmer() = removeAll { it is FeedPostShimmerViewData }

    fun removePostUploading(localPostId: String) =
        removeAll { it is FeedPostUploadingViewData && it.localPostId == localPostId }

    fun removeFeedEmpty() = removeAll { it is FeedEmptyViewData }

    fun removeFindFriendViaContact() = removeAll { it is FeedFindFriendViaContactViewData }

    fun removeOnboardingsList() = removeAll { it is FeedOnboardingsListViewData }

    fun removeMyGroupEmpty() = removeAll { it is FeedGroupMyGroupEmptyViewData }

    fun findFindFriendViaContact() =
        find { it is FeedFindFriendViaContactViewData } as? FeedFindFriendViaContactViewData

    fun findFeedEmpty() = find { it is FeedEmptyViewData } as? FeedEmptyViewData

    fun findStoriesList() = find { it is FeedStoriesListViewData } as? FeedStoriesListViewData

    fun findSuggestedFriendsList() =
        find { it is FeedSuggestedFriendsListViewData } as? FeedSuggestedFriendsListViewData

    fun findSuggestedGroupsList() =
        find { it is FeedSuggestedGroupsListViewData } as? FeedSuggestedGroupsListViewData

    fun findMyGroupsList() =
        find { it is FeedGroupMyGroupsListViewData } as? FeedGroupMyGroupsListViewData

    fun findReachedEnd() = lastOrNull() as? FeedReachedEndViewData
        ?: find { it is FeedReachedEndViewData } as? FeedReachedEndViewData

    fun findAllUploadingPosts() = filterIsInstance(FeedPostUploadingViewData::class.java)

    fun findGroupMyGroupEmpty() =
        find { it is FeedGroupMyGroupEmptyViewData } as? FeedGroupMyGroupEmptyViewData

    fun findGroupDetailsHeader() =
        find { it is FeedGroupDetailsHeaderViewData } as? FeedGroupDetailsHeaderViewData

    fun findGroupDetailsPinnedPostsRow() =
        find { it is FeedGroupDetailsPinnedPostRowViewData } as? FeedGroupDetailsPinnedPostRowViewData

    fun removeGroupDetailsPinnedPostsRow() =
        removeAll { it is FeedGroupDetailsPinnedPostRowViewData }

    fun findUserProfileHeader() = find { it is FeedUserProfileViewData } as? FeedUserProfileViewData

    fun findUserProfilePhotoAlbumList() =
        find { it is FeedUserProfilePhotoAlbumListViewData } as? FeedUserProfilePhotoAlbumListViewData

    fun findUserProfileFriendsList() =
        find { it is FeedUserProfileFriendsListViewData } as? FeedUserProfileFriendsListViewData

    fun findUserProfileAchievementsList() =
        find { it is FeedUserProfileAchievementsListViewData } as? FeedUserProfileAchievementsListViewData

    fun findPlaceholder(itemId: String) =
        find { it is FeedPlaceholderViewData && it.itemId == itemId } as? FeedPlaceholderViewData

    fun indexOfPlaceholder(itemId: String) = indexOfFirst { findPlaceholder(itemId) != null }

    fun getOrCreateAnnouncementPostsList(
        announcementContext: FeedPostAnnouncementSummaryViewData.AnnouncementContext,
        interactor: FeedAnnouncementPostListInteractor
    ): FeedAnnouncementPostsListViewData {
        return findFeedAnnouncementPostsList() ?: FeedAnnouncementPostsListViewData.create(
            announcementContext,
            interactor
        )
    }

    fun findMediaList(mediaId: String) =
        find { item -> item is FeedPostMediaListViewData && item.media.any { media -> media.mediaId == mediaId } } as? FeedPostMediaListViewData

    fun indexOfMediaList(mediaId: String) =
        indexOfFirst { item -> item is FeedPostMediaListViewData && item.media.any { media -> media.mediaId == mediaId } }

    private fun findFeedAnnouncementPostsList(): FeedAnnouncementPostsListViewData? {
        return find { it is FeedAnnouncementPostsListViewData } as? FeedAnnouncementPostsListViewData
    }

    private fun getPostText(itemId: String) = find { it.itemId == itemId } as? FeedPostTextViewData

    private fun getPostQuestion(itemId: String) =
        find { it.itemId == itemId } as? FeedPostQuestionViewData

    private fun getCommentText(itemId: String) =
        find { it.itemId == itemId } as? FeedPostCommentContentViewData

    private fun indexOfFirstPostHeader(postId: String) =
        indexOfFirst { it is FeedPostHeaderViewData && it.postId == postId && it.parentPostId.isNullOrEmpty() }

    private fun indexOfFirstPostFooter(postId: String) =
        indexOfFirst { it.isFeedPostFooterViewData(postId) }

    private fun indexOfFirstChildPostFooter(postId: String) =
        indexOfFirst { it.isFeedChildPostFooterViewData(postId) }

    private fun getPostAttachmentsList(postId: String) =
        asSequence().filterIsInstance(FeedPostAttachmentsListViewData::class.java)
            .filter { it.postId == postId }

    private fun indexOfFirstCombinePostHeader(postId: String) =
        indexOfFirst { it is FeedCombinePostHeaderViewData && it.postId == postId }

    private fun indexOfFirstCombinePostFooter(postId: String) =
        indexOfFirst { it is FeedCombinePostViewMoreViewData && it.postId == postId }

    private fun getCommentAttachment(
        postId: String,
        commentId: String,
        parentCommentId: String?,
        target: String
    ) =
        find {
            it is FeedPostCommentContentViewData && it.postId == postId && it.commentId == commentId &&
                it.parentCommentId.orEmpty() == parentCommentId && it.target.type.type == target
        } as FeedPostCommentContentViewData?

    private fun findFeedPostPollVoteListByItemId(itemId: String) =
        find { it.itemId == itemId } as? FeedPostPollVoteListViewData

    private fun findFeedPostAttachmentsListByItemId(itemId: String) =
        find { it.itemId == itemId } as? FeedPostAttachmentsListViewData

    companion object {
        const val NO_POSITION = -1
    }
}
