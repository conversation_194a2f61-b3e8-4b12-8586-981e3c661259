package com.gg.gapo.core.feed.item.announcement.viewholder

import android.os.Bundle
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.core.view.isVisible
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.databinding.FeedPostAnnouncementSliderItemBinding
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.announcement.interactor.FeedAnnouncementPostListInteractor
import com.gg.gapo.core.feed.item.announcement.model.FeedPostAnnouncementSliderViewData
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import java.util.Locale

/**
 * <AUTHOR>
 * @since 19/01/2021
 */
internal class FeedPostAnnouncementSliderViewHolder(
    private val binding: FeedPostAnnouncementSliderItemBinding,
    private val feedImageLoader: FeedImageLoader
) : FeedViewHolder<FeedPostAnnouncementSliderItemBinding, FeedPostAnnouncementSliderViewData>(
    binding
) {

    override fun onBind(item: FeedPostAnnouncementSliderViewData, bundle: Bundle?) {
        binding.layoutRoot.setDebouncedClickListener {
            item.interactor.onPostClickOnView(item.postId)
        }
        binding.buttonMore.setDebouncedClickListener {
            item.interactor.onAnnouncementPostClickOnOptions(item.postId)
        }

        val defaultRes =
            if (Locale.getDefault().language == "vi") R.drawable.feed_announcement_post_banner_vi else R.drawable.feed_announcement_post_banner_en
        if (bundle == null) {
            binding.layoutInformation.isVisible =
                item.context !is FeedPostAnnouncementSliderViewData.AnnouncementContext.FeedAnnouncement
            setUser(item.user, item.interactor)
            loadMedia(item.media, defaultRes)
            setContent(item.title, item.description)
            setDate(item.date)
            setViewCount(item.seenCount)
            binding.layoutNewBadge.isVisible = item.isNewBadgeVisible
            binding.buttonMore.isVisible =
                item.context !is FeedPostAnnouncementSliderViewData.AnnouncementContext.FeedAnnouncement
        } else {
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.USER_CHANGED_EXTRA)) {
                setUser(item.user, item.interactor)
            }
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.MEDIA_CHANGED_EXTRA)) {
                loadMedia(item.media, defaultRes)
            }
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.CONTENT_CHANGED_EXTRA)) {
                setContent(item.title, item.description)
            }
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.DATE_CHANGED_EXTRA)) {
                setDate(item.date)
            }
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.SEEN_COUNT_CHANGED_EXTRA)) {
                setViewCount(item.seenCount)
            }
            if (bundle.containsKey(FeedPostAnnouncementSliderViewData.NEW_BADGE_VISIBLE_CHANGED_EXTRA)) {
                binding.layoutNewBadge.isVisible = item.isNewBadgeVisible
            }
        }
    }

    override fun onViewRecycled() {
        feedImageLoader.clear(binding.imageAvatar)
        feedImageLoader.clear(binding.imageMedia)
        binding.layoutRoot.removeOnClickListener()
        binding.buttonMore.removeOnClickListener()
    }

    private fun setUser(user: FeedPostAnnouncementSliderViewData.User, interactor: FeedAnnouncementPostListInteractor) {
        binding.textUserName.text = user.displayName
        binding.imageAvatar.loadCircle(
            feedImageLoader,
            user.avatar,
            user.avatarPlaceHolder
        )

        binding.textUserName.setDebouncedClickListener {
            binding.imageAvatar.performClick()
        }
        binding.imageAvatar.setDebouncedClickListener {
            interactor.onAnnouncementPostClickedOnAvatar(user.userId)
        }
    }

    private fun setContent(title: String?, description: String) {
        binding.textTitle.isVisible = !title.isNullOrEmpty()
        binding.textDescription.maxLines = if (title.isNullOrEmpty()) 2 else 1
        binding.textDescription.isAllCaps = true
        binding.textTitle.text = title
        binding.textDescription.text =
            if (title.isNullOrEmpty() && description.isEmpty()) GapoGlobalResources.getString(
                GapoStrings.feeds_post_announcement_post_has_not_description
            ) else description
    }

    private fun setDate(date: String) {
        binding.textDate.text = date
    }

    private fun setViewCount(viewCount: Long) {
        binding.textViewCount.text = viewCount.toString()
    }

    private fun loadMedia(
        media: FeedPostAnnouncementSliderViewData.Media?,
        @DrawableRes defaultRes: Int
    ) {
        when {
            media == null -> {
                binding.imageMedia.scaleType = ImageView.ScaleType.CENTER_CROP
                binding.imageMedia.setImageResource(defaultRes)
            }

            media.media.isNullOrEmpty() -> {
                binding.imageMedia.scaleType = ImageView.ScaleType.CENTER_CROP
                binding.imageMedia.setImageResource(defaultRes)
            }

            else -> {
                binding.imageMedia.scaleType = ImageView.ScaleType.CENTER_CROP
                feedImageLoader.load(media.media)
                    .centerCrop()
                    .dontAnimate()
                    .fallback(defaultRes)
                    .error(defaultRes)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .priority(Priority.IMMEDIATE)
                    .into(binding.imageMedia)
            }
        }
    }
}
