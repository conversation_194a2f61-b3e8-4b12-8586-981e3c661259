package com.gg.gapo.core.feed.usecase.post.domain.usecase

import com.gg.gapo.core.feed.usecase.post.domain.PostRepository
import com.gg.gapo.core.feed.usecase.post.domain.model.AnnouncementPostTarget
import com.gg.gapo.core.feed.usecase.post.exception.PostExceptionHandler
import com.gg.gapo.core.utilities.result.Result

/**
 * <AUTHOR>
 * @since 15/12/2021
 */
class RemoveAnnouncementPostUseCase(
    private val postRepository: PostRepository,
    private val postExceptionHandler: PostExceptionHandler
) {

    suspend operator fun invoke(postId: String, targets: List<AnnouncementPostTarget>): Result<String> {
        return try {
            val message = postRepository.removeAnnouncementPost(postId, targets.map { it.target })
            Result.Success(message)
        } catch (e: Exception) {
            Result.Error(postExceptionHandler(e))
        }
    }
}
