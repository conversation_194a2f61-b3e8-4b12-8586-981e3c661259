package com.gg.gapo.core.feed.item.group.mine.viewholder

import android.os.Bundle
import com.bumptech.glide.Priority
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.gg.gapo.core.feed.R
import com.gg.gapo.core.feed.databinding.FeedGroupMyGroupItemBinding
import com.gg.gapo.core.feed.item.FeedViewHolder
import com.gg.gapo.core.feed.item.group.mine.model.FeedGroupMyGroupViewData
import com.gg.gapo.core.feed.utils.FeedImageLoader
import com.gg.gapo.core.feed.utils.removeOnClickListener
import com.gg.gapo.core.utilities.view.setDebouncedClickListener

/**
 * <AUTHOR>
 * @since 15/04/2021
 */
internal class FeedGroupMyGroupViewHolder(
    private val binding: FeedGroupMyGroupItemBinding,
    private val feedImageLoader: FeedImageLoader
) : FeedViewHolder<FeedGroupMyGroupItemBinding, FeedGroupMyGroupViewData>(binding) {

    private val transforms = MultiTransformation(CenterCrop(), RoundedCorners(space8dp))

    override fun onBind(item: FeedGroupMyGroupViewData, bundle: Bundle?) {
        binding.layoutRoot.setDebouncedClickListener {
            item.interactor.onGroupClickOnView(item.groupId)
        }
        if (bundle == null) {
            setAvatar(item.cover)
            setName(item.name)
        } else {
            if (bundle.containsKey(FeedGroupMyGroupViewData.COVER_CHANGED_EXTRA)) {
                setAvatar(item.cover)
            }
            if (bundle.containsKey(FeedGroupMyGroupViewData.NAME_CHANGED_EXTRA)) {
                setName(item.name)
            }
        }
    }

    override fun onViewRecycled() {
        feedImageLoader.clear(binding.imageAvatar)
        binding.layoutRoot.removeOnClickListener()
    }

    private fun setAvatar(avatar: String?) {
        feedImageLoader.asBitmap()
            .load(avatar)
            .thumbnail(feedImageLoader.asBitmap().sizeMultiplier(0.25f))
            .fallback(R.drawable.feed_group_ic_default_group_avatar_square)
            .placeholder(R.drawable.feed_group_ic_default_group_avatar_square)
            .error(R.drawable.feed_group_ic_default_group_avatar_square)
            .transform(transforms)
            .dontAnimate()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .priority(Priority.IMMEDIATE)
            .into(binding.imageAvatar)
    }

    private fun setName(name: String) {
        binding.textName.text = name
    }
}
