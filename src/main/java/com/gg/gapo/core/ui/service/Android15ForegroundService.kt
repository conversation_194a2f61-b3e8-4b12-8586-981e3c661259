package com.gg.gapo.core.ui.service

import android.app.Service
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.annotation.RequiresApi
import timber.log.Timber

abstract class Android15ForegroundService : Service() {

    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    override fun onTimeout(startId: Int, fgsType: Int) {
        Timber.d("Foreground service timeout - startId: $startId, fgsType: $fgsType")

        when (fgsType) {
            ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE -> {
                handleSpecialUseTimeout(startId)
            }
            ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC -> {
                handleDataSyncTimeout(startId)
            }
            ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROCESSING -> {
                handleMediaProcessingTimeout(startId)
            }
            else -> {
                handleGenericTimeout(startId, fgsType)
            }
        }
    }

    protected open fun handleSpecialUseTimeout(startId: Int) {
        Timber.w("Special use foreground service timed out - stopping service")
        onSpecialUseTimeoutAction()
        stopSelf(startId)
    }

    /**
     * Handle timeout for DATA_SYNC foreground services.
     * This is called when a data sync service times out after the allowed duration.
     * * Default implementation stops the service. Override for custom behavior.
     */
    protected open fun handleDataSyncTimeout(startId: Int) {
        Timber.w("Data sync foreground service timed out - stopping service")
        onDataSyncTimeoutAction()
        stopSelf(startId)
    }

    /**
     * Handle timeout for MEDIA_PROCESSING foreground services.
     * This is called when a media processing service times out after the allowed duration.
     * * Default implementation stops the service. Override for custom behavior.
     */
    protected open fun handleMediaProcessingTimeout(startId: Int) {
        Timber.w("Media processing foreground service timed out - stopping service")
        onMediaProcessingTimeoutAction()
        stopSelf(startId)
    }

    /**
     * Handle timeout for other foreground service types.
     * * Default implementation stops the service. Override for custom behavior.
     */
    protected open fun handleGenericTimeout(startId: Int, fgsType: Int) {
        Timber.w("Foreground service type $fgsType timed out - stopping service")
        onGenericTimeoutAction(fgsType)
        stopSelf(startId)
    }

    protected open fun onSpecialUseTimeoutAction() {
    }

    protected open fun onDataSyncTimeoutAction() {
    }

    protected open fun onMediaProcessingTimeoutAction() {
    }

    protected open fun onGenericTimeoutAction(fgsType: Int) {
    }

    protected fun startForegroundCompat(
        notificationId: Int,
        notification: android.app.Notification,
        foregroundServiceType: Int = ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            // Android 14+ (API 34+) - Use service type
            startForeground(notificationId, notification, foregroundServiceType)
        } else {
            // Pre-Android 14 - Standard foreground service
            startForeground(notificationId, notification)
        }
    }

    /**
     * Helper method to check if the service is running on Android 15 or higher.
     */
    protected fun isAndroid15OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM
    }

    /**
     * Helper method to check if timeout handling is supported.
     */
    protected fun isTimeoutHandlingSupported(): Boolean {
        return isAndroid15OrHigher()
    }
}
