package com.gg.gapo.shared.poll.vote.creator.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.gg.gapo.shared.poll.vote.R
import com.gg.gapo.shared.poll.vote.creator.PollVoteCreatorViewModel
import com.gg.gapo.shared.poll.vote.databinding.PollVoteCreatorFooterItemBinding

/**
 * <AUTHOR>
 * @since 27/07/2021
 */
internal class PollVoteCreatorFooterAdapter(
    context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val pollVoteCreatorViewModel: PollVoteCreatorViewModel
) : RecyclerView.Adapter<PollVoteCreatorFooterAdapter.ViewHolder>() {

    private val layoutInflater = LayoutInflater.from(context)

    init {
        setHasStableIds(true)
    }

    override fun onCreateViewHolder(parent: <PERSON><PERSON><PERSON>, viewType: Int): ViewHolder {
        val binding = PollVoteCreatorFooterItemBinding.inflate(layoutInflater, parent, false)
        binding.lifecycleOwner = lifecycleOwner
        binding.pollVoteCreatorViewModel = pollVoteCreatorViewModel
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind()
    }

    override fun getItemCount(): Int = 1

    override fun getItemViewType(position: Int): Int {
        return R.layout.poll_vote_creator_footer_item
    }

    override fun getItemId(position: Int): Long {
        return "PollVoteCreatorFooterAdapter".hashCode().toLong()
    }

    class ViewHolder(
        private val binding: PollVoteCreatorFooterItemBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.executePendingBindings()
        }
    }
}
