package ja.burhanrashid52.photoeditor

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.opengl.GLException
import android.opengl.GLSurfaceView
import android.util.TypedValue
import android.view.View
import androidx.annotation.NonNull
import java.nio.IntBuffer
import javax.microedition.khronos.opengles.GL10
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

/**
 *
 *
 * Bitmap utility class to perform different transformation on bitmap
 *
 *
 * <AUTHOR>
 * @version 0.1.2
 * @since 5/21/2018
 */
internal object BitmapUtil {
    /**
     * Remove transparency in edited bitmap
     *
     * @param source edited image
     * @return bitmap without any transparency
     */
    fun removeTransparency(source: Bitmap): Bitmap {
        var firstX = 0
        var firstY = 0
        var lastX = source.width
        var lastY = source.height
        val pixels = IntArray(source.width * source.height)
        source.getPixels(pixels, 0, source.width, 0, 0, source.width, source.height)
        loop@ for (x in 0 until source.width) {
            for (y in 0 until source.height) {
                if (pixels[x + y * source.width] != Color.TRANSPARENT) {
                    firstX = x
                    break@loop
                }
            }
        }
        loop@ for (y in 0 until source.height) {
            for (x in firstX until source.height) {
                if (pixels[x + y * source.width] != Color.TRANSPARENT) {
                    firstY = y
                    break@loop
                }
            }
        }
        loop@ for (x in source.width - 1 downTo firstX) {
            for (y in source.height - 1 downTo firstY) {
                if (pixels[x + y * source.width] != Color.TRANSPARENT) {
                    lastX = x
                    break@loop
                }
            }
        }
        loop@ for (y in source.height - 1 downTo firstY) {
            for (x in source.width - 1 downTo firstX) {
                if (pixels[x + y * source.width] != Color.TRANSPARENT) {
                    lastY = y
                    break@loop
                }
            }
        }
        return Bitmap.createBitmap(source, firstX, firstY, lastX - firstX, lastY - firstY)
    }

    /**
     * Save filter bitmap from [ImageFilterView]
     *
     * @param glSurfaceView surface view on which is image is drawn
     * @param gl            open gl source to read pixels from [GLSurfaceView]
     * @return save bitmap
     * @throws OutOfMemoryError error when system is out of memory to load and save bitmap
     */
    @Throws(OutOfMemoryError::class)
    fun createBitmapFromGLSurface(glSurfaceView: GLSurfaceView, gl: GL10): Bitmap? {
        val x = 0
        val y = 0
        val w = glSurfaceView.width
        val h = glSurfaceView.height
        val bitmapBuffer = IntArray(w * h)
        val bitmapSource = IntArray(w * h)
        val intBuffer = IntBuffer.wrap(bitmapBuffer)
        intBuffer.position(0)

        try {
            gl.glReadPixels(x, y, w, h, GL10.GL_RGBA, GL10.GL_UNSIGNED_BYTE, intBuffer)
            var offset1: Int
            var offset2: Int
            for (i in 0 until h) {
                offset1 = i * w
                offset2 = (h - i - 1) * w
                for (j in 0 until w) {
                    val texturePixel = bitmapBuffer[offset1 + j]
                    val blue = texturePixel shr 16 and 0xff
                    val red = texturePixel shl 16 and 0x00ff0000
                    val pixel = texturePixel and -0xff0100 or red or blue
                    bitmapSource[offset2 + j] = pixel
                }
            }
        } catch (e: GLException) {
            return null
        }

        return Bitmap.createBitmap(bitmapSource, w, h, Bitmap.Config.RGB_565)
    }

    /**
     * Creates a bitmap from the supplied view.
     *
     * @param view The view to get the bitmap.
     *
     * @return The bitmap from the supplied drawable.
     */
    @NonNull
    fun createBitmapFromView(
        @NonNull view: PhotoEditorView,
        isFullScreen: Boolean = false,
        minY: Float = -1f,
        maxY: Float = -1f,
        minX: Float = -1f,
        maxX: Float = -1f
    ): Bitmap {
        val height = Resources.getSystem().displayMetrics.heightPixels
        val width = Resources.getSystem().displayMetrics.widthPixels

        val bitmap = Bitmap.createBitmap(
            width,
            height,
            Bitmap.Config.RGB_565
        )

        val canvas = Canvas(bitmap)
        val background = view.background

        background?.draw(canvas)
        view.draw(canvas)
        val targetView = if (view.frameView?.visibility == View.VISIBLE && view.getMatBiec()) {
            view.frameView!!
        } else {
            view.source!!
        }
        return if (isFullScreen) {
            createFullScreenBitmap(targetView, minY, minX, maxY, maxX, bitmap)
        } else {
            createCropBitmap(bitmap, targetView)
        }
    }

    private fun createCropBitmap(bitmap: Bitmap, view: View) = Bitmap.createBitmap(
        bitmap,
        view.x.toInt(),
        view.y.toInt(),
        view.width,
        view.height
    )

    private fun createFullScreenBitmap(view: View, minY: Float, minX: Float, maxY: Float, maxX: Float, bitmap: Bitmap): Bitmap {
        val tempMinY = max(getTempMinY(view, minY).toInt(), 0)
        val tempMinX = max(getTempMinX(view, minX).toInt(), 0)
        val tempMaxY = min(getTempMaxY(view, maxY).toInt(), bitmap.height)
        val tempMaxX = min(getTempMaxX(view, maxX).toInt(), bitmap.width)
        return Bitmap.createBitmap(
            bitmap,
            tempMinX,
            tempMinY,
            (tempMaxX - tempMinX),
            (tempMaxY - tempMinY)
        )
    }

    private fun getTempMinY(view: View, minY: Float) = if (view.y > minY && minY != -1f) minY else view.y

    private fun getTempMinX(view: View, minX: Float) = if (view.x > minX && minX != -1f) minX else view.x

    private fun getTempMaxY(view: View, maxY: Float) = if (view.y + view.height > maxY || maxY == -1f) view.y + view.height else maxY

    private fun getTempMaxX(view: View, maxX: Float) = if (view.x + view.width > maxX || maxX == -1f) view.x + view.width else maxX

    fun createBitmapFromQuestionView(view: View): Bitmap? {
        val bitmap = Bitmap.createBitmap(
            view.measuredWidth,
            view.measuredHeight,
            Bitmap.Config.RGB_565
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.TRANSPARENT)
        val background = view.background

        background?.let {
            background.draw(canvas)
        }

        view.draw(canvas)
        return bitmap
    }

    fun overlay(bmp1: Bitmap, bmp2: Bitmap): Bitmap {
        val bmOverlay = Bitmap.createBitmap(bmp1.width, bmp1.height, bmp1.config ?: Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bmOverlay)
        canvas.drawBitmap(bmp1, Matrix(), null)
        canvas.drawBitmap(bmp2, Matrix(), null)
        return bmOverlay
    }

    private fun convertDpToPixels(dp: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            Resources.getSystem().displayMetrics
        ).roundToInt()
    }
}
