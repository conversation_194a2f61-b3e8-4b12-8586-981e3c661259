<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.gg.gapo.feature.workspace">

    <application>
        <activity
            android:name=".presentation.creator.WorkspaceCreatorActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme" />
        <activity
            android:name=".presentation.member.edit.EditMemberActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".presentation.member.list.MemberActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme" />
        <activity
            android:name=".presentation.invitation.WorkspaceInvitationActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme" />
        <activity
            android:name=".presentation.invitation.link.join.InvitationLinkHandlerActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme" />
        <activity
            android:name=".presentation.invitation.link.generate.WorkspaceInvitationLinkActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Workspace.Theme" />
        <activity
            android:name=".presentation.organization.department.DepartmentTransparentActivity"
            android:theme="@style/GapoTheme.Translucent.Floating" />
        <activity
            android:name=".presentation.organization.role.RoleTransparentActivity"
            android:theme="@style/GapoTheme.Translucent.Floating" />
        <activity
            android:name=".presentation.member.add.AddMemberProxyActivity"
            android:theme="@style/GapoTheme.Translucent.Floating" />
        <activity
            android:name=".presentation.confirm.join.ConfirmJoinWorkspaceProxyActivity"
            android:theme="@style/GapoTheme.Translucent.Floating" />
        <activity
            android:name=".presentation.switchws.SwitchWorkspaceProxyActivity"
            android:launchMode="singleTop"
            android:theme="@style/GapoTheme.Translucent.Floating" />
    </application>
</manifest>